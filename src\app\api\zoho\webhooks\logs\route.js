/**
 * GET /api/zoho/webhooks/logs
 * View recent webhook activity for debugging and monitoring
 */

import connectedDB from '@/app/config/database'
import WebhookEvent from '@/app/models/WebhookEvent'

export async function GET(request) {
  try {
    await connectedDB()

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit')) || 50
    const eventType = searchParams.get('event_type')
    const status = searchParams.get('status') // 'success', 'failed', 'all'
    const hours = parseInt(searchParams.get('hours')) || 24

    // Build query filter
    const filter = {}
    
    // Filter by time range
    const timeFilter = new Date()
    timeFilter.setHours(timeFilter.getHours() - hours)
    filter.processed_at = { $gte: timeFilter }

    // Filter by event type
    if (eventType && eventType !== 'all') {
      filter.event_type = eventType
    }

    // Filter by processing status
    if (status === 'success') {
      filter.processed = true
      filter.error_message = { $exists: false }
    } else if (status === 'failed') {
      filter.$or = [
        { processed: false },
        { error_message: { $exists: true, $ne: null } }
      ]
    }

    // Fetch webhook events
    const webhookEvents = await WebhookEvent.find(filter)
      .sort({ processed_at: -1 })
      .limit(Math.min(limit, 100)) // Cap at 100 for performance
      .lean()

    // Calculate statistics
    const totalEvents = await WebhookEvent.countDocuments(filter)
    const successfulEvents = await WebhookEvent.countDocuments({
      ...filter,
      processed: true,
      error_message: { $exists: false }
    })
    const failedEvents = await WebhookEvent.countDocuments({
      ...filter,
      $or: [
        { processed: false },
        { error_message: { $exists: true, $ne: null } }
      ]
    })

    // Group by event type for summary
    const eventTypeSummary = await WebhookEvent.aggregate([
      { $match: filter },
      {
        $group: {
          _id: '$event_type',
          count: { $sum: 1 },
          successful: {
            $sum: {
              $cond: [
                { $and: [{ $eq: ['$processed', true] }, { $not: { $ifNull: ['$error_message', false] } }] },
                1,
                0
              ]
            }
          },
          failed: {
            $sum: {
              $cond: [
                { $or: [{ $eq: ['$processed', false] }, { $ifNull: ['$error_message', false] }] },
                1,
                0
              ]
            }
          },
          avgProcessingTime: { $avg: '$processing_time_ms' }
        }
      },
      { $sort: { count: -1 } }
    ])

    // Format response
    const response = {
      summary: {
        timeRange: `Last ${hours} hours`,
        totalEvents,
        successfulEvents,
        failedEvents,
        successRate: totalEvents > 0 ? ((successfulEvents / totalEvents) * 100).toFixed(2) + '%' : '0%',
        eventsReturned: webhookEvents.length
      },
      eventTypeSummary,
      recentEvents: webhookEvents.map(event => ({
        id: event._id,
        eventId: event.event_id,
        eventType: event.event_type,
        paymentId: event.payment_id,
        paymentSessionId: event.payment_session_id,
        paymentLinkId: event.payment_link_id,
        amount: event.amount,
        currency: event.currency,
        customerEmail: event.customer_email,
        processed: event.processed,
        processingTime: event.processing_time_ms,
        responseStatus: event.response_status,
        clientIP: event.client_ip,
        signatureVerified: event.signature_verified,
        errorMessage: event.error_message,
        processedAt: event.processed_at,
        transactionId: event.transaction_id
      })),
      filters: {
        limit,
        eventType: eventType || 'all',
        status: status || 'all',
        hours
      }
    }

    return new Response(JSON.stringify(response, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Content-Type'
      }
    })

  } catch (error) {
    console.error('Error fetching webhook logs:', error.message)
    
    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        message: 'Failed to fetch webhook logs',
        details: error.message
      }),
      { status: 500 }
    )
  }
}

/**
 * OPTIONS /api/zoho/webhooks/logs
 * Handle CORS preflight requests
 */
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type'
    }
  })
}
