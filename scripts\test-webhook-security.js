#!/usr/bin/env node

/**
 * Webhook Security Test Script
 * 
 * Tests the enhanced security features of the Zoho Payment webhook endpoint
 * Run with: node scripts/test-webhook-security.js
 */

import crypto from 'crypto'
import fetch from 'node-fetch'

// Configuration
const WEBHOOK_URL = process.env.WEBHOOK_TEST_URL || 'http://localhost:3000/api/zoho/webhooks/payment'
const WEBHOOK_SECRET = process.env.ZOHO_WEBHOOK_SECRET || 'test_secret_key'
const TEST_IP = '127.0.0.1'

// Test webhook payload
const createTestPayload = (overrides = {}) => ({
  event_type: 'payment.succeeded',
  payment_session_id: 'test_session_123',
  payment_id: 'pay_test_456',
  status: 'succeeded',
  amount: 1000,
  currency: 'INR',
  payment_method: 'card',
  created_time: Math.floor(Date.now() / 1000),
  ...overrides
})

// Generate HMAC signature
const generateSignature = (payload, secret) => {
  const body = JSON.stringify(payload)
  return crypto.createHmac('sha256', secret).update(body).digest('hex')
}

// Test cases
const tests = [
  {
    name: 'Valid Webhook Request',
    description: 'Should succeed with valid signature and recent timestamp',
    test: async () => {
      const payload = createTestPayload()
      const signature = generateSignature(payload, WEBHOOK_SECRET)
      
      const response = await fetch(WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-zoho-webhook-signature': signature,
          'x-forwarded-for': TEST_IP
        },
        body: JSON.stringify(payload)
      })
      
      const result = await response.json()
      
      return {
        success: response.ok,
        status: response.status,
        result,
        expected: 'Should return 200 with success message'
      }
    }
  },
  
  {
    name: 'Invalid Signature Test',
    description: 'Should reject webhook with invalid signature',
    test: async () => {
      const payload = createTestPayload()
      const invalidSignature = 'invalid_signature_here'
      
      const response = await fetch(WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-zoho-webhook-signature': invalidSignature,
          'x-forwarded-for': TEST_IP
        },
        body: JSON.stringify(payload)
      })
      
      const result = await response.json()
      
      return {
        success: response.status === 401,
        status: response.status,
        result,
        expected: 'Should return 401 with signature verification error'
      }
    }
  },
  
  {
    name: 'Missing Signature Test',
    description: 'Should reject webhook without signature',
    test: async () => {
      const payload = createTestPayload()
      
      const response = await fetch(WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-forwarded-for': TEST_IP
        },
        body: JSON.stringify(payload)
      })
      
      const result = await response.json()
      
      return {
        success: response.status === 401,
        status: response.status,
        result,
        expected: 'Should return 401 with missing signature error'
      }
    }
  },
  
  {
    name: 'Old Timestamp Test (Replay Attack)',
    description: 'Should reject webhook with old timestamp',
    test: async () => {
      // Create payload with timestamp 10 minutes ago
      const oldTimestamp = Math.floor((Date.now() - 10 * 60 * 1000) / 1000)
      const payload = createTestPayload({ created_time: oldTimestamp })
      const signature = generateSignature(payload, WEBHOOK_SECRET)
      
      const response = await fetch(WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-zoho-webhook-signature': signature,
          'x-forwarded-for': TEST_IP
        },
        body: JSON.stringify(payload)
      })
      
      const result = await response.json()
      
      return {
        success: response.status === 401,
        status: response.status,
        result,
        expected: 'Should return 401 with timestamp validation error'
      }
    }
  },
  
  {
    name: 'Duplicate Event Test (Idempotency)',
    description: 'Should handle duplicate webhook gracefully',
    test: async () => {
      const payload = createTestPayload({
        payment_session_id: 'duplicate_test_session',
        created_time: Math.floor(Date.now() / 1000)
      })
      const signature = generateSignature(payload, WEBHOOK_SECRET)
      
      const headers = {
        'Content-Type': 'application/json',
        'x-zoho-webhook-signature': signature,
        'x-forwarded-for': TEST_IP
      }
      
      // Send first request
      const firstResponse = await fetch(WEBHOOK_URL, {
        method: 'POST',
        headers,
        body: JSON.stringify(payload)
      })
      
      // Send duplicate request
      const secondResponse = await fetch(WEBHOOK_URL, {
        method: 'POST',
        headers,
        body: JSON.stringify(payload)
      })
      
      const firstResult = await firstResponse.json()
      const secondResult = await secondResponse.json()
      
      return {
        success: secondResponse.ok && secondResult.message?.includes('already processed'),
        status: secondResponse.status,
        result: { first: firstResult, second: secondResult },
        expected: 'Second request should return success with "already processed" message'
      }
    }
  },
  
  {
    name: 'Invalid JSON Test',
    description: 'Should reject malformed JSON payload',
    test: async () => {
      const invalidJson = '{"invalid": json payload}'
      const signature = crypto.createHmac('sha256', WEBHOOK_SECRET).update(invalidJson).digest('hex')
      
      const response = await fetch(WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-zoho-webhook-signature': signature,
          'x-forwarded-for': TEST_IP
        },
        body: invalidJson
      })
      
      const result = await response.json()
      
      return {
        success: response.status === 400,
        status: response.status,
        result,
        expected: 'Should return 400 with JSON parsing error'
      }
    }
  },
  
  {
    name: 'Missing Required Fields Test',
    description: 'Should reject webhook missing required fields',
    test: async () => {
      const payload = { invalid: 'payload' } // Missing required fields
      const signature = generateSignature(payload, WEBHOOK_SECRET)
      
      const response = await fetch(WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-zoho-webhook-signature': signature,
          'x-forwarded-for': TEST_IP
        },
        body: JSON.stringify(payload)
      })
      
      const result = await response.json()
      
      return {
        success: response.status === 400,
        status: response.status,
        result,
        expected: 'Should return 400 with validation error'
      }
    }
  }
]

// Run tests
async function runTests() {
  console.log('🔒 Zoho Payment Webhook Security Tests')
  console.log('=====================================')
  console.log(`Testing endpoint: ${WEBHOOK_URL}`)
  console.log(`Using secret: ${WEBHOOK_SECRET.substring(0, 8)}...`)
  console.log('')
  
  let passed = 0
  let failed = 0
  
  for (const test of tests) {
    console.log(`🧪 ${test.name}`)
    console.log(`   ${test.description}`)
    
    try {
      const result = await test.test()
      
      if (result.success) {
        console.log(`   ✅ PASSED (${result.status})`)
        passed++
      } else {
        console.log(`   ❌ FAILED (${result.status})`)
        console.log(`   Expected: ${result.expected}`)
        console.log(`   Got: ${JSON.stringify(result.result, null, 2)}`)
        failed++
      }
    } catch (error) {
      console.log(`   💥 ERROR: ${error.message}`)
      failed++
    }
    
    console.log('')
  }
  
  console.log('📊 Test Results')
  console.log('===============')
  console.log(`✅ Passed: ${passed}`)
  console.log(`❌ Failed: ${failed}`)
  console.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`)
  
  if (failed > 0) {
    console.log('')
    console.log('⚠️  Some tests failed. Check the webhook implementation and configuration.')
    process.exit(1)
  } else {
    console.log('')
    console.log('🎉 All tests passed! Webhook security is working correctly.')
    process.exit(0)
  }
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log('Webhook Security Test Script')
  console.log('')
  console.log('Usage: node scripts/test-webhook-security.js [options]')
  console.log('')
  console.log('Environment Variables:')
  console.log('  WEBHOOK_TEST_URL    - Webhook endpoint URL (default: http://localhost:3000/api/zoho/webhooks/payment)')
  console.log('  ZOHO_WEBHOOK_SECRET - Webhook secret for signature generation (default: test_secret_key)')
  console.log('')
  console.log('Options:')
  console.log('  --help, -h          - Show this help message')
  console.log('')
  process.exit(0)
}

// Run the tests
runTests().catch(error => {
  console.error('💥 Test runner failed:', error.message)
  process.exit(1)
})
