/**
 * Production Enhancements Validation Test Suite
 * 
 * Comprehensive testing for rate limiting, timeout handling, and monitoring
 */

const fetch = globalThis.fetch || require('node-fetch')

const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000'

class ProductionEnhancementsValidator {
  constructor() {
    this.results = {
      summary: {
        total_tests: 0,
        passed: 0,
        failed: 0,
        start_time: new Date()
      },
      tests: []
    }
  }

  async runTest(testName, testFunction) {
    console.log(`\n🧪 Running: ${testName}`)
    this.results.summary.total_tests++
    
    const startTime = Date.now()
    let result = { success: false, error: null, data: null }
    
    try {
      result = await testFunction()
      if (result.success) {
        console.log(`✅ ${testName}: PASSED`)
        this.results.summary.passed++
      } else {
        console.log(`❌ ${testName}: FAILED - ${result.error}`)
        this.results.summary.failed++
      }
    } catch (error) {
      result = { success: false, error: error.message }
      console.log(`❌ ${testName}: ERROR - ${error.message}`)
      this.results.summary.failed++
    }
    
    this.results.tests.push({
      name: testName,
      success: result.success,
      error: result.error,
      data: result.data,
      duration: Date.now() - startTime
    })
    
    return result
  }

  // Test 1: Rate Limiting Functionality
  async testRateLimiting() {
    return this.runTest('Rate Limiting - Basic Functionality', async () => {
      const requests = []
      
      // Make multiple rapid requests to trigger rate limiting
      for (let i = 0; i < 10; i++) {
        requests.push(
          fetch(`${BASE_URL}/api/zoho/health`, {
            method: 'GET'
          })
        )
      }
      
      const responses = await Promise.all(requests)
      
      // Check for rate limit headers
      const firstResponse = responses[0]
      const hasRateLimitHeaders = firstResponse.headers.get('x-ratelimit-limit') !== null
      
      if (!hasRateLimitHeaders) {
        return { success: false, error: 'Rate limit headers not found' }
      }
      
      return {
        success: true,
        data: {
          responses: responses.length,
          rateLimitHeaders: {
            limit: firstResponse.headers.get('x-ratelimit-limit'),
            remaining: firstResponse.headers.get('x-ratelimit-remaining'),
            reset: firstResponse.headers.get('x-ratelimit-reset')
          }
        }
      }
    })
  }

  // Test 2: Rate Limiting Enforcement
  async testRateLimitingEnforcement() {
    return this.runTest('Rate Limiting - Enforcement', async () => {
      const requests = []
      
      // Make many requests to exceed rate limit
      for (let i = 0; i < 150; i++) {
        requests.push(
          fetch(`${BASE_URL}/api/zoho/health`, {
            method: 'GET'
          }).then(r => ({ status: r.status, headers: Object.fromEntries(r.headers.entries()) }))
        )
      }
      
      const responses = await Promise.all(requests)
      const rateLimitedResponses = responses.filter(r => r.status === 429)
      
      if (rateLimitedResponses.length === 0) {
        return { success: false, error: 'No rate limiting enforcement detected' }
      }
      
      // Check for proper 429 response format
      const rateLimitedResponse = await fetch(`${BASE_URL}/api/zoho/health`)
      if (rateLimitedResponse.status === 429) {
        const body = await rateLimitedResponse.json()
        if (!body.code || body.code !== 'RATE_LIMIT_EXCEEDED') {
          return { success: false, error: 'Invalid rate limit response format' }
        }
      }
      
      return {
        success: true,
        data: {
          totalRequests: responses.length,
          rateLimitedCount: rateLimitedResponses.length,
          rateLimitedPercentage: (rateLimitedResponses.length / responses.length * 100).toFixed(2)
        }
      }
    })
  }

  // Test 3: Enhanced Health Check
  async testEnhancedHealthCheck() {
    return this.runTest('Enhanced Health Check', async () => {
      const response = await fetch(`${BASE_URL}/api/zoho/health`)
      
      if (!response.ok) {
        return { success: false, error: `Health check failed with status ${response.status}` }
      }
      
      const healthData = await response.json()
      
      // Verify enhanced structure
      const requiredFields = ['metrics', 'performance', 'configuration']
      const missingFields = requiredFields.filter(field => !healthData[field])
      
      if (missingFields.length > 0) {
        return { success: false, error: `Missing fields: ${missingFields.join(', ')}` }
      }
      
      // Verify rate limiting status
      if (!healthData.checks.rateLimiting) {
        return { success: false, error: 'Rate limiting status not found in health check' }
      }
      
      return {
        success: true,
        data: {
          status: healthData.status,
          checksCount: Object.keys(healthData.checks).length,
          hasMetrics: !!healthData.metrics,
          hasPerformance: !!healthData.performance,
          rateLimitingEnabled: healthData.configuration.rateLimitEnabled
        }
      }
    })
  }

  // Test 4: Webhook Rate Limiting
  async testWebhookRateLimiting() {
    return this.runTest('Webhook Rate Limiting', async () => {
      const webhookPayload = {
        event_type: 'payment.succeeded',
        payment_session_id: 'test_session_123',
        payment_id: 'test_payment_123',
        status: 'succeeded',
        amount: 100,
        currency: 'INR'
      }
      
      const requests = []
      
      // Make multiple webhook requests
      for (let i = 0; i < 20; i++) {
        requests.push(
          fetch(`${BASE_URL}/api/zoho/webhooks/payment`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(webhookPayload)
          }).then(r => ({ status: r.status, headers: Object.fromEntries(r.headers.entries()) }))
        )
      }
      
      const responses = await Promise.all(requests)
      
      // Check for rate limit headers on webhook endpoint
      const hasRateLimitHeaders = responses.some(r => r.headers['x-ratelimit-limit'])
      
      if (!hasRateLimitHeaders) {
        return { success: false, error: 'Webhook rate limiting not applied' }
      }
      
      return {
        success: true,
        data: {
          totalRequests: responses.length,
          successfulResponses: responses.filter(r => r.status === 200).length,
          rateLimitedResponses: responses.filter(r => r.status === 429).length
        }
      }
    })
  }

  // Test 5: Monitoring and Metrics
  async testMonitoringMetrics() {
    return this.runTest('Monitoring and Metrics', async () => {
      // Make some requests to generate metrics
      await fetch(`${BASE_URL}/api/zoho/health`)
      await fetch(`${BASE_URL}/api/zoho/health`)
      
      // Check health endpoint for metrics
      const response = await fetch(`${BASE_URL}/api/zoho/health`)
      const healthData = await response.json()
      
      // Verify metrics structure
      if (!healthData.metrics || !healthData.performance) {
        return { success: false, error: 'Metrics or performance data missing' }
      }
      
      // Check for real-time metrics
      const hasRealtimeMetrics = healthData.metrics.realtime && 
                                Object.keys(healthData.metrics.realtime).length > 0
      
      return {
        success: true,
        data: {
          hasMetrics: !!healthData.metrics,
          hasPerformance: !!healthData.performance,
          hasRealtimeMetrics,
          performanceFields: Object.keys(healthData.performance),
          metricsFields: Object.keys(healthData.metrics)
        }
      }
    })
  }

  // Test 6: Backward Compatibility
  async testBackwardCompatibility() {
    return this.runTest('Backward Compatibility', async () => {
      // Test that existing endpoints still work
      const endpoints = [
        { path: '/api/zoho/health', method: 'GET' },
        { path: '/api/zoho/webhooks/payment', method: 'GET' }
      ]
      
      const results = []
      
      for (const endpoint of endpoints) {
        const response = await fetch(`${BASE_URL}${endpoint.path}`, {
          method: endpoint.method
        })
        
        results.push({
          endpoint: endpoint.path,
          method: endpoint.method,
          status: response.status,
          success: response.ok
        })
      }
      
      const failedEndpoints = results.filter(r => !r.success)
      
      if (failedEndpoints.length > 0) {
        return { 
          success: false, 
          error: `Failed endpoints: ${failedEndpoints.map(e => e.endpoint).join(', ')}` 
        }
      }
      
      return {
        success: true,
        data: {
          testedEndpoints: results.length,
          allSuccessful: true,
          results
        }
      }
    })
  }

  // Test 7: Configuration Validation
  async testConfigurationValidation() {
    return this.runTest('Configuration Validation', async () => {
      const response = await fetch(`${BASE_URL}/api/zoho/health`)
      const healthData = await response.json()
      
      const config = healthData.configuration
      if (!config) {
        return { success: false, error: 'Configuration not found in health check' }
      }
      
      // Check for new configuration fields
      const expectedFields = ['rateLimitEnabled', 'apiTimeout', 'maxRetries']
      const missingFields = expectedFields.filter(field => config[field] === undefined)
      
      if (missingFields.length > 0) {
        return { success: false, error: `Missing configuration fields: ${missingFields.join(', ')}` }
      }
      
      return {
        success: true,
        data: {
          configuration: config,
          hasAllFields: missingFields.length === 0
        }
      }
    })
  }

  async runAllTests() {
    console.log('🚀 Starting Production Enhancements Validation Tests\n')
    
    await this.testRateLimiting()
    await this.testRateLimitingEnforcement()
    await this.testEnhancedHealthCheck()
    await this.testWebhookRateLimiting()
    await this.testMonitoringMetrics()
    await this.testBackwardCompatibility()
    await this.testConfigurationValidation()
    
    this.results.summary.end_time = new Date()
    this.results.summary.duration = this.results.summary.end_time - this.results.summary.start_time
    
    console.log('\n📊 Test Results Summary:')
    console.log(`Total Tests: ${this.results.summary.total_tests}`)
    console.log(`Passed: ${this.results.summary.passed}`)
    console.log(`Failed: ${this.results.summary.failed}`)
    console.log(`Success Rate: ${(this.results.summary.passed / this.results.summary.total_tests * 100).toFixed(2)}%`)
    console.log(`Duration: ${this.results.summary.duration}ms`)
    
    if (this.results.summary.failed > 0) {
      console.log('\n❌ Failed Tests:')
      this.results.tests
        .filter(test => !test.success)
        .forEach(test => console.log(`  - ${test.name}: ${test.error}`))
    }
    
    return this.results
  }
}

// Run tests if called directly
if (require.main === module) {
  const validator = new ProductionEnhancementsValidator()
  validator.runAllTests()
    .then(results => {
      process.exit(results.summary.failed > 0 ? 1 : 0)
    })
    .catch(error => {
      console.error('Test suite failed:', error)
      process.exit(1)
    })
}

module.exports = ProductionEnhancementsValidator
