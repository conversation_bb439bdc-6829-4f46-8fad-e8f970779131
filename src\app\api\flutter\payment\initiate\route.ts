import { NextRequest, NextResponse } from 'next/server'
import zohoPaymentService from '@/app/lib/zohoPaymentService'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    const {
      amount,
      invoiceNo,
      customerId,
      customerName,
      customerEmail,
      customerPhone,
      description,
      currency = 'INR',
      callbackUrl,
      businessName,
      referenceNumber,
      metadata = {}
    } = body

    // Validation
    if (!amount || !invoiceNo || !customerId) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Missing required fields', 
          message: 'amount, invoiceNo, and customerId are required',
          required_fields: ['amount', 'invoiceNo', 'customerId']
        },
        { status: 400 }
      )
    }

    // Validate amount
    const parsedAmount = parseFloat(amount)
    if (isNaN(parsedAmount) || parsedAmount <= 0) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid amount', 
          message: 'Amount must be a positive number' 
        },
        { status: 400 }
      )
    }

    console.log(`[FLUTTER] Initiating payment for invoice: ${invoiceNo}, amount: ${parsedAmount}`)

    // Filter out keys that will be automatically added by the payment service
    const reservedKeys = ['customer_id', 'invoice_number']
    const filteredMetadata = Object.entries(metadata)
      .filter(([key]) => !reservedKeys.includes(key))
      .map(([key, value]) => ({ key, value: String(value) }))

    const paymentData = {
      amount: parsedAmount,
      currency,
      description: description || `Payment for Invoice ${invoiceNo}`,
      invoice_number: invoiceNo,
      customer_id: customerId,
      customer_name: customerName,
      customer_email: customerEmail,
      customer_phone: customerPhone,
      redirect_url: callbackUrl,
      meta_data: [
        { key: 'source', value: 'flutter_app' },
        { key: 'invoice_no', value: invoiceNo },
        ...filteredMetadata
      ]
    }

    // Create payment session using your existing service
    const result = await zohoPaymentService.createPaymentSession(paymentData)
    
    // Generate the WebView URL for Flutter with all required parameters for official SDK
    const baseUrl = process.env.NEXT_PUBLIC_DOMAIN || 'http://localhost:3001'
    const paymentPageUrl = new URL('/api/payment-page', baseUrl)

    // Add parameters for the HTML template (Official Zoho SDK requirements)
    paymentPageUrl.searchParams.set('sessionId', result.payment_session.payments_session_id)
    paymentPageUrl.searchParams.set('amount', parsedAmount.toString())
    paymentPageUrl.searchParams.set('currency', currency)
    paymentPageUrl.searchParams.set('description', paymentData.description)
    paymentPageUrl.searchParams.set('invoiceNumber', invoiceNo)
    paymentPageUrl.searchParams.set('referenceNumber', referenceNumber || metadata.reference_id || `REF-${Date.now()}`)
    paymentPageUrl.searchParams.set('businessName', businessName || metadata.business_name || 'Your Business')

    // Customer information
    if (customerName) paymentPageUrl.searchParams.set('customerName', customerName)
    if (customerEmail) paymentPageUrl.searchParams.set('customerEmail', customerEmail)
    if (customerPhone) paymentPageUrl.searchParams.set('customerPhone', customerPhone)
    if (callbackUrl) paymentPageUrl.searchParams.set('callbackUrl', callbackUrl)

    return NextResponse.json({
      success: true,
      payment_session: {
        id: result.payment_session.payments_session_id,
        amount: parsedAmount,
        currency,
        description: paymentData.description,
        status: 'created',
        expires_at: result.payment_session.expires_at || new Date(Date.now() + 15 * 60 * 1000).toISOString()
      },
      webview_url: paymentPageUrl.toString(),
      transaction_id: result.transaction_id,
      expires_in_minutes: 15,
      // Additional metadata for Flutter app
      flutter_integration: {
        callback_handlers: [
          'flutter_inappwebview.callHandler',
          'webkit.messageHandlers.flutterPaymentCallback',
          'window.flutterPaymentCallback'
        ],
        expected_callbacks: ['success', 'failure', 'cancelled', 'error']
      }
    })

  } catch (error: any) {
    console.error('Flutter payment initiation error:', error)
    
    // Handle specific error types
    if (error.message.includes('token')) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Authentication Error',
          message: 'Zoho Payment authentication failed',
          code: 'AUTH_ERROR'
        },
        { status: 401 }
      )
    }

    if (error.message.includes('Zoho API Error')) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Zoho Payment API Error',
          message: error.message,
          code: 'ZOHO_API_ERROR'
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Payment initiation failed',
        message: error.message,
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    )
  }
}
