/**
 * End-to-End Payment Flow Testing Suite
 * Tests complete payment flows from creation to webhook processing
 */

const axios = require('axios')
const crypto = require('crypto')
require('dotenv').config({ path: '.env.local' })

// Test configuration
const BASE_URL = process.env.NEXT_PUBLIC_DOMAIN || 'http://localhost:3000'
const WEBHOOK_SECRET = process.env.ZOHO_WEBHOOK_SECRET
const TEST_TIMEOUT = 30000 // 30 seconds

console.log('🧪 END-TO-END PAYMENT FLOW TESTING SUITE')
console.log('==========================================')
console.log(`Base URL: ${BASE_URL}`)
console.log(`Test Timeout: ${TEST_TIMEOUT}ms`)
console.log('')

/**
 * Test payment link creation and webhook flow
 */
async function testPaymentLinkFlow() {
  console.log('🔗 Testing Payment Link Flow')
  console.log('-----------------------------')

  try {
    // Step 1: Create payment link
    console.log('📝 Step 1: Creating payment link...')
    const paymentLinkData = {
      amount: 100.50,
      currency: 'INR',
      description: 'E2E Test Payment Link',
      customer_email: '<EMAIL>',
      customer_id: 'test_customer_001',
      customer_name: 'Test Customer',
      invoice_number: `TEST_INV_${Date.now()}`,
      reference_id: `TEST_REF_${Date.now()}`
    }

    const createResponse = await axios.post(`${BASE_URL}/api/zoho/payments/create-link`, paymentLinkData, {
      timeout: TEST_TIMEOUT,
      headers: { 'Content-Type': 'application/json' }
    })

    if (createResponse.status !== 200 || !createResponse.data.success) {
      throw new Error(`Payment link creation failed: ${JSON.stringify(createResponse.data)}`)
    }

    const paymentLink = createResponse.data
    console.log(`   ✅ Payment link created: ${paymentLink.payment_link_id}`)
    console.log(`   🔗 Payment URL: ${paymentLink.payment_link_url}`)

    // Step 2: Simulate webhook for payment success
    console.log('📡 Step 2: Simulating webhook for payment success...')
    const webhookPayload = {
      event_type: 'payment.succeeded',
      payment_link_id: paymentLink.payment_link_id,
      payment_id: `test_payment_${Date.now()}`,
      status: 'success',
      amount: paymentLinkData.amount,
      currency: paymentLinkData.currency,
      payment_method: 'card',
      created_time: Math.floor(Date.now() / 1000),
      customer_email: paymentLinkData.customer_email,
      reference_id: paymentLinkData.reference_id
    }

    const webhookResult = await simulateWebhook(webhookPayload)
    console.log(`   ✅ Webhook processed: ${webhookResult.success ? 'Success' : 'Failed'}`)

    // Step 3: Verify database update
    console.log('🗄️  Step 3: Verifying database update...')
    await new Promise(resolve => setTimeout(resolve, 2000)) // Wait for processing

    const statusResponse = await axios.get(`${BASE_URL}/api/zoho/webhooks/monitor?searchTerm=${paymentLink.payment_link_id}&limit=1`)
    
    if (statusResponse.data.success && statusResponse.data.events.length > 0) {
      const event = statusResponse.data.events[0]
      console.log(`   ✅ Database updated: Status = ${event.processing_status}`)
      return { success: true, paymentLinkId: paymentLink.payment_link_id, event }
    } else {
      throw new Error('Database verification failed')
    }

  } catch (error) {
    console.log(`   ❌ Payment link flow failed: ${error.message}`)
    return { success: false, error: error.message }
  }
}

/**
 * Test payment session creation and webhook flow
 */
async function testPaymentSessionFlow() {
  console.log('🎯 Testing Payment Session Flow')
  console.log('-------------------------------')

  try {
    // Step 1: Create payment session
    console.log('📝 Step 1: Creating payment session...')
    const paymentSessionData = {
      amount: 250.75,
      currency: 'INR',
      description: 'E2E Test Payment Session',
      customer_id: 'test_customer_002',
      customer_name: 'Test Customer 2',
      customer_email: '<EMAIL>',
      invoice_number: `TEST_SESS_${Date.now()}`
    }

    const createResponse = await axios.post(`${BASE_URL}/api/zoho/payments/create-session`, paymentSessionData, {
      timeout: TEST_TIMEOUT,
      headers: { 'Content-Type': 'application/json' }
    })

    if (createResponse.status !== 200 || !createResponse.data.success) {
      throw new Error(`Payment session creation failed: ${JSON.stringify(createResponse.data)}`)
    }

    const paymentSession = createResponse.data
    console.log(`   ✅ Payment session created: ${paymentSession.payment_session.payments_session_id}`)

    // Step 2: Simulate webhook for payment success
    console.log('📡 Step 2: Simulating webhook for payment success...')
    const webhookPayload = {
      event_type: 'payment.succeeded',
      payment_session_id: paymentSession.payment_session.payments_session_id,
      payment_id: `test_payment_sess_${Date.now()}`,
      status: 'success',
      amount: paymentSessionData.amount,
      currency: paymentSessionData.currency,
      payment_method: 'upi',
      created_time: Math.floor(Date.now() / 1000),
      invoice_number: paymentSessionData.invoice_number
    }

    const webhookResult = await simulateWebhook(webhookPayload)
    console.log(`   ✅ Webhook processed: ${webhookResult.success ? 'Success' : 'Failed'}`)

    // Step 3: Verify database update
    console.log('🗄️  Step 3: Verifying database update...')
    await new Promise(resolve => setTimeout(resolve, 2000)) // Wait for processing

    const statusResponse = await axios.get(`${BASE_URL}/api/zoho/webhooks/monitor?searchTerm=${paymentSession.payment_session.payments_session_id}&limit=1`)
    
    if (statusResponse.data.success && statusResponse.data.events.length > 0) {
      const event = statusResponse.data.events[0]
      console.log(`   ✅ Database updated: Status = ${event.processing_status}`)
      return { success: true, paymentSessionId: paymentSession.payment_session.payments_session_id, event }
    } else {
      throw new Error('Database verification failed')
    }

  } catch (error) {
    console.log(`   ❌ Payment session flow failed: ${error.message}`)
    return { success: false, error: error.message }
  }
}

/**
 * Test webhook security features
 */
async function testWebhookSecurity() {
  console.log('🔒 Testing Webhook Security Features')
  console.log('------------------------------------')

  const securityTests = []

  try {
    // Test 1: Valid HMAC signature
    console.log('🔐 Test 1: Valid HMAC signature...')
    const validPayload = {
      event_type: 'payment.succeeded',
      payment_id: 'test_security_001',
      status: 'success',
      amount: 100,
      currency: 'INR',
      created_time: Math.floor(Date.now() / 1000)
    }

    const validResult = await simulateWebhook(validPayload, true)
    securityTests.push({
      test: 'Valid HMAC signature',
      passed: validResult.success,
      details: validResult
    })
    console.log(`   ${validResult.success ? '✅' : '❌'} Valid signature test`)

    // Test 2: Invalid HMAC signature
    console.log('🔐 Test 2: Invalid HMAC signature...')
    const invalidResult = await simulateWebhook(validPayload, false, 'invalid_signature')
    securityTests.push({
      test: 'Invalid HMAC signature rejection',
      passed: !invalidResult.success && invalidResult.status === 401,
      details: invalidResult
    })
    console.log(`   ${!invalidResult.success ? '✅' : '❌'} Invalid signature rejection`)

    // Test 3: Replay attack prevention (old timestamp)
    console.log('🔐 Test 3: Replay attack prevention...')
    const oldPayload = {
      ...validPayload,
      created_time: Math.floor((Date.now() - 10 * 60 * 1000) / 1000) // 10 minutes ago
    }

    const replayResult = await simulateWebhook(oldPayload, true)
    securityTests.push({
      test: 'Replay attack prevention',
      passed: !replayResult.success && replayResult.status === 401,
      details: replayResult
    })
    console.log(`   ${!replayResult.success ? '✅' : '❌'} Replay attack prevention`)

    // Test 4: Idempotency protection
    console.log('🔐 Test 4: Idempotency protection...')
    const idempotentPayload = {
      ...validPayload,
      payment_id: 'test_idempotent_001'
    }

    const firstResult = await simulateWebhook(idempotentPayload, true)
    const secondResult = await simulateWebhook(idempotentPayload, true) // Same payload

    securityTests.push({
      test: 'Idempotency protection',
      passed: firstResult.success && secondResult.success && secondResult.message?.includes('already processed'),
      details: { first: firstResult, second: secondResult }
    })
    console.log(`   ${secondResult.message?.includes('already processed') ? '✅' : '❌'} Idempotency protection`)

    return { success: true, tests: securityTests }

  } catch (error) {
    console.log(`   ❌ Security testing failed: ${error.message}`)
    return { success: false, error: error.message, tests: securityTests }
  }
}

/**
 * Simulate webhook request with proper security
 */
async function simulateWebhook(payload, useValidSignature = true, customSignature = null) {
  try {
    const payloadString = JSON.stringify(payload)
    let signature = customSignature

    if (useValidSignature && !customSignature) {
      // Create valid HMAC signature
      const timestamp = payload.created_time || Math.floor(Date.now() / 1000)
      const signaturePayload = `${timestamp}.${payloadString}`
      signature = crypto.createHmac('sha256', WEBHOOK_SECRET).update(signaturePayload).digest('hex')
    }

    const response = await axios.post(`${BASE_URL}/api/zoho/webhooks/payment`, payload, {
      timeout: TEST_TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        'x-zoho-webhook-signature': signature || 'invalid_signature'
      },
      validateStatus: () => true // Don't throw on non-2xx status
    })

    return {
      success: response.status === 200,
      status: response.status,
      data: response.data,
      message: response.data?.message
    }

  } catch (error) {
    return {
      success: false,
      error: error.message,
      status: error.response?.status
    }
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting End-to-End Payment Flow Tests')
  console.log('==========================================\n')

  const results = {
    paymentLink: null,
    paymentSession: null,
    security: null,
    overall: false
  }

  try {
    // Test payment link flow
    results.paymentLink = await testPaymentLinkFlow()
    console.log('')

    // Test payment session flow
    results.paymentSession = await testPaymentSessionFlow()
    console.log('')

    // Test webhook security
    results.security = await testWebhookSecurity()
    console.log('')

    // Overall results
    results.overall = results.paymentLink.success && 
                     results.paymentSession.success && 
                     results.security.success

    console.log('📊 TEST RESULTS SUMMARY')
    console.log('=======================')
    console.log(`Payment Link Flow: ${results.paymentLink.success ? '✅ PASS' : '❌ FAIL'}`)
    console.log(`Payment Session Flow: ${results.paymentSession.success ? '✅ PASS' : '❌ FAIL'}`)
    console.log(`Security Features: ${results.security.success ? '✅ PASS' : '❌ FAIL'}`)
    console.log(`Overall Status: ${results.overall ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`)

    if (results.security.tests) {
      console.log('\n🔒 Security Test Details:')
      results.security.tests.forEach(test => {
        console.log(`   ${test.passed ? '✅' : '❌'} ${test.test}`)
      })
    }

    return results

  } catch (error) {
    console.error('❌ Test suite failed:', error.message)
    return { ...results, error: error.message }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests()
    .then((results) => {
      process.exit(results.overall ? 0 : 1)
    })
    .catch((error) => {
      console.error('❌ Test execution failed:', error)
      process.exit(1)
    })
}

module.exports = { 
  runAllTests, 
  testPaymentLinkFlow, 
  testPaymentSessionFlow, 
  testWebhookSecurity,
  simulateWebhook 
}
