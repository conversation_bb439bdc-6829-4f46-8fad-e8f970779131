/**
 * Webhook Security Utilities
 *
 * Production-grade security features for webhook processing:
 * - Idempotency protection
 * - Replay attack prevention
 * - IP whitelisting
 * - Retry mechanisms
 */

import crypto from 'crypto'
import { Logger } from './monitoring.js'

/**
 * Generate unique event ID for idempotency
 */
export function generateEventId(webhookData, timestamp) {
  const { event_type, payment_session_id, payment_link_id, payment_id, created_time } = webhookData

  // Use multiple identifiers to create unique event ID
  const identifiers = [
    event_type,
    payment_session_id || payment_link_id || 'unknown',
    payment_id || 'no_payment_id',
    created_time || timestamp || Date.now(),
  ].join('_')

  // Create hash for consistent length and uniqueness
  return crypto.createHash('sha256').update(identifiers).digest('hex').substring(0, 32)
}

/**
 * Validate webhook timestamp to prevent replay attacks
 */
export function validateWebhookTimestamp(webhookData, maxAgeMinutes = 5) {
  try {
    const now = Date.now()
    let webhookTimestamp

    // Try to extract timestamp from webhook data
    if (webhookData.created_time) {
      // Zoho typically sends Unix timestamp in seconds
      webhookTimestamp = webhookData.created_time * 1000
    } else if (webhookData.timestamp) {
      webhookTimestamp = new Date(webhookData.timestamp).getTime()
    } else {
      // If no timestamp in payload, use current time (less secure but functional)
      Logger.warn('No timestamp found in webhook payload', {
        method: 'validateWebhookTimestamp',
        event_type: webhookData.event_type,
        payment_session_id: webhookData.payment_session_id,
      })
      return { valid: true, timestamp: new Date(), age: 0 }
    }

    const age = now - webhookTimestamp
    const maxAge = maxAgeMinutes * 60 * 1000 // Convert to milliseconds
    const valid = age <= maxAge && age >= -60000 // Allow 1 minute clock skew

    Logger.debug('Webhook timestamp validation', {
      method: 'validateWebhookTimestamp',
      webhookTimestamp: new Date(webhookTimestamp).toISOString(),
      currentTime: new Date(now).toISOString(),
      ageMs: age,
      maxAgeMs: maxAge,
      valid,
    })

    return {
      valid,
      timestamp: new Date(webhookTimestamp),
      age: Math.floor(age / 1000), // Return age in seconds
    }
  } catch (error) {
    Logger.error('Error validating webhook timestamp', {
      method: 'validateWebhookTimestamp',
      error: error.message,
      webhookData: JSON.stringify(webhookData),
    })
    return { valid: false, timestamp: null, age: null, error: error.message }
  }
}

/**
 * Parse and validate IP whitelist configuration
 */
export function parseIPWhitelist(whitelistString) {
  if (!whitelistString) return []

  return whitelistString
    .split(',')
    .map((ip) => ip.trim())
    .filter((ip) => ip.length > 0)
    .map((ip) => {
      // Support CIDR notation
      if (ip.includes('/')) {
        return { type: 'cidr', value: ip }
      } else {
        return { type: 'ip', value: ip }
      }
    })
}

/**
 * Check if IP address is in whitelist
 */
export function isIPWhitelisted(clientIP, whitelist) {
  if (!whitelist || whitelist.length === 0) {
    // If no whitelist configured, allow all IPs (log warning)
    Logger.warn('No IP whitelist configured - allowing all IPs', {
      method: 'isIPWhitelisted',
      clientIP,
    })
    return true
  }

  // Handle IPv6 mapped IPv4 addresses
  const normalizedIP = clientIP.replace(/^::ffff:/, '')

  for (const entry of whitelist) {
    if (entry.type === 'ip') {
      if (normalizedIP === entry.value) {
        return true
      }
    } else if (entry.type === 'cidr') {
      if (isIPInCIDR(normalizedIP, entry.value)) {
        return true
      }
    }
  }

  return false
}

/**
 * Check if IP is in CIDR range
 */
function isIPInCIDR(ip, cidr) {
  try {
    const [network, prefixLength] = cidr.split('/')
    const prefix = parseInt(prefixLength, 10)

    // Convert IP addresses to integers for comparison
    const ipInt = ipToInt(ip)
    const networkInt = ipToInt(network)

    // Create subnet mask
    const mask = (0xffffffff << (32 - prefix)) >>> 0

    return (ipInt & mask) === (networkInt & mask)
  } catch (error) {
    Logger.error('Error checking CIDR range', {
      method: 'isIPInCIDR',
      ip,
      cidr,
      error: error.message,
    })
    return false
  }
}

/**
 * Convert IP address to integer
 */
function ipToInt(ip) {
  return ip.split('.').reduce((acc, octet) => (acc << 8) + parseInt(octet, 10), 0) >>> 0
}

/**
 * Get client IP from request headers
 */
export function getClientIP(request) {
  // Check various headers for the real client IP
  const headers = [
    'x-forwarded-for',
    'x-real-ip',
    'x-client-ip',
    'cf-connecting-ip', // Cloudflare
    'x-cluster-client-ip',
    'forwarded',
  ]

  for (const header of headers) {
    const value = request.headers.get(header)
    if (value) {
      // x-forwarded-for can contain multiple IPs, take the first one
      const ip = value.split(',')[0].trim()
      if (ip && ip !== 'unknown') {
        return ip
      }
    }
  }

  // Fallback to connection remote address (may not be available in serverless)
  return request.headers.get('x-forwarded-for') || request.headers.get('remote-addr') || 'unknown'
}

/**
 * Enhanced HMAC signature verification with timestamp
 */
export function verifyWebhookSignature(body, signature, secret, timestamp = null) {
  try {
    if (!secret) {
      Logger.warn('No webhook secret configured - skipping signature verification', {
        method: 'verifyWebhookSignature',
      })
      return { verified: true, reason: 'no_secret_configured' }
    }

    if (!signature) {
      return { verified: false, reason: 'missing_signature' }
    }

    // Create payload for signature verification
    let payload = body
    if (timestamp) {
      // Include timestamp in signature verification for enhanced security
      payload = `${timestamp}.${body}`
    }

    const expectedSignature = crypto.createHmac('sha256', secret).update(payload).digest('hex')

    // Support both prefixed and non-prefixed signatures
    const cleanSignature = signature.replace(/^sha256=/, '')
    const verified = crypto.timingSafeEqual(Buffer.from(cleanSignature, 'hex'), Buffer.from(expectedSignature, 'hex'))

    return {
      verified,
      reason: verified ? 'signature_valid' : 'signature_mismatch',
      expectedSignature: `sha256=${expectedSignature}`,
    }
  } catch (error) {
    Logger.error('Error verifying webhook signature', {
      method: 'verifyWebhookSignature',
      error: error.message,
      hasSignature: !!signature,
      hasSecret: !!secret,
    })
    return { verified: false, reason: 'verification_error', error: error.message }
  }
}

/**
 * Retry mechanism utilities
 */
export class WebhookRetryManager {
  constructor() {
    this.maxRetries = 3
    this.baseDelay = 1000 // 1 second
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  calculateRetryDelay(attemptNumber) {
    return this.baseDelay * Math.pow(2, attemptNumber - 1)
  }

  /**
   * Process webhook with retry logic
   */
  async processWithRetry(webhookEvent, processingFunction) {
    let lastError = null

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        Logger.info('Processing webhook attempt', {
          method: 'processWithRetry',
          webhookId: webhookEvent.webhook_id,
          attempt,
          maxRetries: this.maxRetries,
        })

        const result = await processingFunction(webhookEvent)

        Logger.info('Webhook processing succeeded', {
          method: 'processWithRetry',
          webhookId: webhookEvent.webhook_id,
          attempt,
          result: result?.status || 'success',
        })

        return result
      } catch (error) {
        lastError = error

        Logger.warn('Webhook processing attempt failed', {
          method: 'processWithRetry',
          webhookId: webhookEvent.webhook_id,
          attempt,
          error: error.message,
          willRetry: attempt < this.maxRetries,
        })

        // Don't retry on certain errors (client errors, validation errors)
        if (this.isNonRetryableError(error)) {
          Logger.error('Non-retryable error encountered', {
            method: 'processWithRetry',
            webhookId: webhookEvent.webhook_id,
            error: error.message,
            errorType: error.constructor.name,
          })
          throw error
        }

        // If this isn't the last attempt, schedule retry
        if (attempt < this.maxRetries) {
          const delay = this.calculateRetryDelay(attempt)
          await webhookEvent.scheduleRetry(delay)

          Logger.info('Webhook retry scheduled', {
            method: 'processWithRetry',
            webhookId: webhookEvent.webhook_id,
            nextAttempt: attempt + 1,
            delayMs: delay,
            retryAt: new Date(Date.now() + delay).toISOString(),
          })

          // Wait for the delay before next attempt
          await new Promise((resolve) => setTimeout(resolve, delay))
        }
      }
    }

    // All retries exhausted
    Logger.error('Webhook processing failed after all retries', {
      method: 'processWithRetry',
      webhookId: webhookEvent.webhook_id,
      totalAttempts: this.maxRetries,
      finalError: lastError?.message,
    })

    throw lastError
  }

  /**
   * Check if error should not be retried
   */
  isNonRetryableError(error) {
    // Don't retry validation errors, authentication errors, etc.
    const nonRetryablePatterns = [
      /validation/i,
      /authentication/i,
      /authorization/i,
      /invalid.*signature/i,
      /malformed/i,
      /bad.*request/i,
    ]

    const errorMessage = error.message || ''
    return (
      nonRetryablePatterns.some((pattern) => pattern.test(errorMessage)) || (error.status >= 400 && error.status < 500)
    ) // HTTP 4xx errors
  }

  /**
   * Process pending retries
   */
  async processPendingRetries(WebhookEvent, processingFunction) {
    try {
      const retryableEvents = await WebhookEvent.findRetryableEvents()

      Logger.info('Processing pending webhook retries', {
        method: 'processPendingRetries',
        count: retryableEvents.length,
      })

      for (const event of retryableEvents) {
        try {
          await this.processWithRetry(event, processingFunction)
        } catch (error) {
          // Mark as permanently failed
          await event.markAsFailed(error, null)

          Logger.error('Webhook permanently failed after retries', {
            method: 'processPendingRetries',
            webhookId: event.webhook_id,
            eventId: event.event_id,
            error: error.message,
          })
        }
      }
    } catch (error) {
      Logger.error('Error processing pending retries', {
        method: 'processPendingRetries',
        error: error.message,
      })
    }
  }
}

// Export singleton instance
export const webhookRetryManager = new WebhookRetryManager()
