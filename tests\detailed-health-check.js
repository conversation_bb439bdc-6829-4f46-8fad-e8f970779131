const fetch = require('node-fetch');

async function detailedHealthCheck() {
  const PRODUCTION_URL = 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net';
  
  console.log('🔍 Detailed Health Check Analysis');
  console.log('================================');
  
  try {
    console.log(`\n📡 Testing: ${PRODUCTION_URL}/api/zoho/health`);
    
    const response = await fetch(`${PRODUCTION_URL}/api/zoho/health`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'Production-Readiness-Test/1.0'
      },
      timeout: 30000
    });
    
    console.log(`Status: ${response.status} ${response.statusText}`);
    console.log(`Headers:`, Object.fromEntries(response.headers.entries()));
    
    if (response.ok) {
      const data = await response.json();
      console.log('\n📊 Health Check Response:');
      console.log(JSON.stringify(data, null, 2));
      
      // Analyze the response
      console.log('\n🔍 Analysis:');
      console.log(`Service Status: ${data.status}`);
      console.log(`Domain Configuration: ${data.configuration?.domain}`);
      console.log(`Account ID: ${data.configuration?.account_id}`);
      console.log(`Webhook Secret: ${data.configuration?.webhook_secret}`);
      
      if (data.checks) {
        console.log('\n🧪 Individual Checks:');
        Object.entries(data.checks).forEach(([check, result]) => {
          console.log(`  ${check}: ${result.status} ${result.message ? '- ' + result.message : ''}`);
        });
      }
      
    } else {
      const errorText = await response.text();
      console.log(`\n❌ Error Response: ${errorText}`);
    }
    
  } catch (error) {
    console.log(`\n💥 Request Failed: ${error.message}`);
    console.log(`Error Details:`, error);
  }
}

// Test payment session creation
async function testPaymentCreation() {
  const PRODUCTION_URL = 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net';
  
  console.log('\n\n💳 Testing Payment Session Creation');
  console.log('===================================');
  
  const testData = {
    amount: 100.0,
    currency: 'INR',
    description: 'Production Test Payment',
    invoice_number: `TEST_${Date.now()}`,
    customer_id: 'test_customer_prod',
    customer_name: 'Test Customer',
    customer_email: '<EMAIL>',
    customer_phone: '+91-9876543210'
  };
  
  try {
    const response = await fetch(`${PRODUCTION_URL}/api/zoho/payments/create-session`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(testData),
      timeout: 30000
    });
    
    console.log(`Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('\n✅ Payment Session Created Successfully:');
      console.log(JSON.stringify(data, null, 2));
      
      return data.data?.payment_session_id;
    } else {
      const errorText = await response.text();
      console.log(`\n❌ Payment Creation Failed: ${errorText}`);
    }
    
  } catch (error) {
    console.log(`\n💥 Payment Creation Error: ${error.message}`);
  }
  
  return null;
}

// Test payment status
async function testPaymentStatus(sessionId) {
  if (!sessionId) return;
  
  const PRODUCTION_URL = 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net';
  
  console.log('\n\n📊 Testing Payment Status Retrieval');
  console.log('===================================');
  
  try {
    const response = await fetch(`${PRODUCTION_URL}/api/zoho/payments/status/${sessionId}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      },
      timeout: 30000
    });
    
    console.log(`Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('\n✅ Payment Status Retrieved:');
      console.log(JSON.stringify(data, null, 2));
    } else {
      const errorText = await response.text();
      console.log(`\n❌ Status Retrieval Failed: ${errorText}`);
    }
    
  } catch (error) {
    console.log(`\n💥 Status Retrieval Error: ${error.message}`);
  }
}

// Run all tests
async function runDetailedTests() {
  await detailedHealthCheck();
  const sessionId = await testPaymentCreation();
  await testPaymentStatus(sessionId);
  
  console.log('\n\n🏁 Detailed Testing Complete');
}

runDetailedTests().catch(console.error);
