/**
 * Test script to validate webhook handling for payment links strategy
 * This script tests the enhanced webhook processing for both payment sessions and payment links
 */

const axios = require('axios')
const crypto = require('crypto')

// Configuration
const BASE_URL = process.env.BASE_URL || 'http://localhost:3000'
const WEBHOOK_SECRET = process.env.ZOHO_WEBHOOK_SECRET || 'test-secret'
const WEBHOOK_URL = `${BASE_URL}/api/zoho/webhooks/payment`

console.log('🧪 WEBHOOK PAYMENT LINKS TEST')
console.log('==============================')
console.log(`Base URL: ${BASE_URL}`)
console.log(`Webhook URL: ${WEBHOOK_URL}`)
console.log('')

// Helper function to create webhook signature
function createWebhookSignature(payload, secret) {
  return crypto.createHmac('sha256', secret).update(payload).digest('hex')
}

// Test 1: Payment Session Webhook (Legacy)
async function testPaymentSessionWebhook() {
  try {
    console.log('📡 TEST 1: Payment Session Webhook (Legacy Format)')
    console.log('--------------------------------------------------')
    
    const webhookPayload = {
      event_type: 'payment.succeeded',
      payment_session_id: 'test_session_12345',
      payment_id: 'pay_67890',
      status: 'succeeded',
      amount: 600.10,
      currency: 'INR',
      payment_method: 'card',
      created_time: Math.floor(Date.now() / 1000),
      customer_details: {
        customer_id: '******************',
        customer_email: '<EMAIL>'
      }
    }

    const payloadString = JSON.stringify(webhookPayload)
    const signature = createWebhookSignature(payloadString, WEBHOOK_SECRET)

    console.log('📡 Sending payment session webhook...')
    console.log('Payload:', JSON.stringify(webhookPayload, null, 2))

    const response = await axios.post(WEBHOOK_URL, payloadString, {
      headers: {
        'Content-Type': 'application/json',
        'x-zoho-webhook-signature': signature,
      },
    })

    console.log('✅ Response Status:', response.status)
    console.log('✅ Response Data:', JSON.stringify(response.data, null, 2))

    if (response.status === 200 && response.data.success) {
      console.log('✅ SUCCESS: Payment session webhook processed successfully')
    } else {
      console.log('❌ ERROR: Unexpected response for payment session webhook')
    }

  } catch (error) {
    console.error('❌ TEST FAILED:', error.message)
    if (error.response) {
      console.error('Response Status:', error.response.status)
      console.error('Response Data:', error.response.data)
    }
  }
}

// Test 2: Payment Link Webhook (New Format)
async function testPaymentLinkWebhook() {
  try {
    console.log('')
    console.log('🔗 TEST 2: Payment Link Webhook (New Format)')
    console.log('---------------------------------------------')
    
    const webhookPayload = {
      event_type: 'payment.succeeded',
      payment_link_id: 'link_abcdef123456',
      payment_id: 'pay_link_67890',
      status: 'succeeded',
      amount: 600.10,
      currency: 'INR',
      payment_method: 'upi',
      created_time: Math.floor(Date.now() / 1000),
      customer_details: {
        customer_id: '******************',
        customer_email: '<EMAIL>'
      }
    }

    const payloadString = JSON.stringify(webhookPayload)
    const signature = createWebhookSignature(payloadString, WEBHOOK_SECRET)

    console.log('📡 Sending payment link webhook...')
    console.log('Payload:', JSON.stringify(webhookPayload, null, 2))

    const response = await axios.post(WEBHOOK_URL, payloadString, {
      headers: {
        'Content-Type': 'application/json',
        'x-zoho-webhook-signature': signature,
      },
    })

    console.log('✅ Response Status:', response.status)
    console.log('✅ Response Data:', JSON.stringify(response.data, null, 2))

    if (response.status === 200 && response.data.success) {
      console.log('✅ SUCCESS: Payment link webhook processed successfully')
    } else {
      console.log('❌ ERROR: Unexpected response for payment link webhook')
    }

  } catch (error) {
    console.error('❌ TEST FAILED:', error.message)
    if (error.response) {
      console.error('Response Status:', error.response.status)
      console.error('Response Data:', error.response.data)
    }
  }
}

// Test 3: Invalid Webhook (Missing Required Fields)
async function testInvalidWebhook() {
  try {
    console.log('')
    console.log('❌ TEST 3: Invalid Webhook (Missing Required Fields)')
    console.log('---------------------------------------------------')
    
    const webhookPayload = {
      event_type: 'payment.succeeded',
      // Missing both payment_session_id and payment_link_id
      payment_id: 'pay_invalid',
      status: 'succeeded',
      amount: 100.00,
      currency: 'INR'
    }

    const payloadString = JSON.stringify(webhookPayload)
    const signature = createWebhookSignature(payloadString, WEBHOOK_SECRET)

    console.log('📡 Sending invalid webhook...')

    const response = await axios.post(WEBHOOK_URL, payloadString, {
      headers: {
        'Content-Type': 'application/json',
        'x-zoho-webhook-signature': signature,
      },
    })

    console.log('Response Status:', response.status)
    console.log('Response Data:', JSON.stringify(response.data, null, 2))

    if (response.status === 400) {
      console.log('✅ SUCCESS: Invalid webhook properly rejected with 400 status')
    } else {
      console.log('❌ ERROR: Expected 400 status for invalid webhook')
    }

  } catch (error) {
    if (error.response && error.response.status === 400) {
      console.log('✅ SUCCESS: Invalid webhook properly rejected with 400 status')
      console.log('Error message:', error.response.data.message)
    } else {
      console.error('❌ UNEXPECTED ERROR:', error.message)
    }
  }
}

// Test 4: Webhook Configuration Endpoint
async function testWebhookConfiguration() {
  try {
    console.log('')
    console.log('⚙️ TEST 4: Webhook Configuration Endpoint')
    console.log('------------------------------------------')
    
    const response = await axios.get(WEBHOOK_URL)

    console.log('✅ Response Status:', response.status)
    console.log('✅ Configuration:', JSON.stringify(response.data, null, 2))

    if (response.status === 200 && response.data.supported_events) {
      console.log('✅ SUCCESS: Webhook configuration endpoint working')
      
      // Check if new event types are supported
      const supportedEvents = response.data.supported_events.map(e => e.event)
      const requiredEvents = ['payment.succeeded', 'payment.failed', 'payment.pending']
      
      const missingEvents = requiredEvents.filter(event => !supportedEvents.includes(event))
      if (missingEvents.length === 0) {
        console.log('✅ SUCCESS: All required event types are supported')
      } else {
        console.log('⚠️ WARNING: Missing event types:', missingEvents)
      }
    } else {
      console.log('❌ ERROR: Webhook configuration endpoint not working properly')
    }

  } catch (error) {
    console.error('❌ TEST FAILED:', error.message)
  }
}

async function runTests() {
  console.log('🚀 Starting webhook payment links tests...')
  console.log('')
  
  await testPaymentSessionWebhook()
  await testPaymentLinkWebhook()
  await testInvalidWebhook()
  await testWebhookConfiguration()
  
  console.log('')
  console.log('🏁 Test execution completed!')
  console.log('')
  console.log('📋 SUMMARY:')
  console.log('- Test 1: Validates payment session webhook processing (legacy format)')
  console.log('- Test 2: Validates payment link webhook processing (new format)')
  console.log('- Test 3: Validates proper rejection of invalid webhooks')
  console.log('- Test 4: Validates webhook configuration endpoint')
  console.log('')
  console.log('✅ If all tests pass, the webhook payment links integration is working correctly!')
}

// Run the tests
runTests().catch(console.error)
