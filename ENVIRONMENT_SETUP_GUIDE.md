# Environment Configuration Guide

This guide explains how to set up the required environment variables for the Zoho Payment integration with official SDK.

## Required Environment Variables

### 1. Zoho Payment Configuration

```bash
# Zoho Payment API Configuration
ZOHO_PAYMENT_SESSION_URL=https://payments.zoho.in/api/v1/paymentsessions
ZOHO_PAY_ACCOUNT_ID=your_account_id_here
ZOHO_PAY_API_KEY=your_api_key_here
ZOHO_WEBHOOK_SECRET=your_webhook_secret_here
```

**How to get these values:**

1. **Account ID**: 
   - Login to [Zoho Payments](https://payments.zoho.in/)
   - Go to Settings > Developer Space
   - Copy your Account ID

2. **API Key**:
   - In Developer Space, click "Generate API Key"
   - Copy the generated API key
   - **Important**: Store this securely, it won't be shown again

3. **Webhook Secret**:
   - In Developer Space, go to Webhooks section
   - Create a new webhook endpoint
   - Copy the webhook secret

### 2. Domain Configuration

```bash
# Domain Configuration
NEXT_PUBLIC_DOMAIN=https://your-production-domain.com
NEXT_PUBLIC_API_DOMAIN=https://your-api-domain.com
```

### 3. Database Configuration

```bash
# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/aquapartner
# Or for MongoDB Atlas:
# MONGODB_URI=mongodb+srv://username:<EMAIL>/aquapartner
```

## Development vs Production

### Development Environment (.env.local)
```bash
NODE_ENV=development
NEXT_PUBLIC_DOMAIN=http://localhost:3000
NEXT_PUBLIC_API_DOMAIN=http://localhost:3000
ZOHO_PAY_ACCOUNT_ID=your_test_account_id
ZOHO_PAY_API_KEY=your_test_api_key
```

### Production Environment
```bash
NODE_ENV=production
NEXT_PUBLIC_DOMAIN=https://your-production-domain.com
NEXT_PUBLIC_API_DOMAIN=https://your-production-domain.com
ZOHO_PAY_ACCOUNT_ID=your_live_account_id
ZOHO_PAY_API_KEY=your_live_api_key
```

## Security Best Practices

1. **Never commit API keys** to version control
2. **Use different keys** for development and production
3. **Rotate API keys** regularly
4. **Restrict API key permissions** in Zoho Payments dashboard
5. **Use HTTPS** for all production domains

## Validation Script

Create a script to validate your environment configuration:

```javascript
// scripts/validate-env.js
const requiredVars = [
  'ZOHO_PAY_ACCOUNT_ID',
  'ZOHO_PAY_API_KEY',
  'MONGODB_URI',
  'NEXT_PUBLIC_DOMAIN'
];

const missingVars = requiredVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('Missing required environment variables:');
  missingVars.forEach(varName => console.error(`- ${varName}`));
  process.exit(1);
} else {
  console.log('✅ All required environment variables are set');
}
```

Run with: `node scripts/validate-env.js`

## Troubleshooting

### Common Issues

1. **Invalid API Key Error**:
   - Verify the API key is correct
   - Check if the key has expired
   - Ensure you're using the right environment (test/live)

2. **Account ID Mismatch**:
   - Verify the account ID matches your Zoho Payments account
   - Check if you're using the correct region (IN/US)

3. **Domain Issues**:
   - Ensure domains use HTTPS in production
   - Verify CORS settings allow your domain
   - Check if the domain is whitelisted in Zoho Payments

### Testing Configuration

Use this API endpoint to test your configuration:

```bash
curl -X POST http://localhost:3000/api/flutter/payment/initiate \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 1.00,
    "invoiceNo": "TEST-001",
    "customerId": "TEST-CUSTOMER",
    "customerName": "Test Customer",
    "description": "Test Payment"
  }'
```

Expected response should include `webview_url` and `payment_session` data.
