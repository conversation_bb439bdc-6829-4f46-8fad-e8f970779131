/**
 * Test endpoint to debug customer data structure
 */
export async function GET(request) {
  const { searchParams } = new URL(request.url)
  const customerId = searchParams.get('customerId')
  
  console.log('🔍 TEST CUSTOMER: Request for customer ID:', customerId)
  
  // Mock customer data based on what we see in the logs
  const mockCustomer = {
    customerId: '401088000053137251',
    customerName: 'SRI DEVI AQUA FEEDS & NEEDS',
    mobileNumber: '+919999999999',
    Email: null, // This might be the issue
    email: null,
    // Add other possible field names
    customerEmail: null,
    customer_email: null,
  }
  
  return new Response(
    JSON.stringify({
      success: true,
      customer: mockCustomer,
      message: 'Customer data structure for debugging',
    }),
    { status: 200 }
  )
}
