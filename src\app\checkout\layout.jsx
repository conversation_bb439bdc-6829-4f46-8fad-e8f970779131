import { Providers } from '@/customComponents/providers'
import '@/styles/tailwind.css'
import Script from 'next/script'

export const metadata = {
  title: 'Checkout - AquaPartner',
  description: 'Complete your payment securely with AquaPartner',
}

export default function CheckoutLayout({ children }) {
  return (
    <html lang="en" className="h-full bg-gray-50">
      <head>
        <link rel="preconnect" href="https://rsms.me/" />
        <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />
        <link rel="icon" href="/favicon.png" />
        {/* Zoho Payments Script - Load early for checkout */}
        <Script
          src="https://static.zohocdn.com/zpay/zpay-js/v1/zpayments.js"
          strategy="beforeInteractive"
          id="zoho-payments-script"
        />
      </head>
      <body className="h-full">
        <Providers>{children}</Providers>
      </body>
    </html>
  )
}
