import axios from 'axios'

/**
 * POST /api/test-poc-token-refresh
 * Test direct OAuth token refresh using POC approach
 */
export async function POST(request) {
  try {
    console.log('Testing POC direct OAuth token refresh...')

    const {
      ZOHO_OAUTH_GENERATE_ACCESS_TOKEN_URL,
      ZOHO_OAUTH_REFRESH_TOKEN,
      ZOHO_OAUTH_CLIENT_ID,
      ZOHO_OAUTH_CLIENT_SECRET,
      ZOHO_OAUTH_REDIRECT_URI,
      ZOHO_PAYMENT_SESSION_URL,
      ZOHO_PAY_ACCOUNT_ID
    } = process.env

    // Check for required environment variables
    const missingVars = []
    if (!ZOHO_OAUTH_GENERATE_ACCESS_TOKEN_URL) missingVars.push('ZOHO_OAUTH_GENERATE_ACCESS_TOKEN_URL')
    if (!ZOHO_OAUTH_REFRESH_TOKEN) missingVars.push('ZOHO_OAUTH_REFRESH_TOKEN')
    if (!ZOHO_OAUTH_CLIENT_ID) missingVars.push('ZOHO_OAUTH_CLIENT_ID')
    if (!ZOHO_OAUTH_CLIENT_SECRET) missingVars.push('ZOHO_OAUTH_CLIENT_SECRET')
    if (!ZOHO_OAUTH_REDIRECT_URI) missingVars.push('ZOHO_OAUTH_REDIRECT_URI')

    if (missingVars.length > 0) {
      return new Response(
        JSON.stringify({
          success: false,
          message: 'Missing required OAuth environment variables',
          missing_variables: missingVars,
          available_variables: {
            ZOHO_PAYMENT_SESSION_URL: !!ZOHO_PAYMENT_SESSION_URL,
            ZOHO_PAY_ACCOUNT_ID: !!ZOHO_PAY_ACCOUNT_ID,
          }
        }),
        { status: 400 }
      )
    }

    // Construct OAuth URL (POC approach)
    const getTokenUrl = `${ZOHO_OAUTH_GENERATE_ACCESS_TOKEN_URL}?refresh_token=${ZOHO_OAUTH_REFRESH_TOKEN}&client_id=${ZOHO_OAUTH_CLIENT_ID}&client_secret=${ZOHO_OAUTH_CLIENT_SECRET}&redirect_uri=${ZOHO_OAUTH_REDIRECT_URI}&grant_type=refresh_token`

    console.log('Making OAuth request to:', ZOHO_OAUTH_GENERATE_ACCESS_TOKEN_URL)

    // Make OAuth request
    const response = await axios.post(getTokenUrl)
    
    console.log('OAuth response status:', response.status)
    console.log('OAuth response data:', response.data)

    if (response.status === 200 && response?.data?.access_token) {
      console.log('Direct OAuth token refresh successful')
      
      // Test payment session creation with the new token
      let paymentSessionResult = null
      if (ZOHO_PAYMENT_SESSION_URL && ZOHO_PAY_ACCOUNT_ID) {
        try {
          console.log('Testing payment session creation...')
          
          const paymentSessionResponse = await axios.post(
            `${ZOHO_PAYMENT_SESSION_URL}?account_id=${ZOHO_PAY_ACCOUNT_ID}`,
            {
              amount: 1.0,
              currency: 'INR',
              meta_data: [
                {
                  key: 'test_type',
                  value: 'poc_token_refresh_test',
                },
              ],
            },
            {
              headers: {
                Authorization: `Zoho-oauthtoken ${response.data.access_token}`,
                'Content-Type': 'application/json',
              },
            }
          )

          paymentSessionResult = {
            success: true,
            session_id: paymentSessionResponse?.data?.payments_session?.payments_session_id,
            response: paymentSessionResponse.data
          }

          console.log('Payment session created:', paymentSessionResult.session_id)

        } catch (paymentError) {
          console.error('Payment session creation failed:', paymentError.message)
          paymentSessionResult = {
            success: false,
            error: paymentError.message,
            response: paymentError.response?.data
          }
        }
      }

      return new Response(
        JSON.stringify({
          success: true,
          message: 'POC direct OAuth token refresh successful',
          data: {
            access_token: response.data.access_token,
            token_type: response.data.token_type,
            expires_in: response.data.expires_in,
            scope: response.data.scope,
            api_domain: response.data.api_domain,
            refresh_time: new Date().toISOString(),
          },
          payment_session_test: paymentSessionResult,
          environment_check: {
            ZOHO_OAUTH_GENERATE_ACCESS_TOKEN_URL: !!ZOHO_OAUTH_GENERATE_ACCESS_TOKEN_URL,
            ZOHO_OAUTH_REFRESH_TOKEN: !!ZOHO_OAUTH_REFRESH_TOKEN,
            ZOHO_OAUTH_CLIENT_ID: !!ZOHO_OAUTH_CLIENT_ID,
            ZOHO_OAUTH_CLIENT_SECRET: !!ZOHO_OAUTH_CLIENT_SECRET,
            ZOHO_OAUTH_REDIRECT_URI: !!ZOHO_OAUTH_REDIRECT_URI,
            ZOHO_PAYMENT_SESSION_URL: !!ZOHO_PAYMENT_SESSION_URL,
            ZOHO_PAY_ACCOUNT_ID: !!ZOHO_PAY_ACCOUNT_ID,
          }
        }),
        { status: 200 }
      )

    } else {
      console.error('Invalid OAuth response:', response.data)
      
      return new Response(
        JSON.stringify({
          success: false,
          message: 'Invalid OAuth response',
          error: `Expected access_token but got: ${JSON.stringify(response.data)}`,
          status_code: response.status
        }),
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('POC token refresh test error:', error.message)
    
    return new Response(
      JSON.stringify({
        success: false,
        message: 'POC token refresh test failed',
        error: error.message,
        details: error.response?.data || 'No additional details available',
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      }),
      { status: 500 }
    )
  }
}

/**
 * GET /api/test-poc-token-refresh
 * Check environment variables for POC approach
 */
export async function GET() {
  const {
    ZOHO_OAUTH_GENERATE_ACCESS_TOKEN_URL,
    ZOHO_OAUTH_REFRESH_TOKEN,
    ZOHO_OAUTH_CLIENT_ID,
    ZOHO_OAUTH_CLIENT_SECRET,
    ZOHO_OAUTH_REDIRECT_URI,
    ZOHO_PAYMENT_SESSION_URL,
    ZOHO_PAY_ACCOUNT_ID
  } = process.env

  return new Response(
    JSON.stringify({
      message: 'POC Environment Variables Check',
      oauth_variables: {
        ZOHO_OAUTH_GENERATE_ACCESS_TOKEN_URL: !!ZOHO_OAUTH_GENERATE_ACCESS_TOKEN_URL,
        ZOHO_OAUTH_REFRESH_TOKEN: !!ZOHO_OAUTH_REFRESH_TOKEN,
        ZOHO_OAUTH_CLIENT_ID: !!ZOHO_OAUTH_CLIENT_ID,
        ZOHO_OAUTH_CLIENT_SECRET: !!ZOHO_OAUTH_CLIENT_SECRET,
        ZOHO_OAUTH_REDIRECT_URI: !!ZOHO_OAUTH_REDIRECT_URI,
      },
      payment_variables: {
        ZOHO_PAYMENT_SESSION_URL: !!ZOHO_PAYMENT_SESSION_URL,
        ZOHO_PAY_ACCOUNT_ID: !!ZOHO_PAY_ACCOUNT_ID,
      },
      ready_for_poc: !!(
        ZOHO_OAUTH_GENERATE_ACCESS_TOKEN_URL &&
        ZOHO_OAUTH_REFRESH_TOKEN &&
        ZOHO_OAUTH_CLIENT_ID &&
        ZOHO_OAUTH_CLIENT_SECRET &&
        ZOHO_OAUTH_REDIRECT_URI &&
        ZOHO_PAY_ACCOUNT_ID
      )
    }),
    { status: 200 }
  )
}
