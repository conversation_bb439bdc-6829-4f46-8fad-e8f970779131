import { Schema, model, models } from 'mongoose'

const WebhookEventSchema = new Schema(
  {
    // Zoho webhook data
    event_id: {
      type: String,
      required: true,
    },
    event_type: {
      type: String,
      required: true,
      enum: [
        // Payment Session Events
        'payment.succeeded',
        'payment.failed',
        'payment.pending',
        'payment.cancelled',
        'payment_session.expired',
        // Payment Link Events
        'payment_link.paid',
        'payment_link.cancelled',
        'payment_link.expired',
        // Legacy Events (for backward compatibility)
        'payment_success',
        'payment_failed',
        'payment_pending',
        'refund_success',
        'refund_failed',
        'session_expired',
        'session_cancelled'
      ],
    },
    payment_session_id: {
      type: String,
      sparse: true, // Required for payment session events, not for payment link events
    },
    payment_link_id: {
      type: String,
      sparse: true, // Required for payment link events, not for payment session events
    },
    payment_id: {
      type: String,
      sparse: true, // Not all events have payment_id
    },
    
    // Payment details
    amount: {
      type: Number,
      required: true,
    },
    currency: {
      type: String,
      default: 'INR',
    },
    
    // Customer information
    customer_id: String,
    customer_email: String,
    
    // Transaction details
    transaction_id: String,
    invoice_number: String,
    reference_id: String,
    
    // Webhook metadata
    webhook_received_at: {
      type: Date,
      default: Date.now,
    },
    processed: {
      type: Boolean,
      default: false,
    },
    processed_at: Date,
    processing_attempts: {
      type: Number,
      default: 0,
    },
    
    // Raw webhook data
    raw_data: {
      type: Schema.Types.Mixed,
      required: true,
    },
    
    // Verification
    signature_verified: {
      type: Boolean,
      default: false,
    },
    
    // Error handling
    processing_error: String,
    retry_after: Date,

    // Monitoring fields
    processing_time_ms: {
      type: Number,
      default: 0,
    },
    response_status: {
      type: Number,
      default: 200,
    },
    client_ip: {
      type: String,
      default: 'unknown',
    },
    error_message: {
      type: String,
      sparse: true,
    },
  },
  {
    collection: 'WebhookEvents',
    timestamps: true,
  }
)

// Indexes for efficient queries
WebhookEventSchema.index({ event_id: 1 }, { unique: true })
WebhookEventSchema.index({ payment_session_id: 1 })
WebhookEventSchema.index({ event_type: 1 })
WebhookEventSchema.index({ processed: 1 })
WebhookEventSchema.index({ webhook_received_at: 1 })

// Methods
WebhookEventSchema.methods.markAsProcessed = function() {
  this.processed = true
  this.processed_at = new Date()
  return this.save()
}

WebhookEventSchema.methods.incrementProcessingAttempts = function() {
  this.processing_attempts += 1
  return this.save()
}

const WebhookEvent = models.WebhookEvent || model('WebhookEvent', WebhookEventSchema)

export default WebhookEvent
