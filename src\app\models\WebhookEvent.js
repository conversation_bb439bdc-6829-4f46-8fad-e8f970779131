import { Schema, model, models } from 'mongoose'

const WebhookEventSchema = new Schema(
  {
    // Zoho webhook data
    event_id: {
      type: String,
      required: true,
    },
    event_type: {
      type: String,
      required: true,
      enum: [
        // Payment Session Events
        'payment.succeeded',
        'payment.failed',
        'payment.pending',
        'payment.cancelled',
        'payment_session.expired',
        // Payment Link Events
        'payment_link.paid',
        'payment_link.cancelled',
        'payment_link.expired',
        // Legacy Events (for backward compatibility)
        'payment_success',
        'payment_failed',
        'payment_pending',
        'refund_success',
        'refund_failed',
        'session_expired',
        'session_cancelled',
        // New event types (dot notation)
        'payment.succeeded',
        'payment.failed',
        'payment.pending',
        'payment.cancelled',
        'payment_session.expired',
        'refund.succeeded',
        'refund.failed',
        // Payment link specific events
        'payment_link.created',
        'payment_link.expired',
        'payment_link.completed',
      ],
    },
    payment_session_id: {
      type: String,
      sparse: true, // Required for payment session events, not for payment link events
    },
    payment_link_id: {
      type: String,
      sparse: true, // Required for payment link events, not for payment session events
    },
    payment_id: {
      type: String,
      sparse: true, // Not all events have payment_id
    },

    // Payment details
    amount: {
      type: Number,
      required: true,
    },
    currency: {
      type: String,
      default: 'INR',
    },

    // Customer information
    customer_id: String,
    customer_email: String,

    // Transaction details
    transaction_id: String,
    invoice_number: String,
    reference_id: String,

    // Webhook metadata
    webhook_id: {
      type: String,
      required: true,
    },
    webhook_received_at: {
      type: Date,
      default: Date.now,
    },
    webhook_timestamp: {
      type: Date,
      required: true,
    },
    source_ip: {
      type: String,
      required: true,
    },
    user_agent: {
      type: String,
    },

    // Processing status
    processed: {
      type: Boolean,
      default: false,
    },
    processed_at: Date,
    processing_status: {
      type: String,
      enum: ['processing', 'completed', 'failed', 'duplicate', 'retry_pending'],
      default: 'processing',
    },
    processing_attempts: {
      type: Number,
      default: 0,
    },
    processing_time_ms: {
      type: Number,
    },

    // Raw webhook data
    raw_data: {
      type: Schema.Types.Mixed,
      required: true,
    },
    signature: {
      type: String,
    },

    // Security verification flags
    signature_verified: {
      type: Boolean,
      default: false,
    },
    ip_whitelisted: {
      type: Boolean,
      default: false,
    },
    timestamp_valid: {
      type: Boolean,
      default: false,
    },

    // Error handling and retry logic
    processing_error: String,
    error_stack: String,
    retry_after: Date,

    // Monitoring fields
    processing_time_ms: {
      type: Number,
      default: 0,
    },
    response_status: {
      type: Number,
      default: 200,
    },
    client_ip: {
      type: String,
      default: 'unknown',
    },
    error_message: {
      type: String,
      sparse: true,
    },
  },
  {
    collection: 'WebhookEvents',
    timestamps: true,
  }
)

// Custom validation to ensure either payment_session_id or payment_link_id is provided
WebhookEventSchema.pre('validate', function (next) {
  if (!this.payment_session_id && !this.payment_link_id) {
    this.invalidate('payment_session_id', 'Either payment_session_id or payment_link_id must be provided')
    this.invalidate('payment_link_id', 'Either payment_session_id or payment_link_id must be provided')
  }
  next()
})

// Indexes for efficient queries
// Note: Using background: true and explicit names to prevent conflicts
WebhookEventSchema.index(
  { event_id: 1 },
  {
    unique: true,
    background: true,
    name: 'event_id_unique',
  }
)
WebhookEventSchema.index(
  { payment_session_id: 1 },
  {
    background: true,
    name: 'payment_session_id_webhook',
  }
)
WebhookEventSchema.index(
  { payment_link_id: 1 },
  {
    background: true,
    name: 'payment_link_id_webhook',
  }
)
WebhookEventSchema.index({ event_type: 1 }, { background: true })
WebhookEventSchema.index({ processed: 1 }, { background: true })
WebhookEventSchema.index({ processing_status: 1 }, { background: true })
WebhookEventSchema.index({ source_ip: 1 }, { background: true })
WebhookEventSchema.index({ retry_after: 1 }, { background: true })

// Compound indexes for efficient queries
WebhookEventSchema.index({ event_type: 1, webhook_received_at: -1 }, { background: true })
WebhookEventSchema.index({ processing_status: 1, webhook_received_at: -1 }, { background: true })
WebhookEventSchema.index({ payment_session_id: 1, event_type: 1 }, { background: true })
WebhookEventSchema.index({ payment_link_id: 1, event_type: 1 }, { background: true })

// TTL index to automatically delete old webhook events (90 days)
// This also serves as the main index for webhook_received_at
WebhookEventSchema.index(
  { webhook_received_at: 1 },
  {
    expireAfterSeconds: 90 * 24 * 60 * 60,
    background: true,
    name: 'webhook_received_at_ttl',
  }
)

// Instance methods
WebhookEventSchema.methods.markAsProcessed = function () {
  this.processed = true
  this.processed_at = new Date()
  this.processing_status = 'completed'
  return this.save()
}

WebhookEventSchema.methods.markAsCompleted = function (transactionId, finalStatus, processingTimeMs) {
  this.processed = true
  this.processed_at = new Date()
  this.processing_status = 'completed'
  this.transaction_id = transactionId
  this.processing_time_ms = processingTimeMs
  return this.save()
}

WebhookEventSchema.methods.markAsFailed = function (error, processingTimeMs) {
  this.processing_status = 'failed'
  this.processed_at = new Date()
  this.processing_error = error.message
  this.error_stack = error.stack
  this.processing_time_ms = processingTimeMs
  this.processing_attempts += 1
  return this.save()
}

WebhookEventSchema.methods.markAsDuplicate = function () {
  this.processing_status = 'duplicate'
  this.processed_at = new Date()
  return this.save()
}

WebhookEventSchema.methods.incrementProcessingAttempts = function () {
  this.processing_attempts += 1
  return this.save()
}

WebhookEventSchema.methods.scheduleRetry = function (delayMs) {
  this.retry_count += 1
  this.retry_after = new Date(Date.now() + delayMs)
  this.processing_status = 'retry_pending'
  return this.save()
}

WebhookEventSchema.methods.canRetry = function () {
  return this.retry_count < this.max_retries && this.processing_status !== 'completed'
}

// Static methods
WebhookEventSchema.statics.findByEventId = function (eventId) {
  return this.findOne({ event_id: eventId })
}

WebhookEventSchema.statics.findFailedEvents = function (limit = 100) {
  return this.find({ processing_status: 'failed' }).sort({ webhook_received_at: -1 }).limit(limit)
}

WebhookEventSchema.statics.findRetryableEvents = function () {
  return this.find({
    processing_status: 'retry_pending',
    retry_after: { $lte: new Date() },
    retry_count: { $lt: 3 },
  }).sort({ retry_after: 1 })
}

WebhookEventSchema.statics.getProcessingStats = function (hours = 24) {
  const since = new Date(Date.now() - hours * 60 * 60 * 1000)
  return this.aggregate([
    { $match: { webhook_received_at: { $gte: since } } },
    {
      $group: {
        _id: '$processing_status',
        count: { $sum: 1 },
        avgProcessingTime: { $avg: '$processing_time_ms' },
      },
    },
  ])
}

const WebhookEvent = models.WebhookEvent || model('WebhookEvent', WebhookEventSchema)

export default WebhookEvent
