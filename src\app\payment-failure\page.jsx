'use client'

import { useRouter, useSearchParams } from 'next/navigation'
import { Suspense, useEffect } from 'react'

function PaymentFailurePageContent() {
  const searchParams = useSearchParams()
  const router = useRouter()

  const errorMessage = searchParams.get('error') || 'Payment failed'
  const invoiceNumber = searchParams.get('invoiceNumber')
  const sessionId = searchParams.get('sessionId')

  useEffect(() => {
    // Log the failure for debugging
    console.log('Payment failure page loaded:', {
      error: errorMessage,
      invoiceNumber,
      sessionId,
      timestamp: new Date().toISOString()
    })
  }, [errorMessage, invoiceNumber, sessionId])

  const handleRetryPayment = () => {
    // For mobile WebView, we can't navigate back to Flutter app directly
    // The Flutter app should handle the WebView navigation
    window.history.back()
  }

  const handleContactSupport = () => {
    // For mobile WebView, we'll show contact information
    alert('Please contact <NAME_EMAIL> for assistance with your payment.')
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <div className="rounded-lg bg-white p-8 shadow-lg">
          {/* Error Icon */}
          <div className="text-center">
            <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
              <svg className="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>

            <h2 className="mt-6 text-2xl font-bold text-gray-900">Payment Failed</h2>
            <p className="mt-2 text-sm text-gray-600">
              We couldn&apos;t process your payment. Please try again.
            </p>
          </div>

          {/* Error Details */}
          <div className="mt-6 rounded-md bg-red-50 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error Details</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{errorMessage}</p>
                  {invoiceNumber && (
                    <p className="mt-1">
                      <span className="font-medium">Invoice:</span> {invoiceNumber}
                    </p>
                  )}
                  {sessionId && (
                    <p className="mt-1 text-xs text-red-600">
                      <span className="font-medium">Session ID:</span> {sessionId}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Common Reasons */}
          <div className="mt-6">
            <h3 className="text-sm font-medium text-gray-900">Common reasons for payment failure:</h3>
            <ul className="mt-2 text-sm text-gray-600 space-y-1">
              <li>• Insufficient funds in your account</li>
              <li>• Incorrect card details</li>
              <li>• Network connectivity issues</li>
              <li>• Bank security restrictions</li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="mt-8 space-y-3">
            <button
              onClick={handleRetryPayment}
              className="flex w-full justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Try Again
            </button>

            <button
              onClick={handleContactSupport}
              className="flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Contact Support
            </button>
          </div>

          {/* Footer */}
          <div className="mt-6 text-center">
            <p className="text-xs text-gray-500">
              If you continue to experience issues, please contact our support team.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function PaymentFailurePage() {
  return (
    <Suspense
      fallback={
        <div className="flex min-h-screen items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="mx-auto h-12 w-12 animate-spin rounded-full border-b-2 border-red-600"></div>
            <p className="mt-4 text-gray-600">Loading...</p>
          </div>
        </div>
      }
    >
      <PaymentFailurePageContent />
    </Suspense>
  )
}
