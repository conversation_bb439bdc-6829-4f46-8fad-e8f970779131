/**
 * Comprehensive Zoho Payment API Production Test
 * 
 * Tests all critical components in production environment:
 * 1. API connectivity to Zoho's production endpoints
 * 2. Payment session creation with test/sandbox credentials
 * 3. Database connectivity validation
 * 4. Environment variables configuration
 * 5. Error handling scenarios
 * 6. Token management and authentication flow
 */

const fetch = globalThis.fetch || require('node-fetch')

const PRODUCTION_URL = 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net'

class ProductionTestSuite {
  constructor() {
    this.results = {
      connectivity: null,
      database: null,
      environment: null,
      authentication: null,
      payment_session: null,
      error_handling: null,
      performance: []
    }
  }

  async runAllTests() {
    console.log('🚀 COMPREHENSIVE ZOHO PAYMENT API PRODUCTION TEST')
    console.log('=' .repeat(60))
    console.log(`Production URL: ${PRODUCTION_URL}`)
    console.log(`Test Started: ${new Date().toISOString()}`)
    console.log('=' .repeat(60))

    try {
      // Test 1: API Connectivity
      await this.testAPIConnectivity()
      
      // Test 2: Database Connectivity
      await this.testDatabaseConnectivity()
      
      // Test 3: Environment Variables
      await this.testEnvironmentConfiguration()
      
      // Test 4: Authentication & Token Management
      await this.testAuthenticationFlow()
      
      // Test 5: Payment Session Creation
      await this.testPaymentSessionCreation()
      
      // Test 6: Error Handling Scenarios
      await this.testErrorHandling()
      
      // Generate comprehensive report
      this.generateReport()
      
    } catch (error) {
      console.error('💥 Test suite execution failed:', error.message)
      process.exit(1)
    }
  }

  async testAPIConnectivity() {
    console.log('\n📡 TEST 1: API Connectivity')
    console.log('-'.repeat(40))
    
    const startTime = Date.now()
    
    try {
      const response = await fetch(`${PRODUCTION_URL}/api/zoho/health`, {
        method: 'GET',
        headers: {
          'User-Agent': 'AquaPartner-ProductionTest/1.0',
          'Cache-Control': 'no-cache'
        }
      })
      
      const endTime = Date.now()
      const responseTime = endTime - startTime
      
      console.log(`⏱️  Health Check Response Time: ${responseTime}ms`)
      console.log(`📊 HTTP Status: ${response.status} ${response.statusText}`)
      
      if (response.ok) {
        const healthData = await response.json()
        console.log('✅ API Connectivity: SUCCESS')
        console.log(`🔗 Zoho API Status: ${healthData.zoho_api_status || 'Unknown'}`)
        console.log(`💾 Database Status: ${healthData.database_status || 'Unknown'}`)
        
        this.results.connectivity = {
          success: true,
          response_time: responseTime,
          status: response.status,
          health_data: healthData
        }
      } else {
        throw new Error(`Health check failed with status ${response.status}`)
      }
      
    } catch (error) {
      console.log('❌ API Connectivity: FAILED')
      console.log(`   Error: ${error.message}`)
      
      this.results.connectivity = {
        success: false,
        error: error.message
      }
    }
  }

  async testDatabaseConnectivity() {
    console.log('\n💾 TEST 2: Database Connectivity')
    console.log('-'.repeat(40))
    
    const startTime = Date.now()
    
    try {
      // Test database through a simple API call that requires DB access
      const response = await fetch(`${PRODUCTION_URL}/api/zoho/payments/create-session`, {
        method: 'GET', // This should return requirements, testing DB connection
        headers: {
          'User-Agent': 'AquaPartner-ProductionTest/1.0'
        }
      })
      
      const endTime = Date.now()
      const responseTime = endTime - startTime
      
      if (response.ok) {
        const data = await response.json()
        console.log('✅ Database Connectivity: SUCCESS')
        console.log(`⏱️  DB Response Time: ${responseTime}ms`)
        console.log(`📋 API Requirements Retrieved: ${data.required_fields?.length || 0} fields`)
        
        this.results.database = {
          success: true,
          response_time: responseTime,
          requirements_loaded: !!data.required_fields
        }
      } else {
        throw new Error(`Database test failed with status ${response.status}`)
      }
      
    } catch (error) {
      console.log('❌ Database Connectivity: FAILED')
      console.log(`   Error: ${error.message}`)
      
      this.results.database = {
        success: false,
        error: error.message
      }
    }
  }

  async testEnvironmentConfiguration() {
    console.log('\n🔧 TEST 3: Environment Configuration')
    console.log('-'.repeat(40))
    
    try {
      const response = await fetch(`${PRODUCTION_URL}/api/zoho/health`, {
        method: 'GET'
      })
      
      if (response.ok) {
        const healthData = await response.json()
        
        console.log('✅ Environment Configuration: SUCCESS')
        console.log(`🔑 Environment: ${healthData.environment || 'production'}`)
        console.log(`⚙️  Configuration Status: ${healthData.configuration?.status || 'Unknown'}`)
        
        // Check for required environment indicators
        const hasRequiredConfig = healthData.configuration && 
                                 healthData.zoho_api_status && 
                                 healthData.database_status
        
        this.results.environment = {
          success: true,
          environment: healthData.environment,
          configuration_complete: hasRequiredConfig,
          health_data: healthData
        }
      } else {
        throw new Error('Environment configuration check failed')
      }
      
    } catch (error) {
      console.log('❌ Environment Configuration: FAILED')
      console.log(`   Error: ${error.message}`)
      
      this.results.environment = {
        success: false,
        error: error.message
      }
    }
  }

  async testAuthenticationFlow() {
    console.log('\n🔐 TEST 4: Authentication & Token Management')
    console.log('-'.repeat(40))
    
    const startTime = Date.now()
    
    try {
      // Test authentication by attempting to create a payment session
      // This will validate token retrieval and authentication flow
      const testPayload = {
        amount: 1.0,
        currency: 'INR',
        description: 'Authentication Test Payment',
        invoice_number: `AUTH_TEST_${Date.now()}`,
        customer_id: `auth_customer_${Date.now()}`,
        customer_name: 'Auth Test Customer',
        customer_email: '<EMAIL>',
        customer_phone: '+91-9876543210'
      }
      
      const response = await fetch(`${PRODUCTION_URL}/api/zoho/payments/create-session`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'AquaPartner-ProductionTest/1.0'
        },
        body: JSON.stringify(testPayload)
      })
      
      const endTime = Date.now()
      const responseTime = endTime - startTime
      
      const responseData = await response.json()
      
      if (response.ok && responseData.success) {
        console.log('✅ Authentication Flow: SUCCESS')
        console.log(`🔑 Token Management: Working`)
        console.log(`⏱️  Auth Response Time: ${responseTime}ms`)
        console.log(`💳 Test Session Created: ${responseData.data.payment_session_id}`)
        
        this.results.authentication = {
          success: true,
          response_time: responseTime,
          token_management: 'working',
          test_session_id: responseData.data.payment_session_id
        }
      } else if (responseData.error && responseData.error.includes('token')) {
        console.log('❌ Authentication Flow: TOKEN ERROR')
        console.log(`   Token Issue: ${responseData.message}`)
        
        this.results.authentication = {
          success: false,
          error: 'token_error',
          message: responseData.message
        }
      } else {
        console.log('⚠️  Authentication Flow: PARTIAL SUCCESS')
        console.log(`   Response: ${responseData.message || 'Unknown response'}`)
        
        this.results.authentication = {
          success: false,
          error: 'authentication_partial',
          response: responseData
        }
      }
      
    } catch (error) {
      console.log('❌ Authentication Flow: FAILED')
      console.log(`   Error: ${error.message}`)
      
      this.results.authentication = {
        success: false,
        error: error.message
      }
    }
  }

  async testPaymentSessionCreation() {
    console.log('\n💳 TEST 5: Payment Session Creation')
    console.log('-'.repeat(40))
    
    const startTime = Date.now()
    
    // Test payment data with sandbox/test credentials
    const paymentData = {
      amount: 1.0, // Safe test amount
      currency: 'INR',
      description: 'Production Test Payment Session - Sandbox Mode',
      invoice_number: `PROD_TEST_${Date.now()}`,
      customer_id: `prod_customer_${Date.now()}`,
      customer_name: 'Production Test Customer',
      customer_email: '<EMAIL>',
      customer_phone: '+91-9876543210',
      redirect_url: `${PRODUCTION_URL}/payment/success`,
      reference_id: `PROD_REF_${Date.now()}`,
      meta_data: [
        { key: 'test_type', value: 'production_validation' },
        { key: 'environment', value: 'production' },
        { key: 'amount_safe', value: 'true' },
        { key: 'sandbox_mode', value: 'true' }
      ]
    }

    try {
      const response = await fetch(`${PRODUCTION_URL}/api/zoho/payments/create-session`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'AquaPartner-ProductionTest/1.0'
        },
        body: JSON.stringify(paymentData)
      })

      const endTime = Date.now()
      const responseTime = endTime - startTime

      const responseData = await response.json()

      console.log(`⏱️  Payment Session Response Time: ${responseTime}ms`)
      console.log(`📊 HTTP Status: ${response.status}`)

      if (response.ok && responseData.success && responseData.data.payment_session_id) {
        console.log('✅ Payment Session Creation: SUCCESS')
        console.log(`💳 Session ID: ${responseData.data.payment_session_id}`)
        console.log(`💰 Amount: ₹${responseData.data.amount}`)
        console.log(`🏷️  Invoice: ${responseData.data.invoice_number}`)
        console.log(`📅 Created: ${new Date(responseData.data.created_time * 1000).toISOString()}`)

        this.results.payment_session = {
          success: true,
          response_time: responseTime,
          session_id: responseData.data.payment_session_id,
          amount: responseData.data.amount,
          currency: responseData.data.currency,
          created_time: responseData.data.created_time
        }
      } else {
        console.log('❌ Payment Session Creation: FAILED')
        console.log(`   Error: ${responseData.error || 'Unknown error'}`)
        console.log(`   Message: ${responseData.message || 'No message'}`)

        this.results.payment_session = {
          success: false,
          error: responseData.error,
          message: responseData.message,
          response_time: responseTime
        }
      }

    } catch (error) {
      console.log('❌ Payment Session Creation: FAILED')
      console.log(`   Error: ${error.message}`)

      this.results.payment_session = {
        success: false,
        error: error.message
      }
    }
  }

  async testErrorHandling() {
    console.log('\n🚨 TEST 6: Error Handling Scenarios')
    console.log('-'.repeat(40))

    const errorTests = [
      {
        name: 'Missing Required Fields',
        payload: { amount: 100 }, // Missing required fields
        expectedError: 'validation_error'
      },
      {
        name: 'Invalid Amount',
        payload: {
          amount: -50, // Invalid negative amount
          description: 'Invalid amount test',
          invoice_number: 'INV-INVALID',
          customer_id: 'CUST-INVALID'
        },
        expectedError: 'invalid_amount'
      },
      {
        name: 'Invalid Currency',
        payload: {
          amount: 100,
          currency: 'USD', // Only INR supported
          description: 'Invalid currency test',
          invoice_number: 'INV-CURRENCY',
          customer_id: 'CUST-CURRENCY'
        },
        expectedError: 'invalid_currency'
      }
    ]

    const errorResults = []

    for (const test of errorTests) {
      console.log(`\n   Testing: ${test.name}`)

      try {
        const startTime = Date.now()

        const response = await fetch(`${PRODUCTION_URL}/api/zoho/payments/create-session`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'AquaPartner-ProductionTest/1.0'
          },
          body: JSON.stringify(test.payload)
        })

        const endTime = Date.now()
        const responseTime = endTime - startTime

        const responseData = await response.json()

        if (!response.ok && responseData.error) {
          console.log(`   ✅ Error handled correctly: ${responseData.error}`)
          errorResults.push({
            test: test.name,
            success: true,
            error_type: responseData.error,
            message: responseData.message,
            response_time: responseTime
          })
        } else {
          console.log(`   ❌ Error not handled: Expected error but got success`)
          errorResults.push({
            test: test.name,
            success: false,
            issue: 'Expected error but got success',
            response: responseData
          })
        }

      } catch (error) {
        console.log(`   ⚠️  Network error: ${error.message}`)
        errorResults.push({
          test: test.name,
          success: false,
          network_error: error.message
        })
      }
    }

    const successfulErrorTests = errorResults.filter(r => r.success).length
    console.log(`\n📊 Error Handling Summary: ${successfulErrorTests}/${errorTests.length} tests passed`)

    this.results.error_handling = {
      total_tests: errorTests.length,
      successful_tests: successfulErrorTests,
      test_results: errorResults
    }
  }

  generateReport() {
    console.log('\n' + '='.repeat(60))
    console.log('📋 COMPREHENSIVE PRODUCTION TEST REPORT')
    console.log('='.repeat(60))

    const testResults = [
      { name: 'API Connectivity', result: this.results.connectivity },
      { name: 'Database Connectivity', result: this.results.database },
      { name: 'Environment Configuration', result: this.results.environment },
      { name: 'Authentication & Token Management', result: this.results.authentication },
      { name: 'Payment Session Creation', result: this.results.payment_session },
      { name: 'Error Handling', result: this.results.error_handling }
    ]

    let totalTests = 0
    let passedTests = 0

    testResults.forEach(test => {
      totalTests++
      const status = test.result?.success ? '✅ PASS' : '❌ FAIL'
      console.log(`${status} ${test.name}`)

      if (test.result?.success) {
        passedTests++
        if (test.result.response_time) {
          console.log(`     Response Time: ${test.result.response_time}ms`)
        }
      } else if (test.result?.error) {
        console.log(`     Error: ${test.result.error}`)
      }
    })

    console.log('\n' + '-'.repeat(60))
    console.log(`📊 OVERALL RESULTS: ${passedTests}/${totalTests} tests passed`)
    console.log(`🎯 Success Rate: ${Math.round((passedTests/totalTests) * 100)}%`)

    // Performance Summary
    const responseTimes = []
    testResults.forEach(test => {
      if (test.result?.response_time) {
        responseTimes.push(test.result.response_time)
      }
    })

    if (responseTimes.length > 0) {
      const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length
      const maxResponseTime = Math.max(...responseTimes)
      const minResponseTime = Math.min(...responseTimes)

      console.log('\n⚡ PERFORMANCE METRICS:')
      console.log(`   Average Response Time: ${Math.round(avgResponseTime)}ms`)
      console.log(`   Fastest Response: ${minResponseTime}ms`)
      console.log(`   Slowest Response: ${maxResponseTime}ms`)
    }

    // Critical Issues
    const criticalIssues = []
    if (!this.results.connectivity?.success) criticalIssues.push('API Connectivity Failed')
    if (!this.results.database?.success) criticalIssues.push('Database Connectivity Failed')
    if (!this.results.authentication?.success) criticalIssues.push('Authentication Failed')

    if (criticalIssues.length > 0) {
      console.log('\n🚨 CRITICAL ISSUES:')
      criticalIssues.forEach(issue => console.log(`   • ${issue}`))
    }

    // Recommendations
    console.log('\n💡 RECOMMENDATIONS:')
    if (this.results.payment_session?.success) {
      console.log('   ✅ Payment API is ready for production use')
      console.log('   ✅ All critical components are functioning correctly')
    } else {
      console.log('   ⚠️  Payment API requires attention before production use')
    }

    if (responseTimes.some(time => time > 5000)) {
      console.log('   ⚠️  Some responses are slow (>5s) - consider optimization')
    }

    console.log('\n' + '='.repeat(60))
    console.log(`🏁 Test Completed: ${new Date().toISOString()}`)
    console.log('='.repeat(60))

    // Exit with appropriate code
    if (passedTests === totalTests) {
      console.log('🎉 ALL TESTS PASSED - Production environment is ready!')
      process.exit(0)
    } else {
      console.log('❌ SOME TESTS FAILED - Review issues before production use')
      process.exit(1)
    }
  }
}

// Run the comprehensive test suite
if (typeof require !== 'undefined' && require.main === module) {
  const testSuite = new ProductionTestSuite()
  testSuite.runAllTests().catch(error => {
    console.error('💥 Test suite failed to execute:', error.message)
    process.exit(1)
  })
}

module.exports = { ProductionTestSuite }
