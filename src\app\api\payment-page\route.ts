import { NextRequest, NextResponse } from 'next/server'
import path from 'path'
import fs from 'fs'

// Force dynamic rendering to prevent static generation
export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('sessionId')
    const amount = searchParams.get('amount')
    const currency = searchParams.get('currency') || 'INR'
    const description = searchParams.get('description') || 'Payment'
    const callbackUrl = searchParams.get('callbackUrl') || ''
    const invoiceNumber = searchParams.get('invoiceNumber') || ''
    const referenceNumber = searchParams.get('referenceNumber') || ''
    const customerName = searchParams.get('customerName') || ''
    const customerEmail = searchParams.get('customerEmail') || ''
    const customerPhone = searchParams.get('customerPhone') || ''
    const businessName = searchParams.get('businessName') || 'Your Business'

    if (!sessionId) {
      return new NextResponse('Missing sessionId parameter', { status: 400 })
    }

    // Read the HTML template
    const htmlPath = path.join(process.cwd(), 'public', 'payment-templates', 'zoho-payment.html')
    let htmlContent = fs.readFileSync(htmlPath, 'utf8')

    // Replace placeholders with actual values for official Zoho SDK
    htmlContent = htmlContent
      .replace(/{{SESSION_ID}}/g, sessionId)
      .replace(/{{AMOUNT}}/g, amount || '')
      .replace(/{{CURRENCY}}/g, currency)
      .replace(/{{DESCRIPTION}}/g, description)
      .replace(/{{CALLBACK_URL}}/g, callbackUrl)
      .replace(/{{API_DOMAIN}}/g, process.env.NEXT_PUBLIC_API_DOMAIN || '')
      .replace(/{{ACCOUNT_ID}}/g, process.env.ZOHO_PAY_ACCOUNT_ID || '')
      .replace(/{{API_KEY}}/g, process.env.ZOHO_PAY_API_KEY || '')
      .replace(/{{BUSINESS_NAME}}/g, businessName)
      .replace(/{{INVOICE_NUMBER}}/g, invoiceNumber)
      .replace(/{{REFERENCE_NUMBER}}/g, referenceNumber)
      .replace(/{{CUSTOMER_NAME}}/g, customerName)
      .replace(/{{CUSTOMER_EMAIL}}/g, customerEmail)
      .replace(/{{CUSTOMER_PHONE}}/g, customerPhone)

    return new NextResponse(htmlContent, {
      headers: {
        'Content-Type': 'text/html',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
  } catch (error) {
    console.error('Error serving payment page:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}
