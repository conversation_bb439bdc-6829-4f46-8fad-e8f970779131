/**
 * Rate Limiting Middleware for Next.js App Router
 * 
 * Production-grade rate limiting with Azure App Service compatibility
 * Supports in-memory storage with Redis-ready architecture for scaling
 */

// In-memory storage for rate limiting (Redis-ready interface)
class RateLimitStore {
  constructor() {
    this.store = new Map()
    this.cleanupInterval = setInterval(() => this.cleanup(), 60000) // Cleanup every minute
  }

  async get(key) {
    const data = this.store.get(key)
    if (!data) return null
    
    // Check if expired
    if (Date.now() > data.resetTime) {
      this.store.delete(key)
      return null
    }
    
    return data
  }

  async set(key, value, ttlMs) {
    this.store.set(key, {
      ...value,
      resetTime: Date.now() + ttlMs
    })
  }

  async increment(key, ttlMs) {
    const existing = await this.get(key)
    if (!existing) {
      await this.set(key, { count: 1 }, ttlMs)
      return { count: 1, resetTime: Date.now() + ttlMs }
    }
    
    existing.count++
    this.store.set(key, existing)
    return existing
  }

  cleanup() {
    const now = Date.now()
    for (const [key, data] of this.store.entries()) {
      if (now > data.resetTime) {
        this.store.delete(key)
      }
    }
  }

  // Redis-compatible interface for future migration
  async redis() {
    // Future: return Redis client instance
    return this
  }
}

// Global store instance
const rateLimitStore = new RateLimitStore()

/**
 * Rate limiting configuration
 */
const RATE_LIMIT_CONFIGS = {
  // Payment operations - stricter limits
  payment_operations: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100,
    skipSuccessful: false,
    message: 'Too many payment requests. Please try again later.',
    endpoints: ['/api/zoho/payments/', '/api/zoho/webhooks/', '/api/zoho/refunds/']
  },
  
  // Status and health checks - more permissive
  status_operations: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 500,
    skipSuccessful: true,
    message: 'Too many status requests. Please try again later.',
    endpoints: ['/api/zoho/health', '/api/zoho/payments/status/']
  },
  
  // Webhook operations - special handling for Zoho
  webhook_operations: {
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 200,
    skipSuccessful: true,
    message: 'Webhook rate limit exceeded.',
    endpoints: ['/api/zoho/webhooks/']
  }
}

/**
 * Extract client IP from request headers (Azure App Service compatible)
 */
function getClientIP(request) {
  // Azure App Service proxy headers
  const forwardedFor = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const azureClientIP = request.headers.get('x-azure-clientip')
  
  // Parse X-Forwarded-For (can contain multiple IPs)
  if (forwardedFor) {
    const ips = forwardedFor.split(',').map(ip => ip.trim())
    return ips[0] // First IP is the original client
  }
  
  // Fallback to other headers
  if (realIP) return realIP
  if (azureClientIP) return azureClientIP
  
  // Development fallback
  return '127.0.0.1'
}

/**
 * Determine rate limit configuration based on request path
 */
function getRateLimitConfig(pathname) {
  for (const [configName, config] of Object.entries(RATE_LIMIT_CONFIGS)) {
    for (const endpoint of config.endpoints) {
      if (pathname.startsWith(endpoint)) {
        return { ...config, name: configName }
      }
    }
  }
  
  // Default configuration for other Zoho API endpoints
  return {
    ...RATE_LIMIT_CONFIGS.payment_operations,
    name: 'default',
    maxRequests: 200
  }
}

/**
 * Rate limiting middleware for Next.js App Router
 * 
 * @param {Object} options - Rate limiting options
 * @param {number} options.windowMs - Time window in milliseconds
 * @param {number} options.maxRequests - Maximum requests per window
 * @param {boolean} options.skipSuccessful - Skip counting successful requests
 * @param {string} options.message - Error message for rate limit exceeded
 * @returns {Function} Middleware function
 */
export function withRateLimit(options = {}) {
  return function rateLimitMiddleware(handler) {
    return async function(request, context) {
      // Check if rate limiting is enabled
      if (process.env.RATE_LIMIT_ENABLED === 'false') {
        return handler(request, context)
      }

      try {
        const pathname = new URL(request.url).pathname
        const config = options.windowMs ? options : getRateLimitConfig(pathname)
        const clientIP = getClientIP(request)
        
        // Create rate limit key
        const rateLimitKey = `rate_limit:${config.name}:${clientIP}`
        
        // Check current rate limit status
        const current = await rateLimitStore.increment(rateLimitKey, config.windowMs)
        
        // Add rate limit headers
        const resetTime = new Date(current.resetTime)
        const remaining = Math.max(0, config.maxRequests - current.count)
        
        // Check if limit exceeded
        if (current.count > config.maxRequests) {
          console.warn(`Rate limit exceeded for IP ${clientIP} on ${pathname}`, {
            method: 'rateLimitExceeded',
            ip: clientIP,
            path: pathname,
            count: current.count,
            limit: config.maxRequests,
            resetTime: resetTime.toISOString()
          })
          
          return new Response(
            JSON.stringify({
              error: 'Rate limit exceeded',
              message: config.message || 'Too many requests. Please try again later.',
              code: 'RATE_LIMIT_EXCEEDED',
              retry_after: Math.ceil((current.resetTime - Date.now()) / 1000),
              limit: config.maxRequests,
              remaining: 0,
              reset: resetTime.toISOString()
            }),
            {
              status: 429,
              headers: {
                'Content-Type': 'application/json',
                'X-RateLimit-Limit': config.maxRequests.toString(),
                'X-RateLimit-Remaining': '0',
                'X-RateLimit-Reset': resetTime.toISOString(),
                'Retry-After': Math.ceil((current.resetTime - Date.now()) / 1000).toString()
              }
            }
          )
        }
        
        // Execute the original handler
        const response = await handler(request, context)
        
        // Add rate limit headers to successful responses
        if (response instanceof Response) {
          response.headers.set('X-RateLimit-Limit', config.maxRequests.toString())
          response.headers.set('X-RateLimit-Remaining', remaining.toString())
          response.headers.set('X-RateLimit-Reset', resetTime.toISOString())
        }
        
        return response
        
      } catch (error) {
        console.error('Rate limiting error:', error)
        // On rate limiting errors, allow the request to proceed
        return handler(request, context)
      }
    }
  }
}

/**
 * Get rate limit status for monitoring
 */
export async function getRateLimitStatus() {
  const stats = {
    total_keys: rateLimitStore.store.size,
    configs: Object.keys(RATE_LIMIT_CONFIGS),
    enabled: process.env.RATE_LIMIT_ENABLED !== 'false',
    store_type: 'memory', // Will be 'redis' when migrated
    cleanup_interval: '60s'
  }
  
  return stats
}

/**
 * Reset rate limit for specific IP (admin function)
 */
export async function resetRateLimit(ip, configName = null) {
  if (configName) {
    const key = `rate_limit:${configName}:${ip}`
    rateLimitStore.store.delete(key)
  } else {
    // Reset all rate limits for IP
    for (const configName of Object.keys(RATE_LIMIT_CONFIGS)) {
      const key = `rate_limit:${configName}:${ip}`
      rateLimitStore.store.delete(key)
    }
  }
}

export default withRateLimit
