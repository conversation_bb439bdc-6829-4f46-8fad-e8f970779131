/**
 * GitHub Actions Workflow Verification Script
 * 
 * Verifies the Azure App Service deployment workflow configuration
 */

const fs = require('fs')
const yaml = require('js-yaml')

class WorkflowVerification {
  constructor() {
    this.workflowPath = '.github/workflows/azure-app-service-deploy.yml'
    this.issues = []
    this.successes = []
  }

  log(type, message) {
    if (type === 'success') {
      console.log(`✅ ${message}`)
      this.successes.push(message)
    } else {
      console.log(`❌ ${message}`)
      this.issues.push(message)
    }
  }

  verifyWorkflowExists() {
    if (fs.existsSync(this.workflowPath)) {
      this.log('success', 'Workflow file exists')
      return true
    } else {
      this.log('error', 'Workflow file not found')
      return false
    }
  }

  verifyWorkflowSyntax() {
    try {
      const content = fs.readFileSync(this.workflowPath, 'utf8')
      const workflow = yaml.load(content)
      this.log('success', 'Workflow YAML syntax is valid')
      return workflow
    } catch (error) {
      this.log('error', `Workflow YAML syntax error: ${error.message}`)
      return null
    }
  }

  verifyJobStructure(workflow) {
    if (!workflow.jobs) {
      this.log('error', 'No jobs defined in workflow')
      return false
    }

    if (!workflow.jobs.build_and_deploy) {
      this.log('error', 'build_and_deploy job not found')
      return false
    }

    this.log('success', 'Job structure is correct')
    return true
  }

  verifySteps(workflow) {
    const job = workflow.jobs.build_and_deploy
    if (!job.steps || !Array.isArray(job.steps)) {
      this.log('error', 'No steps defined in build_and_deploy job')
      return false
    }

    const requiredSteps = [
      'actions/checkout',
      'actions/setup-node',
      'npm ci',
      'npm run build',
      'azure/webapps-deploy'
    ]

    const stepNames = job.steps.map(step => {
      if (step.uses) return step.uses.split('@')[0]
      if (step.run && step.run.includes('npm ci')) return 'npm ci'
      if (step.run && step.run.includes('npm run build')) return 'npm run build'
      return step.name || 'unnamed'
    })

    let allStepsFound = true
    requiredSteps.forEach(requiredStep => {
      if (stepNames.some(step => step.includes(requiredStep))) {
        this.log('success', `Required step found: ${requiredStep}`)
      } else {
        this.log('error', `Required step missing: ${requiredStep}`)
        allStepsFound = false
      }
    })

    return allStepsFound
  }

  verifyEnvironmentVariables(workflow) {
    const content = fs.readFileSync(this.workflowPath, 'utf8')
    
    const requiredSecrets = [
      'AZURE_PUBLISH_PROFILE',
      'MONGODB_URI',
      'ZOHO_PAYMENT_SESSION_URL',
      'ZOHO_PAY_ACCOUNT_ID',
      'ZOHO_PAY_API_KEY',
      'ZOHO_OAUTH_CLIENT_ID',
      'ZOHO_OAUTH_CLIENT_SECRET',
      'ZOHO_OAUTH_REFRESH_TOKEN',
      'ZOHO_WEBHOOK_SECRET'
    ]

    let allSecretsFound = true
    requiredSecrets.forEach(secret => {
      if (content.includes(`secrets.${secret}`)) {
        this.log('success', `Required secret referenced: ${secret}`)
      } else {
        this.log('error', `Required secret missing: ${secret}`)
        allSecretsFound = false
      }
    })

    return allSecretsFound
  }

  verifyNoProblematicPatterns(workflow) {
    const content = fs.readFileSync(this.workflowPath, 'utf8')
    
    // Check for the problematic azure-credentials parameter
    if (content.includes('azure-credentials:')) {
      this.log('error', 'Problematic azure-credentials parameter found')
      return false
    }

    // Check for azure/appservice-settings action with azure-credentials
    if (content.includes('azure/appservice-settings') && content.includes('azure-credentials')) {
      this.log('error', 'azure/appservice-settings action has azure-credentials parameter')
      return false
    }

    this.log('success', 'No problematic authentication patterns found')
    return true
  }

  verifyDeploymentConfiguration(workflow) {
    const content = fs.readFileSync(this.workflowPath, 'utf8')
    
    // Check for web.config creation
    if (content.includes('Create web.config')) {
      this.log('success', 'web.config creation step found')
    } else {
      this.log('error', 'web.config creation step missing')
    }

    // Check for server.js creation
    if (content.includes('Create server.js')) {
      this.log('success', 'server.js creation step found')
    } else {
      this.log('error', 'server.js creation step missing')
    }

    // Check for .env.production creation
    if (content.includes('.env.production')) {
      this.log('success', '.env.production creation step found')
    } else {
      this.log('error', '.env.production creation step missing')
    }

    return true
  }

  verifyAppServiceConfiguration(workflow) {
    const content = fs.readFileSync(this.workflowPath, 'utf8')
    
    // Check for correct app name
    if (content.includes("app-name: 'aquapartner'")) {
      this.log('success', 'Correct Azure App Service name configured')
    } else {
      this.log('error', 'Azure App Service name not configured correctly')
    }

    // Check for publish profile usage
    if (content.includes('publish-profile: ${{ secrets.AZURE_PUBLISH_PROFILE }}')) {
      this.log('success', 'Publish profile authentication configured')
    } else {
      this.log('error', 'Publish profile authentication missing')
    }

    return true
  }

  async runAllVerifications() {
    console.log('🔍 GitHub Actions Workflow Verification')
    console.log('======================================')

    if (!this.verifyWorkflowExists()) {
      return this.generateReport()
    }

    const workflow = this.verifyWorkflowSyntax()
    if (!workflow) {
      return this.generateReport()
    }

    this.verifyJobStructure(workflow)
    this.verifySteps(workflow)
    this.verifyEnvironmentVariables(workflow)
    this.verifyNoProblematicPatterns(workflow)
    this.verifyDeploymentConfiguration(workflow)
    this.verifyAppServiceConfiguration(workflow)

    return this.generateReport()
  }

  generateReport() {
    console.log('\n======================================')
    console.log('📊 WORKFLOW VERIFICATION REPORT')
    console.log('======================================')

    const totalChecks = this.successes.length + this.issues.length
    const successRate = totalChecks > 0 ? ((this.successes.length / totalChecks) * 100).toFixed(1) : '0.0'

    console.log(`\n📈 SUMMARY`)
    console.log(`Total Checks: ${totalChecks}`)
    console.log(`Passed: ${this.successes.length}`)
    console.log(`Failed: ${this.issues.length}`)
    console.log(`Success Rate: ${successRate}%`)

    if (this.issues.length > 0) {
      console.log(`\n🚨 ISSUES FOUND:`)
      this.issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`)
      })
    }

    console.log(`\n🎯 WORKFLOW STATUS`)
    if (successRate >= 90) {
      console.log('✅ READY FOR DEPLOYMENT - Workflow configuration is excellent')
    } else if (successRate >= 70) {
      console.log('⚠️  NEEDS ATTENTION - Some issues should be resolved')
    } else {
      console.log('❌ NOT READY - Critical issues must be fixed')
    }

    return {
      totalChecks,
      passed: this.successes.length,
      failed: this.issues.length,
      successRate: parseFloat(successRate),
      issues: this.issues,
      successes: this.successes
    }
  }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { WorkflowVerification }
}

// Run verification if this file is executed directly
if (typeof require !== 'undefined' && require.main === module) {
  const verifier = new WorkflowVerification()
  verifier.runAllVerifications().catch(console.error)
}
