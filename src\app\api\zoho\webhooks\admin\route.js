/**
 * Webhook Administration API
 *
 * Endpoints for managing and monitoring webhook processing:
 * - GET: Get webhook statistics and status
 * - POST: Trigger manual retry processing
 * - PUT: Retry specific webhook event
 */

import connectedDB from '@/app/config/database'
import { Logger } from '@/app/lib/monitoring'
import { withRateLimit } from '@/app/lib/rateLimiting'
import { getWebhookStats, processWebhookRetries, retryWebhookEvent } from '@/app/lib/webhookRetryProcessor'
import WebhookEvent from '@/app/models/WebhookEvent'
import { NextResponse } from 'next/server'

/**
 * GET /api/zoho/webhooks/admin
 * Get webhook processing statistics and status
 */
async function handleAdminGET(request) {
  try {
    await connectedDB()

    const url = new URL(request.url)
    const hours = parseInt(url.searchParams.get('hours')) || 24
    const includeDetails = url.searchParams.get('details') === 'true'

    Logger.info('Webhook admin stats requested', {
      method: 'handleAdminGET',
      hours,
      includeDetails,
    })

    const stats = await getWebhookStats(hours)

    if (includeDetails) {
      // Add additional detailed information
      const recentEvents = await WebhookEvent.find({})
        .sort({ webhook_received_at: -1 })
        .limit(20)
        .select(
          'webhook_id event_id event_type processing_status webhook_received_at processed_at processing_time_ms source_ip'
        )

      stats.recent_events = recentEvents
    }

    return NextResponse.json({
      success: true,
      data: stats,
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    Logger.error('Webhook admin GET failed', {
      method: 'handleAdminGET',
      error: error.message,
      stack: error.stack,
    })

    return NextResponse.json(
      {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/zoho/webhooks/admin
 * Trigger manual webhook retry processing
 */
async function handleAdminPOST(request) {
  try {
    await connectedDB()

    const body = await request.json()
    const { action } = body

    Logger.info('Webhook admin action requested', {
      method: 'handleAdminPOST',
      action,
    })

    switch (action) {
      case 'process_retries':
        const retryResult = await processWebhookRetries()

        Logger.info('Manual retry processing completed', {
          method: 'handleAdminPOST',
          action,
          result: retryResult,
        })

        return NextResponse.json({
          success: true,
          action,
          result: retryResult,
          timestamp: new Date().toISOString(),
        })

      case 'cleanup_old_events':
        // Clean up webhook events older than 90 days
        const cutoffDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)
        const deleteResult = await WebhookEvent.deleteMany({
          webhook_received_at: { $lt: cutoffDate },
          processing_status: { $in: ['completed', 'failed'] },
        })

        Logger.info('Old webhook events cleaned up', {
          method: 'handleAdminPOST',
          action,
          deletedCount: deleteResult.deletedCount,
        })

        return NextResponse.json({
          success: true,
          action,
          deleted_count: deleteResult.deletedCount,
          cutoff_date: cutoffDate.toISOString(),
          timestamp: new Date().toISOString(),
        })

      default:
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid action',
            valid_actions: ['process_retries', 'cleanup_old_events'],
            timestamp: new Date().toISOString(),
          },
          { status: 400 }
        )
    }
  } catch (error) {
    Logger.error('Webhook admin POST failed', {
      method: 'handleAdminPOST',
      error: error.message,
      stack: error.stack,
    })

    return NextResponse.json(
      {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/zoho/webhooks/admin
 * Retry specific webhook event
 */
async function handleAdminPUT(request) {
  try {
    await connectedDB()

    const body = await request.json()
    const { event_id } = body

    if (!event_id) {
      return NextResponse.json(
        {
          success: false,
          error: 'event_id is required',
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      )
    }

    Logger.info('Manual webhook retry requested', {
      method: 'handleAdminPUT',
      eventId: event_id,
    })

    const retryResult = await retryWebhookEvent(event_id)

    Logger.info('Manual webhook retry completed', {
      method: 'handleAdminPUT',
      eventId: event_id,
      success: retryResult.success,
    })

    const statusCode = retryResult.success ? 200 : 400

    return NextResponse.json(
      {
        ...retryResult,
        timestamp: new Date().toISOString(),
      },
      { status: statusCode }
    )
  } catch (error) {
    Logger.error('Webhook admin PUT failed', {
      method: 'handleAdminPUT',
      error: error.message,
      stack: error.stack,
    })

    return NextResponse.json(
      {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/zoho/webhooks/admin
 * Delete specific webhook event (for cleanup)
 */
async function handleAdminDELETE(request) {
  try {
    await connectedDB()

    const url = new URL(request.url)
    const eventId = url.searchParams.get('event_id')

    if (!eventId) {
      return NextResponse.json(
        {
          success: false,
          error: 'event_id parameter is required',
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      )
    }

    Logger.info('Webhook event deletion requested', {
      method: 'handleAdminDELETE',
      eventId,
    })

    const webhookEvent = await WebhookEvent.findByEventId(eventId)

    if (!webhookEvent) {
      return NextResponse.json(
        {
          success: false,
          error: 'Webhook event not found',
          event_id: eventId,
          timestamp: new Date().toISOString(),
        },
        { status: 404 }
      )
    }

    // Only allow deletion of completed or failed events
    if (!['completed', 'failed'].includes(webhookEvent.processing_status)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Can only delete completed or failed webhook events',
          event_id: eventId,
          current_status: webhookEvent.processing_status,
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      )
    }

    await WebhookEvent.deleteOne({ event_id: eventId })

    Logger.info('Webhook event deleted', {
      method: 'handleAdminDELETE',
      eventId,
      webhookId: webhookEvent.webhook_id,
    })

    return NextResponse.json({
      success: true,
      message: 'Webhook event deleted',
      event_id: eventId,
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    Logger.error('Webhook admin DELETE failed', {
      method: 'handleAdminDELETE',
      error: error.message,
      stack: error.stack,
    })

    return NextResponse.json(
      {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    )
  }
}

// Apply rate limiting to admin endpoints and export directly
export const GET = withRateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 50, // Lower limit for admin operations
  message: 'Too many admin requests. Please try again later.',
})(handleAdminGET)

export const POST = withRateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 20, // Very strict limit for admin POST operations
  message: 'Too many admin modification requests. Please try again later.',
})(handleAdminPOST)

export const PUT = withRateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 30, // Moderate limit for retry operations
  message: 'Too many retry requests. Please try again later.',
})(handleAdminPUT)

export const DELETE = withRateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 10, // Very strict limit for delete operations
  message: 'Too many delete requests. Please try again later.',
})(handleAdminDELETE)
