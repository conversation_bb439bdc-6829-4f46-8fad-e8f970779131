/**
 * Token and Zoho API Diagnostic Test
 * 
 * Diagnoses token issues and Zoho API connectivity problems
 */

const fetch = globalThis.fetch || require('node-fetch')

const PRODUCTION_URL = 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net'

async function runTokenDiagnostics() {
  console.log('🔍 TOKEN AND ZOHO API DIAGNOSTIC TEST')
  console.log('=' .repeat(50))
  console.log(`Production URL: ${PRODUCTION_URL}`)
  console.log(`Timestamp: ${new Date().toISOString()}`)
  console.log('=' .repeat(50))

  // Test 1: Check Health Endpoint
  console.log('\n🏥 TEST 1: Health Check Detailed Analysis')
  console.log('-'.repeat(40))
  
  try {
    const healthResponse = await fetch(`${PRODUCTION_URL}/api/zoho/health`, {
      method: 'GET',
      headers: {
        'User-Agent': 'AquaPartner-TokenDiagnostic/1.0'
      }
    })
    
    console.log(`📊 Health Status: ${healthResponse.status}`)
    
    if (healthResponse.ok) {
      const healthData = await healthResponse.json()
      console.log('✅ Health endpoint accessible')
      console.log('📋 Health Data:')
      console.log(JSON.stringify(healthData, null, 2))
    } else {
      console.log('❌ Health endpoint failed')
      const errorText = await healthResponse.text()
      console.log(`Error: ${errorText}`)
    }
  } catch (error) {
    console.log('💥 Health check failed:', error.message)
  }

  // Test 2: Test with minimal payload
  console.log('\n💳 TEST 2: Minimal Payment Session Test')
  console.log('-'.repeat(40))
  
  const minimalPayload = {
    amount: 1,
    description: 'Minimal test',
    invoice_number: `MIN_${Date.now()}`,
    customer_id: `min_${Date.now()}`
  }
  
  try {
    console.log('📋 Minimal Payload:')
    console.log(JSON.stringify(minimalPayload, null, 2))
    
    const response = await fetch(`${PRODUCTION_URL}/api/zoho/payments/create-session`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'AquaPartner-TokenDiagnostic/1.0'
      },
      body: JSON.stringify(minimalPayload)
    })
    
    console.log(`📊 Response Status: ${response.status}`)
    console.log('📋 Response Headers:')
    response.headers.forEach((value, key) => {
      console.log(`   ${key}: ${value}`)
    })
    
    const responseText = await response.text()
    console.log(`📄 Response Body:`)
    console.log(responseText)
    
    try {
      const responseData = JSON.parse(responseText)
      
      if (responseData.error && responseData.message) {
        console.log('\n🔍 Error Analysis:')
        console.log(`   Error Type: ${responseData.error}`)
        console.log(`   Message: ${responseData.message}`)
        console.log(`   Details: ${responseData.details || 'None'}`)
        
        // Check for specific error patterns
        if (responseData.message.includes('token')) {
          console.log('🚨 TOKEN ISSUE DETECTED')
          console.log('   This appears to be a token-related problem')
        } else if (responseData.message.includes('400')) {
          console.log('🚨 ZOHO API VALIDATION ERROR')
          console.log('   Zoho API is rejecting the request payload')
        } else if (responseData.message.includes('401')) {
          console.log('🚨 AUTHENTICATION ERROR')
          console.log('   Token is invalid or expired')
        } else if (responseData.message.includes('403')) {
          console.log('🚨 AUTHORIZATION ERROR')
          console.log('   Token lacks required permissions')
        }
      }
    } catch (parseError) {
      console.log('❌ Response is not valid JSON')
    }
    
  } catch (error) {
    console.log('💥 Minimal test failed:', error.message)
  }

  // Test 3: Check API Requirements
  console.log('\n📋 TEST 3: API Requirements Check')
  console.log('-'.repeat(40))
  
  try {
    const reqResponse = await fetch(`${PRODUCTION_URL}/api/zoho/payments/create-session`, {
      method: 'GET',
      headers: {
        'User-Agent': 'AquaPartner-TokenDiagnostic/1.0'
      }
    })
    
    if (reqResponse.ok) {
      const requirements = await reqResponse.json()
      console.log('✅ API requirements accessible')
      console.log(`📋 Required fields: ${requirements.required_fields?.length || 0}`)
      console.log(`📋 Optional fields: ${requirements.optional_fields?.length || 0}`)
      
      if (requirements.required_fields) {
        console.log('\n📝 Required Fields:')
        requirements.required_fields.forEach(field => {
          console.log(`   • ${field.field} (${field.type}): ${field.description}`)
        })
      }
    } else {
      console.log('❌ API requirements check failed')
    }
  } catch (error) {
    console.log('💥 Requirements check failed:', error.message)
  }

  // Test 4: Test with exact example payload
  console.log('\n🎯 TEST 4: Example Payload Test')
  console.log('-'.repeat(40))
  
  const examplePayload = {
    amount: 100.5,
    currency: 'INR',
    description: 'Payment for Order #12345',
    invoice_number: 'INV-12345',
    customer_id: 'CUST-001',
    customer_name: 'John Doe',
    customer_email: '<EMAIL>',
    customer_phone: '+919876543210',
    redirect_url: `${PRODUCTION_URL}/payment/success`,
    reference_id: 'REF-12345',
    meta_data: [
      { key: 'order_id', value: 'ORD-123' },
      { key: 'product_type', value: 'aquaculture' }
    ]
  }
  
  try {
    console.log('📋 Example Payload (from documentation):')
    console.log(JSON.stringify(examplePayload, null, 2))
    
    const response = await fetch(`${PRODUCTION_URL}/api/zoho/payments/create-session`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'AquaPartner-TokenDiagnostic/1.0'
      },
      body: JSON.stringify(examplePayload)
    })
    
    console.log(`📊 Response Status: ${response.status}`)
    
    const responseText = await response.text()
    console.log(`📄 Response:`)
    console.log(responseText.substring(0, 500))
    if (responseText.length > 500) {
      console.log('...(truncated)')
    }
    
  } catch (error) {
    console.log('💥 Example payload test failed:', error.message)
  }

  console.log('\n' + '='.repeat(50))
  console.log('🏁 TOKEN DIAGNOSTIC COMPLETED')
  console.log('='.repeat(50))
  
  console.log('\n💡 NEXT STEPS:')
  console.log('1. Check the server logs for more detailed error information')
  console.log('2. Verify Zoho Payment token is valid and not expired')
  console.log('3. Confirm Zoho Payment API credentials and account status')
  console.log('4. Test with Zoho Payment API directly using cURL')
  console.log('5. Check if the external token management service is working')
}

// Run the diagnostic
if (typeof require !== 'undefined' && require.main === module) {
  runTokenDiagnostics()
    .then(() => {
      console.log('\n✅ Diagnostic completed successfully')
      process.exit(0)
    })
    .catch(error => {
      console.error('\n💥 Diagnostic failed:', error.message)
      process.exit(1)
    })
}

module.exports = { runTokenDiagnostics }
