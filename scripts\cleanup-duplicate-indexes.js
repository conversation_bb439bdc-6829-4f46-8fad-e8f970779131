/**
 * Database Index Cleanup Script
 * 
 * This script resolves duplicate index warnings by:
 * 1. Dropping conflicting indexes created by external scripts
 * 2. Allowing Mongoose to manage all indexes through schema definitions
 * 3. Ensuring proper index naming and configuration
 */

const { MongoClient } = require('mongodb');
require('dotenv').config({ path: '.env.local' });

async function cleanupDuplicateIndexes() {
  const uri = process.env.MONGODB_URI;
  if (!uri) {
    console.error('❌ MONGODB_URI not found in environment variables');
    process.exit(1);
  }

  const client = new MongoClient(uri);

  try {
    await client.connect();
    console.log('✅ Connected to MongoDB');

    const db = client.db();
    
    // Clean up PaymentTransactions collection
    await cleanupPaymentTransactionIndexes(db);
    
    // Clean up WebhookEvents collection
    await cleanupWebhookEventIndexes(db);
    
    console.log('\n🎉 Index cleanup completed successfully!');
    console.log('📝 Mongoose will now manage all indexes through schema definitions.');
    
  } catch (error) {
    console.error('❌ Error during index cleanup:', error);
    process.exit(1);
  } finally {
    await client.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

async function cleanupPaymentTransactionIndexes(db) {
  console.log('\n🔧 Cleaning up PaymentTransactions indexes...');
  
  const collection = db.collection('PaymentTransactions');
  
  try {
    // Get existing indexes
    const indexes = await collection.indexes();
    console.log('📋 Current indexes:', indexes.map(idx => ({ 
      name: idx.name, 
      key: idx.key, 
      unique: idx.unique, 
      sparse: idx.sparse 
    })));

    // Drop problematic indexes that might conflict with Mongoose
    const indexesToDrop = [
      'payments_session_id_sparse',
      'payment_link_id_sparse',
      'payments_session_id_1', // Default MongoDB index name
      'payment_link_id_1',     // Default MongoDB index name
      'payment_id_1'           // Default MongoDB index name
    ];

    for (const indexName of indexesToDrop) {
      const indexExists = indexes.find(idx => idx.name === indexName);
      if (indexExists) {
        try {
          await collection.dropIndex(indexName);
          console.log(`   ✅ Dropped index: ${indexName}`);
        } catch (error) {
          if (error.code === 27) { // Index not found
            console.log(`   ℹ️  Index ${indexName} already removed`);
          } else {
            console.log(`   ⚠️  Could not drop index ${indexName}:`, error.message);
          }
        }
      }
    }

    console.log('✅ PaymentTransactions index cleanup completed');
    
  } catch (error) {
    console.error('❌ Error cleaning PaymentTransactions indexes:', error);
  }
}

async function cleanupWebhookEventIndexes(db) {
  console.log('\n🔧 Cleaning up WebhookEvents indexes...');
  
  const collection = db.collection('WebhookEvents');
  
  try {
    // Get existing indexes
    const indexes = await collection.indexes();
    console.log('📋 Current indexes:', indexes.map(idx => ({ 
      name: idx.name, 
      key: idx.key, 
      unique: idx.unique, 
      sparse: idx.sparse 
    })));

    // Drop any conflicting indexes
    const indexesToDrop = [
      'payment_session_id_1',
      'payment_link_id_1',
      'webhook_received_at_1'
    ];

    for (const indexName of indexesToDrop) {
      const indexExists = indexes.find(idx => idx.name === indexName);
      if (indexExists) {
        try {
          await collection.dropIndex(indexName);
          console.log(`   ✅ Dropped index: ${indexName}`);
        } catch (error) {
          if (error.code === 27) { // Index not found
            console.log(`   ℹ️  Index ${indexName} already removed`);
          } else {
            console.log(`   ⚠️  Could not drop index ${indexName}:`, error.message);
          }
        }
      }
    }

    console.log('✅ WebhookEvents index cleanup completed');
    
  } catch (error) {
    console.error('❌ Error cleaning WebhookEvents indexes:', error);
  }
}

// Run the cleanup
if (require.main === module) {
  cleanupDuplicateIndexes().catch(console.error);
}

module.exports = { cleanupDuplicateIndexes };
