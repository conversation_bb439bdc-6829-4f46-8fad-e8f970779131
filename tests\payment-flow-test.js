const https = require('https')

// Test configuration
const BASE_URL = 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net'
const TEST_INVOICE = {
  invoiceId: 'TEST_INV_001',
  invoiceNumber: 'INV-2024-TEST-001',
  total: 100.0,
  balance: 100.0,
  invoiceStatus: 'Overdue',
  dueDate: '2024-12-31T23:59:59.000Z',
}

const TEST_CUSTOMER = {
  customerId: 'TEST_CUST_001',
  customerCode: 'TC001',
  customerName: 'Test Customer',
  companyName: 'Test Company Ltd',
  Email: '<EMAIL>',
  mobileNumber: '+91-9876543210',
}

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const requestOptions = {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        ...options.headers,
      },
    }

    const request = https.request(url, requestOptions, (response) => {
      // Handle redirects (308 Permanent Redirect)
      if (response.statusCode === 308 && response.headers.location) {
        const redirectUrl = response.headers.location.startsWith('http')
          ? response.headers.location
          : `${BASE_URL}${response.headers.location}`
        console.log(`   Redirect to: ${redirectUrl}`)
        return makeRequest(redirectUrl, options).then(resolve).catch(reject)
      }

      let data = ''

      response.on('data', (chunk) => {
        data += chunk
      })

      response.on('end', () => {
        resolve({
          statusCode: response.statusCode,
          headers: response.headers,
          data: data,
        })
      })
    })

    request.on('error', (error) => {
      reject(error)
    })

    if (options.body) {
      request.write(options.body)
    }

    request.setTimeout(30000, () => {
      request.destroy()
      reject(new Error('Request timeout'))
    })

    request.end()
  })
}

async function testPaymentFlow() {
  console.log('🧪 Testing Complete Pay Now Flow')
  console.log('================================')

  try {
    // Step 1: Test Health Check
    console.log('\n1. Testing API Health...')
    const healthResponse = await makeRequest(`${BASE_URL}/api/zoho/health/`)

    if (healthResponse.statusCode === 200) {
      const healthData = JSON.parse(healthResponse.data)
      console.log('✅ API Health Check: PASS')
      console.log(`   Service: ${healthData.service}`)
      console.log(`   Status: ${healthData.status}`)
      console.log(`   Domain: ${healthData.configuration?.domain}`)
    } else {
      console.log('❌ API Health Check: FAIL')
      console.log(`   Status: ${healthResponse.statusCode}`)
      return
    }

    // Step 2: Test Payment Session Creation (simulating Pay Now button click)
    console.log('\n2. Testing Payment Session Creation...')

    const paymentRequest = {
      amount: TEST_INVOICE.total,
      currency: 'INR',
      description: `Payment for Invoice ${TEST_INVOICE.invoiceNumber}`,
      invoice_number: TEST_INVOICE.invoiceNumber,
      customer_id: TEST_CUSTOMER.customerId,
      customer_name: TEST_CUSTOMER.customerName,
      customer_email: TEST_CUSTOMER.Email,
      customer_phone: TEST_CUSTOMER.mobileNumber,
      redirect_url: `${BASE_URL}/payment-success`,
      reference_id: `INV_${TEST_INVOICE.invoiceNumber}_${Date.now()}`,
      meta_data: [
        { key: 'invoice_id', value: TEST_INVOICE.invoiceId },
        { key: 'customer_code', value: TEST_CUSTOMER.customerCode },
        { key: 'due_date', value: TEST_INVOICE.dueDate },
      ],
    }

    console.log('   Payment Request Data:')
    console.log(`   - Amount: ₹${paymentRequest.amount}`)
    console.log(`   - Invoice: ${paymentRequest.invoice_number}`)
    console.log(`   - Customer: ${paymentRequest.customer_name}`)
    console.log(`   - Email: ${paymentRequest.customer_email}`)

    const createResponse = await makeRequest(`${BASE_URL}/api/zoho/payments/create-session/`, {
      method: 'POST',
      body: JSON.stringify(paymentRequest),
    })

    if (createResponse.statusCode === 201) {
      const createData = JSON.parse(createResponse.data)
      console.log('✅ Payment Session Creation: PASS')
      console.log(`   Session ID: ${createData.data?.payment_session_id}`)
      console.log(`   Transaction ID: ${createData.data?.transaction_id}`)
      console.log(`   Amount: ₹${createData.data?.amount}`)
      console.log(`   Expires In: ${createData.data?.expires_in}`)

      // Step 3: Test Payment Status Retrieval
      if (createData.data?.payment_session_id) {
        console.log('\n3. Testing Payment Status Retrieval...')

        const statusResponse = await makeRequest(
          `${BASE_URL}/api/zoho/payments/status/${createData.data.payment_session_id}/`
        )

        if (statusResponse.statusCode === 200) {
          const statusData = JSON.parse(statusResponse.data)
          console.log('✅ Payment Status Retrieval: PASS')
          console.log(`   Status: ${statusData.data?.status}`)
          console.log(`   Session ID: ${statusData.data?.payment_session_id}`)
          console.log(`   Amount: ₹${statusData.data?.amount}`)
          console.log(`   Currency: ${statusData.data?.currency}`)
        } else {
          console.log('❌ Payment Status Retrieval: FAIL')
          console.log(`   Status: ${statusResponse.statusCode}`)
          console.log(`   Response: ${statusResponse.data}`)
        }
      }

      // Step 4: Test Payment URL Generation
      console.log('\n4. Testing Payment URL Generation...')
      const paymentUrl = `/payment?session_id=${createData.data?.payment_session_id}&invoice=${TEST_INVOICE.invoiceNumber}`
      console.log('✅ Payment URL Generated:')
      console.log(`   URL: ${BASE_URL}${paymentUrl}`)
      console.log('   This URL would be opened in a new window for payment processing')
    } else {
      console.log('❌ Payment Session Creation: FAIL')
      console.log(`   Status: ${createResponse.statusCode}`)
      console.log(`   Response: ${createResponse.data}`)

      try {
        const errorData = JSON.parse(createResponse.data)
        console.log(`   Error: ${errorData.message || errorData.error}`)
      } catch (e) {
        console.log(`   Raw Response: ${createResponse.data.substring(0, 200)}`)
      }
    }

    // Step 5: Test Payment List Endpoint
    console.log('\n5. Testing Payment List Endpoint...')
    const listResponse = await makeRequest(
      `${BASE_URL}/api/zoho/payments/list/?customer_id=${TEST_CUSTOMER.customerId}&limit=5`
    )

    if (listResponse.statusCode === 200) {
      const listData = JSON.parse(listResponse.data)
      console.log('✅ Payment List: PASS')
      console.log(`   Success: ${listData.success}`)
      console.log(
        `   Transactions: ${Array.isArray(listData.data?.transactions) ? listData.data.transactions.length : 'Not an array'}`
      )
    } else {
      console.log('❌ Payment List: FAIL')
      console.log(`   Status: ${listResponse.statusCode}`)
    }

    // Step 6: Test Webhook Endpoint
    console.log('\n6. Testing Webhook Endpoint...')
    const webhookResponse = await makeRequest(`${BASE_URL}/api/zoho/webhooks/payment/`)

    if (webhookResponse.statusCode === 200) {
      const webhookData = JSON.parse(webhookResponse.data)
      console.log('✅ Webhook Endpoint: PASS')
      console.log(`   Endpoint: ${webhookData.endpoint || 'Configured'}`)
      console.log(`   Events: ${webhookData.supported_events?.length || 0} supported`)
    } else {
      console.log('❌ Webhook Endpoint: FAIL')
      console.log(`   Status: ${webhookResponse.statusCode}`)
    }
  } catch (error) {
    console.log(`\n💥 Test Error: ${error.message}`)
    console.log('Stack:', error.stack)
  }

  console.log('\n🏁 Payment Flow Test Complete')
}

// Test Payment Flow Simulation
async function simulatePayNowClick() {
  console.log('\n🎯 Simulating Pay Now Button Click')
  console.log('==================================')

  console.log('User Action: Click "Pay Now" button for overdue invoice')
  console.log(`Invoice: ${TEST_INVOICE.invoiceNumber} (₹${TEST_INVOICE.total})`)
  console.log(`Customer: ${TEST_CUSTOMER.customerName}`)
  console.log('Expected Flow:')
  console.log('1. Button shows loading state')
  console.log('2. Payment request sent to API')
  console.log('3. Payment session created')
  console.log('4. Payment window opens')
  console.log('5. User completes payment')
  console.log('6. Redirect to success/failure page')

  await testPaymentFlow()
}

// Run the test
simulatePayNowClick().catch(console.error)
