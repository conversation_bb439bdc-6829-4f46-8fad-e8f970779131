# Pay Now Button Testing Guide

## Overview
This guide provides comprehensive testing procedures for the "Pay Now" button functionality in the invoicesPage.jsx component.

## Test Environment Setup

### Prerequisites
1. ✅ Zoho Payments account configured
2. ✅ Valid access tokens in environment variables
3. ✅ Database connection established
4. ✅ Customer with overdue invoices
5. ✅ Browser developer tools open for monitoring

### Environment Variables Required
```bash
ZOHO_PAY_ACCOUNT_ID=your_account_id
ZOHO_PAYMENT_CLIENT_ID=your_client_id
ZOHO_PAYMENT_CLIENT_SECRET=your_client_secret
NEXT_PUBLIC_DOMAIN=your_domain
```

## Manual Testing Procedures

### Test 1: Button Visibility and State
**Objective**: Verify Pay Now button appears only for overdue invoices

**Steps**:
1. Navigate to `/billingAndPayments` page
2. Check invoice list for different statuses
3. Verify button visibility rules

**Expected Results**:
- ✅ "Pay Now" button visible only for `invoiceStatus === 'Overdue'`
- ✅ <PERSON><PERSON> shows "Paid" text for `Closed` or `Paid` invoices
- ✅ <PERSON><PERSON> shows "No payment required" for other statuses

### Test 2: Loading State Functionality
**Objective**: Test loading indicators and disabled states

**Steps**:
1. Click "Pay Now" button on overdue invoice
2. Observe button state changes
3. Check for proper loading indicators

**Expected Results**:
- ✅ Button becomes disabled immediately
- ✅ Loading spinner appears
- ✅ Text changes to "Processing..."
- ✅ Button background changes to gray
- ✅ Cursor changes to "not-allowed"

### Test 3: Email Handling Logic
**Objective**: Test email fallback strategies

**Test Cases**:
```javascript
// Test Case 3a: Valid Email
customer = {
  customerId: "CUST001",
  Email: "<EMAIL>",
  customerName: "Test Customer"
}
// Expected: Uses "<EMAIL>"

// Test Case 3b: Lowercase Email
customer = {
  customerId: "CUST001", 
  email: "<EMAIL>",
  customerName: "Test Customer"
}
// Expected: Uses "<EMAIL>"

// Test Case 3c: No Email
customer = {
  customerId: "CUST001",
  customerName: "Test Customer"
}
// Expected: Uses "<EMAIL>"
```

### Test 4: Payment Data Validation
**Objective**: Verify correct payment data structure

**Steps**:
1. Open browser console
2. Click "Pay Now" button
3. Check console logs for payment data

**Expected Payment Data Structure**:
```javascript
{
  amount: 1000.50,                    // From selectedInvoice.total
  currency: "INR",                    // Fixed value
  description: "Payment for AquaPartner Invoice INV-001",
  invoice_number: "INV-001",          // From selectedInvoice.invoiceNumber
  customer_id: "CUST001",             // From customer.customerId
  customer_name: "Test Customer",     // From customer.customerName
  customer_email: "<EMAIL>", // With fallback logic
  customer_phone: "+91XXXXXXXXXX",    // From customer.mobileNumber
  redirect_url: "http://localhost:3000/payment-success?invoice=INV-001",
  reference_id: "CUST001_INV-001_1234567890",
  notify_user: false
}
```

### Test 5: API Integration Testing
**Objective**: Test actual API calls and responses

**Steps**:
1. Monitor Network tab in browser dev tools
2. Click "Pay Now" button
3. Verify API calls and responses

**Expected Network Activity**:
- ✅ POST request to `/api/zoho/payments/create-payment`
- ✅ Request headers include `X-Force-Payment-Method: links`
- ✅ Response status 201 (Created)
- ✅ Response contains payment link data

### Test 6: Error Handling
**Objective**: Test error scenarios and user feedback

**Test Cases**:
```javascript
// Test Case 6a: Network Error
// Disconnect internet and click Pay Now
// Expected: Error message displayed

// Test Case 6b: Invalid Customer Data
// Test with missing customer information
// Expected: Graceful fallback or error message

// Test Case 6c: API Error Response
// Mock API to return error
// Expected: User-friendly error message
```

### Test 7: Payment Link Redirect
**Objective**: Test successful payment link creation and redirect

**Steps**:
1. Click "Pay Now" with valid data
2. Wait for payment link creation
3. Verify redirect behavior

**Expected Results**:
- ✅ Payment link created successfully
- ✅ User redirected to Zoho payment page
- ✅ Payment page contains correct amount and description
- ✅ Return URL configured correctly

## Browser Console Testing Script

```javascript
// Paste this in browser console on the invoices page
async function testPayNowButton() {
  console.log('🧪 TESTING: Pay Now Button Functionality');
  
  // Test 1: Check if required functions exist
  console.log('📋 TEST 1: Function Availability');
  console.log('createZohoPaymentLink available:', typeof createZohoPaymentLink !== 'undefined');
  console.log('initializeZohoPaymentResponse available:', typeof initializeZohoPaymentResponse !== 'undefined');
  
  // Test 2: Check customer data
  console.log('📋 TEST 2: Customer Data');
  const customerData = window.customerData || {}; // Assuming customer data is available
  console.log('Customer ID:', customerData.customerId);
  console.log('Customer Email:', customerData.Email || customerData.email);
  console.log('Customer Name:', customerData.customerName);
  
  // Test 3: Check for overdue invoices
  console.log('📋 TEST 3: Invoice Data');
  const payNowButtons = document.querySelectorAll('button[title*="Pay this overdue invoice"]');
  console.log('Pay Now buttons found:', payNowButtons.length);
  
  // Test 4: Simulate button click (if buttons exist)
  if (payNowButtons.length > 0) {
    console.log('📋 TEST 4: Button Click Simulation');
    console.log('First Pay Now button:', payNowButtons[0]);
    console.log('Button disabled state:', payNowButtons[0].disabled);
    console.log('Button text:', payNowButtons[0].textContent.trim());
  }
  
  console.log('✅ TESTING: Complete - Check results above');
}

// Run the test
testPayNowButton();
```

## Expected Console Output

### Successful Payment Flow
```
🔗 PAY NOW: Initiating payment link for invoice INV-001
📧 EMAIL: Using customer.Email: <EMAIL>
🔗 PAYMENT DATA: Creating payment link with: {amount: 1000.50, currency: "INR", ...}
🔗 PAYMENT LINK API: Creating payment link: {amount: 1000.50, currency: "INR", ...}
🔗 PAYMENT LINK API: Response status: 201 Created
✅ PAYMENT LINK API: Success response: {success: true, payment_method: "payment_link", ...}
✅ PAYMENT LINK CREATED: Response: {success: true, ...}
🔗 PAYMENT INIT: Using payment link redirect
🔗 PAYMENT INIT: Redirecting to: https://payments.zoho.in/paymentlink/...
✅ PAYMENT REDIRECT: User will be redirected to payment link
```

### Error Scenarios
```
❌ PAY NOW ERROR: Error: Failed to create payment link: Bad Request
// Error message displayed in UI
```

## Automated Testing Checklist

- [ ] Button visibility for different invoice statuses
- [ ] Loading state transitions
- [ ] Email fallback logic
- [ ] Payment data structure validation
- [ ] API request/response handling
- [ ] Error message display
- [ ] Payment link redirect functionality
- [ ] Customer data validation
- [ ] Invoice number tracking
- [ ] Reference ID generation

## Common Issues and Solutions

### Issue 1: Button Not Visible
**Cause**: Invoice status not 'Overdue'
**Solution**: Check invoice data and status values

### Issue 2: Payment Link Creation Fails
**Cause**: Missing required fields or invalid API credentials
**Solution**: Verify environment variables and customer data

### Issue 3: Redirect Not Working
**Cause**: Payment link URL not received or invalid
**Solution**: Check API response and payment link data

### Issue 4: Loading State Stuck
**Cause**: API call hanging or error not caught
**Solution**: Check network connectivity and error handling

## Performance Testing

### Metrics to Monitor
- Payment link creation time (should be < 3 seconds)
- Button response time (should be immediate)
- Memory usage during payment flow
- Network request size and timing

### Load Testing
- Test with multiple simultaneous payment requests
- Verify button state management with rapid clicks
- Test with large invoice amounts and long descriptions

## Security Testing

### Data Validation
- Test with malicious input in customer data
- Verify email sanitization
- Check for XSS vulnerabilities in error messages

### API Security
- Verify authentication headers
- Check for sensitive data in console logs
- Validate HTTPS usage for all requests

## Conclusion

This comprehensive testing guide ensures the Pay Now button functionality works correctly across all scenarios. Regular testing should be performed after any changes to the payment flow or related components.
