# Zoho Payment Webhook Monitoring System

## Overview

Comprehensive webhook traffic monitoring and logging system for Zoho Payment webhooks, providing real-time visibility and historical tracking of all webhook activity.

## Implementation Status

✅ **COMPLETED** - Webhook monitoring system is fully implemented and tested.

## Features Implemented

### 1. **Comprehensive Request Logging**
- ✅ Unique request ID generation for tracking
- ✅ Client IP address extraction (supports X-Forwarded-For, X-Real-IP)
- ✅ Request headers capture (content-type, user-agent, etc.)
- ✅ Request body size and preview logging
- ✅ Timestamp tracking with ISO format

### 2. **Signature Verification Monitoring**
- ✅ Signature presence detection
- ✅ Verification result logging (valid/invalid)
- ✅ Webhook secret configuration status
- ✅ Security breach detection and alerting

### 3. **Webhook Data Analysis**
- ✅ Event type identification and logging
- ✅ Payment details extraction (amount, currency, status)
- ✅ Transaction identifier tracking (payment_id, session_id, link_id)
- ✅ Customer information logging
- ✅ Full webhook payload capture

### 4. **Transaction Processing Monitoring**
- ✅ Transaction lookup result tracking
- ✅ Database query performance monitoring
- ✅ Status update operation logging
- ✅ Success/failure tracking with detailed error messages

### 5. **Response Tracking**
- ✅ HTTP status code logging
- ✅ Processing time measurement (milliseconds)
- ✅ Response payload capture
- ✅ Error details and stack traces

### 6. **Real-time Console Monitoring**
- ✅ Structured console output with emojis for easy reading
- ✅ Color-coded status indicators
- ✅ Request flow visualization
- ✅ Processing summary reports

### 7. **Historical Database Logging**
- ✅ Webhook events stored in WebhookEvent collection
- ✅ Enhanced schema with monitoring fields
- ✅ Processing metrics (time, status, errors)
- ✅ Client information tracking

### 8. **Monitoring Dashboard API**
- ✅ `/api/zoho/webhooks/logs` endpoint for webhook activity
- ✅ Configurable time ranges and filters
- ✅ Event type summaries with statistics
- ✅ Success rate calculations
- ✅ Recent events listing with details

## Console Output Examples

### Incoming Request Monitoring
```
🔔 ===== WEBHOOK REQUEST RECEIVED =====
📋 Request ID: webhook_1751614862458_ift8o9t59
⏰ Timestamp: 2025-07-04T10:11:02.458Z
🌐 Client IP: ***********
📊 Content Length: 245 bytes
🔐 Signature: Present
📋 Headers: {
  "content-type": "application/json",
  "user-agent": "Zoho-Webhook/1.0",
  "x-zoho-webhook-signature": "***PRESENT***"
}
=====================================
```

### Signature Verification
```
🔐 SIGNATURE VERIFICATION: ✅ VALID
🔑 Webhook Secret: Configured
```

### Webhook Data Analysis
```
📋 ===== WEBHOOK DATA ANALYSIS =====
🎯 Event Type: payment_link.paid
💰 Amount: 101.50
📊 Status: paid
🔗 Payment ID: pay_123456789
🔗 Link ID: 5619000000259011
===================================
```

### Transaction Lookup
```
✅ TRANSACTION FOUND
🔍 Type: Payment Link
🆔 Identifier: 5619000000259011
💾 Transaction ID: 68675c5d7d3f02e6fbc30e1a
👤 Customer: <EMAIL>
💰 Amount: 101.50 INR
📊 Current Status: created
```

### Processing Summary
```
🎉 ===== WEBHOOK PROCESSING SUMMARY =====
📋 Request ID: webhook_1751614862458_ift8o9t59
🎯 Event Type: payment_link.paid
📊 Status: COMPLETED SUCCESSFULLY
⏱️  Total Processing Time: 59ms
⏰ Timestamp: 2025-07-04T10:11:02.517Z
=========================================
```

## Database Schema Enhancements

### WebhookEvent Model - New Fields
```javascript
{
  // Monitoring fields
  processing_time_ms: Number,     // Processing duration
  response_status: Number,        // HTTP response status
  client_ip: String,             // Client IP address
  error_message: String,         // Error details if failed
  
  // Existing fields enhanced
  event_id: String,              // Unique request identifier
  signature_verified: Boolean,   // Signature validation result
  processed: Boolean,            // Processing success status
  processed_at: Date,           // Processing timestamp
}
```

## API Endpoints

### Webhook Logs API
```
GET /api/zoho/webhooks/logs
```

**Query Parameters:**
- `limit` (default: 50, max: 100) - Number of events to return
- `event_type` (default: 'all') - Filter by event type
- `status` ('success', 'failed', 'all') - Filter by processing status
- `hours` (default: 24) - Time range in hours

**Response Format:**
```json
{
  "summary": {
    "timeRange": "Last 24 hours",
    "totalEvents": 15,
    "successfulEvents": 12,
    "failedEvents": 3,
    "successRate": "80.00%",
    "eventsReturned": 15
  },
  "eventTypeSummary": [
    {
      "_id": "payment_link.paid",
      "count": 8,
      "successful": 7,
      "failed": 1,
      "avgProcessingTime": 45.2
    }
  ],
  "recentEvents": [
    {
      "id": "...",
      "eventId": "webhook_1751614862458_ift8o9t59",
      "eventType": "payment_link.paid",
      "processed": true,
      "processingTime": 59,
      "clientIP": "***********",
      "processedAt": "2025-07-04T10:11:02.517Z"
    }
  ]
}
```

## Usage Examples

### View Recent Webhook Activity
```bash
curl "http://localhost:3000/api/zoho/webhooks/logs?limit=10&hours=1"
```

### Filter Failed Webhooks
```bash
curl "http://localhost:3000/api/zoho/webhooks/logs?status=failed&hours=24"
```

### Monitor Specific Event Type
```bash
curl "http://localhost:3000/api/zoho/webhooks/logs?event_type=payment_link.paid&limit=20"
```

## Performance Metrics

The monitoring system tracks:
- **Processing Time**: Millisecond precision timing
- **Success Rate**: Percentage of successful webhook processing
- **Event Distribution**: Count by event type
- **Error Patterns**: Common failure reasons
- **Client Sources**: IP address tracking

## Security Features

- **Signature Verification Logging**: All verification attempts logged
- **IP Address Tracking**: Client source identification
- **Error Pattern Detection**: Suspicious activity monitoring
- **Request Validation**: Malformed request detection

## Backward Compatibility

✅ **All existing functionality preserved**:
- Existing webhook processing logic unchanged
- Database schema backward compatible
- API response format maintained
- Authentication flow preserved

## Production Deployment

The monitoring system is production-ready with:
- **Low Overhead**: Minimal performance impact
- **Error Resilience**: Monitoring failures don't affect webhook processing
- **Scalable Logging**: Efficient database operations
- **Real-time Visibility**: Immediate feedback on webhook activity

## Troubleshooting

### Common Issues
1. **High Processing Times**: Check database connectivity
2. **Signature Failures**: Verify webhook secret configuration
3. **Transaction Not Found**: Ensure payment link exists in database
4. **IP Address Unknown**: Check proxy configuration

### Debug Commands
```bash
# Check recent webhook activity
curl "http://localhost:3000/api/zoho/webhooks/logs?limit=5"

# Monitor failed webhooks
curl "http://localhost:3000/api/zoho/webhooks/logs?status=failed"

# Check specific event type
curl "http://localhost:3000/api/zoho/webhooks/logs?event_type=payment_link.paid"
```

## Next Steps

1. **Configure Zoho Dashboard**: Set webhook URL to receive real events
2. **Set Up Alerting**: Monitor failed webhook rates
3. **Performance Optimization**: Tune database queries if needed
4. **Analytics Dashboard**: Build visual monitoring interface (optional)

The webhook monitoring system provides comprehensive visibility into all Zoho payment webhook activity while maintaining the existing functionality and performance.
