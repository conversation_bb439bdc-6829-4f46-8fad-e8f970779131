# GitHub Secrets Setup Script

## Quick Setup Guide

This document provides the exact values you need to add as GitHub Secrets for your Azure deployment.

## GitHub Repository Secrets

Go to your GitHub repository → **Settings** → **Secrets and variables** → **Actions** → **New repository secret**

Add each of the following secrets:

### 1. Domain Configuration

**Secret Name:** `NEXT_PUBLIC_DOMAIN`
**Value:** `https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net`

**Secret Name:** `NEXT_PUBLIC_API_DOMAIN`
**Value:** `/api`

### 2. Database Configuration

**Secret Name:** `MONGODB_URI`
**Value:** `mongodb+srv://admin:<EMAIL>/aquapartner`

### 3. Zoho OAuth Configuration

**Secret Name:** `ZOHO_OAUTH_CLIENT_ID`
**Value:** `1000.OYY8H71ELT0DNW5CFFBE124H7GILMP`

**Secret Name:** `ZOHO_OAUTH_CLIENT_SECRET`
**Value:** `67ca50629a18ba97eb9868be9c8faacf6645edf13d`

**Secret Name:** `ZOHO_OAUTH_REFRESH_TOKEN`
**Value:** `**********************************************************************`

### 4. Zoho Payment Configuration

**Secret Name:** `ZOHO_PAYMENT_SESSION_URL`
**Value:** `https://payments.zoho.in/api/v1/paymentsessions`

**Secret Name:** `ZOHO_PAY_ACCOUNT_ID`
**Value:** `***********`

**Secret Name:** `ZOHO_PAY_API_KEY`
**Value:** `1003.f35b9411653295bb03db1e8490dc6cdd.0f625d54ec97f4eba7bedd0dc6fc23b8`

### 5. Webhook Configuration

**Secret Name:** `ZOHO_WEBHOOK_SECRET`
**Value:** `21155a0f5fd70801737195ba85492b7f622fe3a39b1da0a13a76635bfe3b0c1c56c0d7a833170831ac446bcf4ec801288a0e3c4ecf1347d5ceac9f6c54faa43ad9f40e40d9cc77733fe0afed6018bdee`

## Verification Steps

After adding all secrets:

1. **Check Secrets List:**

   - You should have 10 secrets total
   - All secret names should match exactly (case-sensitive)

2. **Trigger Deployment:**

   ```bash
   git add .
   git commit -m "Add environment variables configuration"
   git push origin main
   ```

3. **Monitor Deployment:**

   - Go to **Actions** tab in GitHub
   - Watch the latest workflow run
   - Ensure it completes successfully

4. **Test Health Endpoint:**

   ```bash
   curl https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api/zoho/health
   ```

   Expected result:

   ```json
   {
     "status": "healthy",
     "checks": {
       "environment": {
         "status": "healthy",
         "message": "All required environment variables are set"
       }
     }
   }
   ```

## Important Notes

- **Case Sensitivity:** Secret names are case-sensitive and must match exactly
- **No Spaces:** Ensure no leading/trailing spaces in secret values
- **Security:** These secrets are encrypted and only accessible during workflow runs
- **Updates:** If you need to update a secret, you can edit it in the same location

## Troubleshooting

If the health endpoint still shows missing `ZOHO_PAY_ACCOUNT_ID`:

1. **Double-check the secret name:** It should be exactly `ZOHO_PAY_ACCOUNT_ID`
2. **Verify the value:** Should be `***********`
3. **Check deployment logs:** Look for any errors in the GitHub Actions workflow
4. **Wait for deployment:** Changes may take a few minutes to propagate

## Security Recommendations

For production deployment:

1. **Use different credentials** for production vs development
2. **Rotate secrets regularly** (especially API keys and tokens)
3. **Monitor access logs** for any unauthorized usage
4. **Use Azure Key Vault** for additional security in production environments

## Next Steps

Once all secrets are configured and the deployment is successful:

1. Test the complete payment flow
2. Verify webhook functionality
3. Set up monitoring and alerts
4. Document any production-specific configurations
