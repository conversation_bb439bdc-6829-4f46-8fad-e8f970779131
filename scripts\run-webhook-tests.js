/**
 * Webhook Testing Suite Runner
 * Executes all webhook-related tests and generates comprehensive reports
 */

const { spawn } = require('child_process')
const fs = require('fs').promises
const path = require('path')
require('dotenv').config({ path: '.env.local' })

console.log('🧪 WEBHOOK TESTING SUITE RUNNER')
console.log('================================')
console.log('')

/**
 * Run a test script and capture results
 */
async function runTest(testScript, testName) {
  return new Promise((resolve) => {
    console.log(`🚀 Running ${testName}...`)
    console.log(`📄 Script: ${testScript}`)
    console.log('─'.repeat(50))

    const child = spawn('node', [testScript], {
      stdio: 'pipe',
      cwd: process.cwd()
    })

    let stdout = ''
    let stderr = ''

    child.stdout.on('data', (data) => {
      const output = data.toString()
      stdout += output
      process.stdout.write(output) // Real-time output
    })

    child.stderr.on('data', (data) => {
      const output = data.toString()
      stderr += output
      process.stderr.write(output) // Real-time error output
    })

    child.on('close', (code) => {
      console.log('─'.repeat(50))
      console.log(`✅ ${testName} completed with exit code: ${code}`)
      console.log('')

      resolve({
        testName,
        testScript,
        exitCode: code,
        success: code === 0,
        stdout,
        stderr,
        timestamp: new Date().toISOString()
      })
    })

    child.on('error', (error) => {
      console.log('─'.repeat(50))
      console.log(`❌ ${testName} failed to start: ${error.message}`)
      console.log('')

      resolve({
        testName,
        testScript,
        exitCode: -1,
        success: false,
        stdout,
        stderr,
        error: error.message,
        timestamp: new Date().toISOString()
      })
    })
  })
}

/**
 * Generate test report
 */
async function generateTestReport(results) {
  const timestamp = new Date().toISOString()
  const reportData = {
    timestamp,
    environment: {
      nodeVersion: process.version,
      platform: process.platform,
      baseUrl: process.env.NEXT_PUBLIC_DOMAIN || 'http://localhost:3000',
      webhookSecret: process.env.ZOHO_WEBHOOK_SECRET ? 'Configured' : 'Missing'
    },
    summary: {
      totalTests: results.length,
      passed: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      successRate: Math.round((results.filter(r => r.success).length / results.length) * 100)
    },
    results
  }

  // Generate JSON report
  const jsonReport = JSON.stringify(reportData, null, 2)
  await fs.writeFile('webhook-test-report.json', jsonReport)

  // Generate HTML report
  const htmlReport = generateHTMLReport(reportData)
  await fs.writeFile('webhook-test-report.html', htmlReport)

  // Generate console summary
  console.log('📊 TEST EXECUTION SUMMARY')
  console.log('=========================')
  console.log(`Total Tests: ${reportData.summary.totalTests}`)
  console.log(`Passed: ${reportData.summary.passed} ✅`)
  console.log(`Failed: ${reportData.summary.failed} ${reportData.summary.failed > 0 ? '❌' : ''}`)
  console.log(`Success Rate: ${reportData.summary.successRate}%`)
  console.log('')

  results.forEach(result => {
    console.log(`${result.success ? '✅' : '❌'} ${result.testName}: ${result.success ? 'PASSED' : 'FAILED'}`)
    if (!result.success && result.error) {
      console.log(`   Error: ${result.error}`)
    }
  })

  console.log('')
  console.log('📄 Reports generated:')
  console.log('   - webhook-test-report.json')
  console.log('   - webhook-test-report.html')

  return reportData
}

/**
 * Generate HTML test report
 */
function generateHTMLReport(reportData) {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Webhook Test Report - ${reportData.timestamp}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; color: #333; }
        .summary-card .value { font-size: 2em; font-weight: bold; }
        .passed { color: #28a745; }
        .failed { color: #dc3545; }
        .test-result { margin-bottom: 20px; padding: 15px; border-radius: 8px; border-left: 4px solid; }
        .test-passed { background: #d4edda; border-color: #28a745; }
        .test-failed { background: #f8d7da; border-color: #dc3545; }
        .test-name { font-weight: bold; margin-bottom: 5px; }
        .test-details { font-size: 0.9em; color: #666; }
        .environment { background: #e9ecef; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        .environment h3 { margin-top: 0; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 0.8em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Webhook Test Report</h1>
            <p>Generated on ${new Date(reportData.timestamp).toLocaleString()}</p>
        </div>

        <div class="environment">
            <h3>Environment Information</h3>
            <p><strong>Node Version:</strong> ${reportData.environment.nodeVersion}</p>
            <p><strong>Platform:</strong> ${reportData.environment.platform}</p>
            <p><strong>Base URL:</strong> ${reportData.environment.baseUrl}</p>
            <p><strong>Webhook Secret:</strong> ${reportData.environment.webhookSecret}</p>
        </div>

        <div class="summary">
            <div class="summary-card">
                <h3>Total Tests</h3>
                <div class="value">${reportData.summary.totalTests}</div>
            </div>
            <div class="summary-card">
                <h3>Passed</h3>
                <div class="value passed">${reportData.summary.passed}</div>
            </div>
            <div class="summary-card">
                <h3>Failed</h3>
                <div class="value failed">${reportData.summary.failed}</div>
            </div>
            <div class="summary-card">
                <h3>Success Rate</h3>
                <div class="value">${reportData.summary.successRate}%</div>
            </div>
        </div>

        <h2>Test Results</h2>
        ${reportData.results.map(result => `
            <div class="test-result ${result.success ? 'test-passed' : 'test-failed'}">
                <div class="test-name">
                    ${result.success ? '✅' : '❌'} ${result.testName}
                </div>
                <div class="test-details">
                    <p><strong>Script:</strong> ${result.testScript}</p>
                    <p><strong>Exit Code:</strong> ${result.exitCode}</p>
                    <p><strong>Timestamp:</strong> ${new Date(result.timestamp).toLocaleString()}</p>
                    ${result.error ? `<p><strong>Error:</strong> ${result.error}</p>` : ''}
                </div>
            </div>
        `).join('')}
    </div>
</body>
</html>
  `.trim()
}

/**
 * Main test runner
 */
async function runAllWebhookTests() {
  const tests = [
    {
      script: 'tests/webhook-security-test.js',
      name: 'Webhook Security Tests'
    },
    {
      script: 'tests/e2e-payment-flow-test.js',
      name: 'End-to-End Payment Flow Tests'
    },
    {
      script: 'scripts/verify-webhook-configuration.js',
      name: 'Webhook Configuration Verification'
    }
  ]

  console.log(`📋 Scheduled ${tests.length} test suites:`)
  tests.forEach((test, index) => {
    console.log(`   ${index + 1}. ${test.name}`)
  })
  console.log('')

  const results = []

  for (const test of tests) {
    try {
      // Check if test file exists
      await fs.access(test.script)
      const result = await runTest(test.script, test.name)
      results.push(result)
    } catch (error) {
      console.log(`❌ Test file not found: ${test.script}`)
      results.push({
        testName: test.name,
        testScript: test.script,
        exitCode: -1,
        success: false,
        stdout: '',
        stderr: '',
        error: `Test file not found: ${error.message}`,
        timestamp: new Date().toISOString()
      })
    }
  }

  // Generate comprehensive report
  const report = await generateTestReport(results)

  // Return overall success
  const overallSuccess = results.every(r => r.success)
  
  console.log('')
  console.log(`🎯 Overall Result: ${overallSuccess ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`)
  
  return {
    success: overallSuccess,
    report,
    results
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllWebhookTests()
    .then((result) => {
      process.exit(result.success ? 0 : 1)
    })
    .catch((error) => {
      console.error('❌ Test runner failed:', error)
      process.exit(1)
    })
}

module.exports = { runAllWebhookTests, runTest, generateTestReport }
