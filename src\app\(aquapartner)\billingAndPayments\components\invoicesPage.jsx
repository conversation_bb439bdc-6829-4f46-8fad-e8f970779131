'use client'

import { Badge } from '@/components/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/table'
import LoadingScreen from '@/customComponents/LoadingScreen'
import { retailerAtom } from '@/customComponents/providers'
import { indianCurrencyFormat } from '@/utils/formats'
import { useFetchInvoicesByCustomerId } from '@/utils/requests'
import { debugZohoIntegration } from '@/utils/zohoPayment'
import { createZohoPaymentLink, initializeZohoPaymentResponse } from '@/utils/zohoPaymentLinks'
import { useAtomValue } from 'jotai'
import moment from 'moment'
import { useEffect, useState } from 'react'
import { InvoiceDetails } from './invoiceDetails'
// Utility function for class names
function classNames(...classes) {
  return classes.filter(Boolean).join(' ')
}

export const InvoicesPage = () => {
  const [loading, setLoading] = useState(true)
  const customer = useAtomValue(retailerAtom)
  const [invoice, setInvoice] = useState({})
  const [isPopupOpen, setIsPopupOpen] = useState(false)
  const [paymentError, setPaymentError] = useState(null)
  const [loadingInvoiceId, setLoadingInvoiceId] = useState(null)

  const invoices = useFetchInvoicesByCustomerId(customer.customerId)

  const handleOpenPopup = ({ selectedInvoice }) => {
    setInvoice(selectedInvoice)
    setIsPopupOpen(true)
  }

  // Optimized Payment Link integration for consistent user experience
  const handlePayNow = async (selectedInvoice) => {
    try {
      console.log('🔗 PAY NOW: Initiating payment link for invoice', selectedInvoice.invoiceNumber)
      setLoadingInvoiceId(selectedInvoice.invoiceId)
      setPaymentError(null)

      // Enhanced email handling with multiple fallback strategies
      let finalCustomerEmail = null
      if (customer.Email && customer.Email.trim() !== '') {
        finalCustomerEmail = customer.Email.trim()
        console.log('📧 EMAIL: Using customer.Email:', finalCustomerEmail)
      } else if (customer['email'] && customer['email'].trim() !== '') {
        finalCustomerEmail = customer['email'].trim()
        console.log('📧 EMAIL: Using customer.email (lowercase):', finalCustomerEmail)
      } else {
        finalCustomerEmail = `${customer.customerId}@aquapartner.com`
        console.log('📧 EMAIL: Using fallback email:', finalCustomerEmail)
      }

      // Prepare payment data for Payment Link creation
      const paymentData = {
        amount: selectedInvoice.total,
        currency: 'INR',
        description: `Payment for AquaPartner Invoice ${selectedInvoice.invoiceNumber}`,
        invoice_number: selectedInvoice.invoiceNumber,
        customer_id: customer.customerId,
        customer_name: customer.customerName,
        customer_email: finalCustomerEmail,
        customer_phone: customer.mobileNumber,
        redirect_url: `${window.location.origin}/payment-success?invoice=${selectedInvoice.invoiceNumber}`,
        reference_id: `${customer.customerId}_${selectedInvoice.invoiceNumber}_${Date.now()}`,
        notify_user: false, // Don't send email notifications to avoid spam
      }

      console.log('� PAYMENT DATA: Creating payment link with:', paymentData)

      // Create payment link using the dedicated function
      const paymentResponse = await createZohoPaymentLink(paymentData)

      console.log('✅ PAYMENT LINK CREATED: Response:', paymentResponse)

      // Initialize payment (will redirect to payment link)
      initializeZohoPaymentResponse(paymentResponse, selectedInvoice.invoiceNumber)

      console.log('✅ PAYMENT REDIRECT: User will be redirected to payment link')
      setLoadingInvoiceId(null)
    } catch (error) {
      console.error('❌ PAY NOW ERROR:', error)
      setLoadingInvoiceId(null)
      setPaymentError(`Failed to initiate payment: ${error.message}`)
    }
  }

  useEffect(() => {
    if (invoices?.results) {
      setLoading(false)
    }
  }, [invoices])

  useEffect(() => {
    // Initialize debug utilities for payment monitoring
    debugZohoIntegration()
  }, [])

  return (
    <>
      {loading && <LoadingScreen />}

      {/* Payment Error Display */}
      {paymentError && (
        <div className="mb-4 rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Payment Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{paymentError}</p>
              </div>
              <div className="mt-4">
                <button
                  type="button"
                  onClick={() => setPaymentError(null)}
                  className="rounded-md bg-red-50 px-2 py-1.5 text-sm font-medium text-red-800 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-600 focus:ring-offset-2 focus:ring-offset-red-50"
                >
                  Dismiss
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      <Table className="mt-2 [--gutter:theme(spacing.6)] lg:[--gutter:theme(spacing.10)]">
        <TableHead>
          <TableRow>
            <TableHeader>Invoice date</TableHeader>
            <TableHeader>Invoice Number</TableHeader>
            <TableHeader className="text-right">Total</TableHeader>
            <TableHeader>Status</TableHeader>
            <TableHeader></TableHeader>
          </TableRow>
        </TableHead>
        <TableBody>
          {invoices.results &&
            invoices.results.map((invoice, index) => (
              <TableRow className="hover:bg-blue-50" key={index} title={`Order # No`}>
                <TableCell className="" onClick={() => handleOpenPopup({ selectedInvoice: invoice })}>
                  {moment(invoice.invoiceDate).format('DD-MM-YYYY')}
                </TableCell>
                <TableCell onClick={() => handleOpenPopup({ selectedInvoice: invoice })}>
                  {invoice.invoiceNumber}
                </TableCell>
                <TableCell className="text-right" onClick={() => handleOpenPopup({ selectedInvoice: invoice })}>
                  {indianCurrencyFormat(invoice.total)}
                </TableCell>

                <TableCell onClick={() => handleOpenPopup({ selectedInvoice: invoice })}>
                  <Badge
                    color={
                      invoice.invoiceStatus === 'Void'
                        ? 'zinc'
                        : invoice.invoiceStatus === 'Closed'
                          ? 'green'
                          : invoice.invoiceStatus === 'Invoiced'
                            ? 'blue'
                            : invoice.invoiceStatus === 'Partially Delivered'
                              ? 'yellow'
                              : invoice.invoiceStatus === 'Rejected'
                                ? 'red'
                                : invoice.invoiceStatus === 'Overdue'
                                  ? 'red'
                                  : 'yellow'
                    }
                  >
                    {invoice.invoiceStatus}
                  </Badge>
                </TableCell>
                <TableCell>
                  {invoice.invoiceStatus === 'Overdue' ? (
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handlePayNow(invoice)
                      }}
                      disabled={loadingInvoiceId === invoice.invoiceId}
                      className={`rounded-md px-3 py-1 text-sm font-medium text-white transition-colors duration-200 ${
                        loadingInvoiceId === invoice.invoiceId
                          ? 'cursor-not-allowed bg-gray-400'
                          : 'bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2'
                      }`}
                      title="Pay this overdue invoice using Zoho Payments"
                    >
                      {loadingInvoiceId === invoice.invoiceId ? (
                        <span className="flex items-center">
                          <svg
                            className="-ml-1 mr-2 h-4 w-4 animate-spin text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            ></circle>
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                          </svg>
                          Processing...
                        </span>
                      ) : (
                        'Pay Now'
                      )}
                    </button>
                  ) : (
                    <span className="text-sm text-gray-500">
                      {invoice.invoiceStatus === 'Closed' || invoice.invoiceStatus === 'Paid'
                        ? 'Paid'
                        : 'No payment required'}
                    </span>
                  )}
                </TableCell>
              </TableRow>
            ))}
        </TableBody>
      </Table>

      {isPopupOpen && (
        <InvoiceDetails customerInvoice={invoice} open={isPopupOpen} setOpen={setIsPopupOpen} customer={customer} />
      )}
    </>
  )
}
