'use client'

import { Badge } from '@/components/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/table'
import LoadingScreen from '@/customComponents/LoadingScreen'
import { retailerAtom } from '@/customComponents/providers'
import { indianCurrencyFormat } from '@/utils/formats'
import { useFetchInvoicesByCustomerId } from '@/utils/requests'
import { createZohoPaymentSession, debugZohoIntegration, initializeZohoWidget } from '@/utils/zohoPayment'
import { initializeZohoPaymentResponse } from '@/utils/zohoPaymentLinks'
import { useAtomValue } from 'jotai'
import moment from 'moment'
import { useEffect, useState } from 'react'
import { InvoiceDetails } from './invoiceDetails'
// Utility function for class names
function classNames(...classes) {
  return classes.filter(Boolean).join(' ')
}

export const InvoicesPage = () => {
  const [loading, setLoading] = useState(true)
  const customer = useAtomValue(retailerAtom)
  const [invoice, setInvoice] = useState({})
  const [isPopupOpen, setIsPopupOpen] = useState(false)
  const [paymentError, setPaymentError] = useState(null)
  const [loadingInvoiceId, setLoadingInvoiceId] = useState(null)

  const invoices = useFetchInvoicesByCustomerId(customer.customerId)

  const handleOpenPopup = ({ selectedInvoice }) => {
    setInvoice(selectedInvoice)
    setIsPopupOpen(true)
  }

  // Working payment integration using POC approach (our previous implementation)
  const handlePayNowPOC = async (selectedInvoice) => {
    try {
      console.log('🎯 PAY NOW POC: Button clicked for invoice', selectedInvoice.invoiceNumber)
      setLoadingInvoiceId(selectedInvoice.invoiceId)
      setPaymentError(null)

      console.log('📡 API CALL: Creating payment session...')
      const paymentSession = await createZohoPaymentSession({
        amount: selectedInvoice.total,
        currency_code: 'INR',
        customer_id: customer.customerId,
        invoice_number: selectedInvoice.invoiceNumber,
        description: `Payment for AquaPartner Invoice ${selectedInvoice.invoiceNumber}`,
      })

      console.log('✅ SESSION CREATED: Payment session response:', paymentSession)
      console.log('🔑 SESSION ID: Extracted session_id:', paymentSession.session_id)

      if (!paymentSession.session_id) {
        console.error('❌ SESSION ERROR: No session_id in response')
        throw new Error('Invalid payment session response')
      }

      console.log('🚀 WIDGET INIT: Calling initializeZohoWidget with:')
      console.log('  - Session ID:', paymentSession.session_id)
      console.log('  - Amount:', selectedInvoice.total)
      console.log('  - Invoice:', selectedInvoice.invoiceNumber)

      initializeZohoWidget(paymentSession.session_id, selectedInvoice.total, selectedInvoice.invoiceNumber)

      console.log('✅ WIDGET CALL: initializeZohoWidget called successfully')
      setLoadingInvoiceId(null)
    } catch (error) {
      console.error('❌ PAY NOW POC ERROR:', error)
      setLoadingInvoiceId(null)
      setPaymentError(`Failed to initiate payment: ${error.message}`)
    }
  }

  // New payment integration with automatic method selection and fallback
  const handlePayNowAdvanced = async (selectedInvoice) => {
    try {
      console.log('🎯 PAY NOW ADVANCED: Button clicked for invoice', selectedInvoice.invoiceNumber)
      setLoadingInvoiceId(selectedInvoice.invoiceId)
      setPaymentError(null)

      console.log('📡 API CALL: Creating payment with automatic method selection...')
      console.log('👤 CUSTOMER DATA (RAW):', customer)
      console.log('👤 CUSTOMER DATA (DETAILED):', {
        customerId: customer.customerId,
        customerName: customer.customerName,
        Email: customer.Email,
        email: customer['email'], // Check lowercase version too
        mobileNumber: customer.mobileNumber,
        companyName: customer.companyName,
      })

      // Enhanced email handling with multiple fallback strategies
      let finalCustomerEmail = null

      // Try different email field variations
      if (customer.Email && customer.Email.trim() !== '') {
        finalCustomerEmail = customer.Email.trim()
        console.log('📧 EMAIL: Using customer.Email:', finalCustomerEmail)
      } else if (customer['email'] && customer['email'].trim() !== '') {
        finalCustomerEmail = customer['email'].trim()
        console.log('📧 EMAIL: Using customer.email (lowercase):', finalCustomerEmail)
      } else {
        // Generate fallback email
        finalCustomerEmail = `${customer.customerId}@aquapartner.com`
        console.log('📧 EMAIL: Using fallback email:', finalCustomerEmail)
      }

      console.log('📧 EMAIL FINAL:', finalCustomerEmail)

      // Force payment links strategy for consistency across mobile and desktop
      console.log('🔗 PAYMENT STRATEGY: Forcing payment links for consistent user experience')

      const paymentResponse = await fetch('/api/zoho/payments/create-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Force-Payment-Method': 'links', // Force payment links strategy
        },
        body: JSON.stringify({
          amount: selectedInvoice.total,
          currency: 'INR',
          customer_id: customer.customerId,
          customer_name: customer.customerName,
          customer_email: finalCustomerEmail,
          customer_phone: customer.mobileNumber,
          invoice_number: selectedInvoice.invoiceNumber,
          description: `Payment for AquaPartner Invoice ${selectedInvoice.invoiceNumber}`,
          redirect_url: `${window.location.origin}/payment-success?invoice=${selectedInvoice.invoiceNumber}`,
          reference_id: `${customer.customerId}_${selectedInvoice.invoiceNumber}_${Date.now()}`,
        }),
      })

      console.log('📡 PAYMENT API: Response status:', paymentResponse.status, paymentResponse.statusText)

      if (!paymentResponse.ok) {
        const errorText = await paymentResponse.text()
        console.error('❌ PAYMENT API ERROR: Response not ok:', errorText)
        throw new Error(`Failed to create payment: ${paymentResponse.statusText}`)
      }

      const paymentResponseData = await paymentResponse.json()
      console.log('✅ PAYMENT CREATED: Payment response:', paymentResponseData)
      console.log('🔧 PAYMENT METHOD: Used method:', paymentResponseData.payment_method)

      if (paymentResponseData.fallback_used) {
        console.log('🔄 FALLBACK: Original method failed, used fallback')
        console.log('  - Original:', paymentResponseData.original_method)
        console.log('  - Final:', paymentResponseData.final_method)
      }

      // Initialize payment based on the method used
      initializeZohoPaymentResponse(paymentResponseData, selectedInvoice.invoiceNumber)

      console.log('✅ PAYMENT INIT: Payment initialization completed')
      setLoadingInvoiceId(null)
    } catch (error) {
      console.error('❌ PAY NOW ADVANCED ERROR:', error)
      setLoadingInvoiceId(null)
      setPaymentError(`Failed to initiate payment: ${error.message}`)
    }
  }

  /* const initiatePayment = async ({ selectedInvoice }) => {
    try {
      const response = await fetch('/api/initiatePayment', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }, // invoiceNo: selectedInvoice?.invoiceNumber
        body: JSON.stringify({ amount: selectedInvoice?.total, invoiceNo: selectedInvoice?.invoiceNumber }),
      })
      const data = await response.json()
      if (data.error) {
        alert('Error, Try again after some time')
        return
      }
      let config = {
        account_id: '***********',
        domain: 'IN',
        otherOptions: {
          api_key: '1003.f35b9411653295bb03db1e8490dc6cdd.0f625d54ec97f4eba7bedd0dc6fc23b8',
        },
      }
      let instance = new window.ZPayments(config)
      try {
        let options = {
          amount: data.paymentSession.amount,
          currency_code: 'INR',
          payments_session_id: data.paymentSession.payments_session_id,
          currency_symbol: '₹',
          business: customer?.companyName,
          description: '',
          address: {
            name: customer?.customerName,
            email: customer?.Email ?? '<EMAIL>',
          },
        }
        let paymentResponse = await instance.requestPaymentMethod(options)
      } catch (error) {
        console.log(JSON.stringify(error))
        if (err.code != 'widget_closed') {
          console.error('Widget Closed')
        }
      } finally {
        await instance.close()
      }
    } catch (error) {
      console.log('Payment Error : ', error)
    }
  }
 */

  useEffect(() => {
    if (invoices?.results) {
      setLoading(false)
    }
  }, [invoices])

  useEffect(() => {
    // Check Zoho script status and initialize debug utilities
    console.log('🔍 SCRIPT: Checking ZPayments availability')
    console.log('✅ SCRIPT: ZPayments available:', !!window.ZPayments)
    if (!window.ZPayments) {
      console.warn('⚠️ SCRIPT: ZPayments not yet available, may need to wait')
    }

    // Initialize debug utilities
    debugZohoIntegration()
  }, [])

  /* useEffect(() => {
    // Load Zoho Payments widget dynamically
    const script = document.createElement('script')
    script.src = 'https://static.zohocdn.com/zpay/zpay-js/v1/zpayments.js'
    script.async = true
    document.body.appendChild(script)
    return () => {
      document.body.removeChild(script)
    }
  }, []) */

  return (
    <>
      {loading && <LoadingScreen />}

      {/* Payment Error Display */}
      {paymentError && (
        <div className="mb-4 rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Payment Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{paymentError}</p>
              </div>
              <div className="mt-4">
                <button
                  type="button"
                  onClick={() => setPaymentError(null)}
                  className="rounded-md bg-red-50 px-2 py-1.5 text-sm font-medium text-red-800 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-600 focus:ring-offset-2 focus:ring-offset-red-50"
                >
                  Dismiss
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      <Table className="mt-2 [--gutter:theme(spacing.6)] lg:[--gutter:theme(spacing.10)]">
        <TableHead>
          <TableRow>
            <TableHeader>Invoice date</TableHeader>
            <TableHeader>Invoice Number</TableHeader>
            <TableHeader className="text-right">Total</TableHeader>
            <TableHeader>Status</TableHeader>
            <TableHeader></TableHeader>
          </TableRow>
        </TableHead>
        <TableBody>
          {invoices.results &&
            invoices.results.map((invoice, index) => (
              <TableRow className="hover:bg-blue-50" key={index} title={`Order # No`}>
                <TableCell className="" onClick={() => handleOpenPopup({ selectedInvoice: invoice })}>
                  {moment(invoice.invoiceDate).format('DD-MM-YYYY')}
                </TableCell>
                <TableCell onClick={() => handleOpenPopup({ selectedInvoice: invoice })}>
                  {invoice.invoiceNumber}
                </TableCell>
                <TableCell className="text-right" onClick={() => handleOpenPopup({ selectedInvoice: invoice })}>
                  {indianCurrencyFormat(invoice.total)}
                </TableCell>

                <TableCell onClick={() => handleOpenPopup({ selectedInvoice: invoice })}>
                  <Badge
                    color={
                      invoice.invoiceStatus === 'Void'
                        ? 'zinc'
                        : invoice.invoiceStatus === 'Closed'
                          ? 'green'
                          : invoice.invoiceStatus === 'Invoiced'
                            ? 'blue'
                            : invoice.invoiceStatus === 'Partially Delivered'
                              ? 'yellow'
                              : invoice.invoiceStatus === 'Rejected'
                                ? 'red'
                                : invoice.invoiceStatus === 'Overdue'
                                  ? 'red'
                                  : 'yellow'
                    }
                  >
                    {invoice.invoiceStatus}
                  </Badge>
                </TableCell>
                <TableCell>
                  {invoice.invoiceStatus === 'Overdue' ? (
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handlePayNowAdvanced(invoice)
                      }}
                      disabled={loadingInvoiceId === invoice.invoiceId}
                      className={`rounded-md px-3 py-1 text-sm font-medium text-white transition-colors duration-200 ${
                        loadingInvoiceId === invoice.invoiceId
                          ? 'cursor-not-allowed bg-gray-400'
                          : 'bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2'
                      }`}
                      title="Pay this overdue invoice using Zoho Payments"
                    >
                      {loadingInvoiceId === invoice.invoiceId ? (
                        <span className="flex items-center">
                          <svg
                            className="-ml-1 mr-2 h-4 w-4 animate-spin text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            ></circle>
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                          </svg>
                          Processing...
                        </span>
                      ) : (
                        'Pay Now'
                      )}
                    </button>
                  ) : (
                    <span className="text-sm text-gray-500">
                      {invoice.invoiceStatus === 'Closed' || invoice.invoiceStatus === 'Paid'
                        ? 'Paid'
                        : 'No payment required'}
                    </span>
                  )}
                </TableCell>
                {/* <TableCell>
                  <Link
                    href="#"
                    onClick={() => initiatePayment({ selectedInvoice: invoice })}
                    className="text-blue-700"
                  >
                    PayNow
                  </Link>
                </TableCell> */}
              </TableRow>
            ))}
        </TableBody>
      </Table>

      {isPopupOpen && (
        <InvoiceDetails customerInvoice={invoice} open={isPopupOpen} setOpen={setIsPopupOpen} customer={customer} />
      )}
    </>
  )
}
