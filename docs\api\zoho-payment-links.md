# Zoho Payment Links API

## Overview

The Zoho Payment Links API allows you to create shareable payment links that can be sent to customers via email, SMS, or other channels. This API integrates with the existing Zoho payment infrastructure and follows the same patterns as the payment session API.

**Implementation Note**: This API uses an alternative implementation that creates payment sessions and generates shareable checkout URLs, as the direct Zoho Payment Links API endpoint is not available in the current account configuration.

## Endpoint

```
POST /api/zoho/payments/create-link
GET  /api/zoho/payments/create-link
```

## Authentication

Uses the same Zoho OAuth token management system as other payment APIs. The service automatically handles token refresh and fallback to database-stored tokens.

## Create Payment Link

### Request

**Method:** `POST`  
**URL:** `/api/zoho/payments/create-link`  
**Content-Type:** `application/json`

#### Required Fields

| Field            | Type   | Description                         | Example                      |
| ---------------- | ------ | ----------------------------------- | ---------------------------- |
| `amount`         | number | Payment amount (must be positive)   | `100.50`                     |
| `description`    | string | Payment description (max 500 chars) | `"Payment for Order #12345"` |
| `customer_email` | string | Customer email address              | `"<EMAIL>"`     |

#### Optional Fields

| Field                    | Type    | Description                       | Default | Example                                 |
| ------------------------ | ------- | --------------------------------- | ------- | --------------------------------------- |
| `currency`               | string  | Payment currency                  | `"INR"` | `"INR"`                                 |
| `invoice_number`         | string  | Invoice number (max 50 chars)     | -       | `"INV-12345"`                           |
| `customer_id`            | string  | Customer identifier               | -       | `"CUST-001"`                            |
| `customer_name`          | string  | Customer name                     | -       | `"John Doe"`                            |
| `customer_phone`         | string  | Customer phone number             | -       | `"+919876543210"`                       |
| `redirect_url`           | string  | URL to redirect after payment     | -       | `"https://yourapp.com/success"`         |
| `reference_id`           | string  | Internal reference ID             | -       | `"REF-12345"`                           |
| `expires_at`             | number  | Unix timestamp for expiry         | 7 days  | `1640995200`                            |
| `send_sms`               | boolean | Send link via SMS                 | `false` | `true`                                  |
| `send_email`             | boolean | Send link via email               | `false` | `true`                                  |
| `partial_payments`       | boolean | Allow partial payments            | `false` | `true`                                  |
| `minimum_partial_amount` | number  | Min amount for partial payments   | -       | `50.0`                                  |
| `meta_data`              | array   | Additional metadata (max 5 items) | `[]`    | `[{"key": "order_id", "value": "123"}]` |

#### Example Request

```json
{
  "amount": 100.5,
  "currency": "INR",
  "description": "Payment for Order #12345",
  "customer_email": "<EMAIL>",
  "customer_name": "John Doe",
  "customer_id": "CUST-001",
  "invoice_number": "INV-12345",
  "send_email": true,
  "partial_payments": false,
  "meta_data": [
    { "key": "order_id", "value": "ORD-123" },
    { "key": "product_type", "value": "aquaculture" }
  ]
}
```

### Response

#### Success Response (201 Created)

```json
{
  "success": true,
  "message": "Payment link created successfully",
  "data": {
    "payment_link_id": "pl_123456789",
    "payment_link_url": "https://payments.zoho.in/checkout/pl_123456789",
    "amount": 100.5,
    "currency": "INR",
    "description": "Payment for Order #12345",
    "customer_email": "<EMAIL>",
    "status": "active",
    "created_time": 1640995200,
    "expires_at": 1641600000,
    "transaction_id": "64a1b2c3d4e5f6789",
    "send_sms": false,
    "send_email": true,
    "partial_payments": false
  },
  "payment_link": {
    // Full Zoho payment link object
  }
}
```

#### Error Responses

**400 Bad Request - Missing Required Fields**

```json
{
  "error": "Missing required fields",
  "message": "amount, description, and customer_email are required",
  "required_fields": ["amount", "description", "customer_email"]
}
```

**400 Bad Request - Invalid Email**

```json
{
  "error": "Invalid email",
  "message": "Please provide a valid email address"
}
```

**401 Unauthorized - Authentication Error**

```json
{
  "error": "Authentication Error",
  "message": "No Zoho Payment token found",
  "details": "Please check your Zoho Payment configuration"
}
```

## Get API Documentation

### Request

**Method:** `GET`  
**URL:** `/api/zoho/payments/create-link`

### Response

Returns comprehensive API documentation including field requirements, examples, and feature descriptions.

## Features

- ✅ **Shareable Links**: Create payment links that can be shared via any channel
- ✅ **Email/SMS Notifications**: Automatic delivery to customers
- ✅ **Partial Payments**: Support for installment payments
- ✅ **Custom Expiry**: Set custom expiration dates
- ✅ **Redirect Support**: Custom success/failure URLs
- ✅ **Metadata Tracking**: Store additional context data
- ✅ **Database Integration**: Automatic transaction logging
- ✅ **Error Handling**: Comprehensive validation and error responses

## Integration with Existing System

This API seamlessly integrates with the existing payment infrastructure:

- Uses the same `zohoPaymentService` for authentication and API calls
- Stores transactions in the same `PaymentTransaction` model
- Follows identical error handling patterns
- Uses the same environment variables and configuration
- Compatible with existing webhook and status checking systems

## Testing

Use the provided test script to validate the API:

```bash
node tests/api/zoho-payment-link-test.js
```

## Security Considerations

- Email validation prevents invalid recipients
- Amount validation ensures positive values
- Expiry date validation prevents past dates
- Partial payment validation ensures logical amounts
- Same OAuth token security as existing payment APIs

## Deployment

The API is configured for Azure App Service deployment with:

- `export const dynamic = 'force-dynamic'` for proper server-side rendering
- Environment variable compatibility
- Database connection handling
- Error logging and monitoring
