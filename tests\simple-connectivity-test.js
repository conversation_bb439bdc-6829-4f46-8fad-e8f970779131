const https = require('https')

function testConnectivity(url) {
  return new Promise((resolve, reject) => {
    const request = https.get(url, (response) => {
      let data = ''

      response.on('data', (chunk) => {
        data += chunk
      })

      response.on('end', () => {
        resolve({
          statusCode: response.statusCode,
          headers: response.headers,
          data: data,
        })
      })
    })

    request.on('error', (error) => {
      reject(error)
    })

    request.setTimeout(30000, () => {
      request.destroy()
      reject(new Error('Request timeout'))
    })
  })
}

async function runConnectivityTest() {
  const PRODUCTION_URL = 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net'

  console.log('🔗 Basic Connectivity Test')
  console.log('==========================')

  try {
    console.log(`Testing: ${PRODUCTION_URL}/api/zoho/health/`)

    const result = await testConnectivity(`${PRODUCTION_URL}/api/zoho/health/`)

    console.log(`✅ Connection successful!`)
    console.log(`Status Code: ${result.statusCode}`)
    console.log(`Content-Type: ${result.headers['content-type']}`)
    console.log(`Response Length: ${result.data.length} bytes`)

    if (result.statusCode === 200) {
      try {
        const jsonData = JSON.parse(result.data)
        console.log('\n📊 Health Check Data:')
        console.log(`Service: ${jsonData.service}`)
        console.log(`Status: ${jsonData.status}`)
        console.log(`Domain Config: ${jsonData.configuration?.domain}`)
        console.log(`Account ID: ${jsonData.configuration?.account_id}`)
        console.log(`Webhook Secret: ${jsonData.configuration?.webhook_secret}`)
      } catch (parseError) {
        console.log('\n⚠️ Response is not valid JSON')
        console.log('Raw response:', result.data.substring(0, 500))
      }
    } else {
      console.log('\n❌ Non-200 status code')
      console.log('Response:', result.data.substring(0, 500))
    }
  } catch (error) {
    console.log(`❌ Connection failed: ${error.message}`)
  }
}

runConnectivityTest()
