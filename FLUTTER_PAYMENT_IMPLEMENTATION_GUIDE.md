# Flutter Payment Implementation Guide (Official SDK)

## ⚠️ **Migration Notice**

**This guide has been updated for the official Zoho Payments SDK.** The backend endpoints now use the official SDK for enhanced security and compatibility.

## Complete Payment Service & Error Handling

## 🔧 Complete Payment Service Implementation

### ZohoPaymentService Class

```dart
import 'dart:convert';
import 'package:http/http.dart' as http;

class ZohoPaymentService {
  static const String _baseUrl = 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net';
  static const String _apiPath = '/api';

  // HTTP Client with timeout configuration
  static final http.Client _client = http.Client();
  static const Duration _timeout = Duration(seconds: 30);

  // Headers
  static Map<String, String> get _defaultHeaders => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // Endpoints with trailing slashes
  static String get _healthEndpoint => '$_baseUrl$_apiPath/zoho/health/';
  static String get _createSessionEndpoint => '$_baseUrl$_apiPath/zoho/payments/create-session/';
  static String get _paymentListEndpoint => '$_baseUrl$_apiPath/zoho/payments/list/';
  static String _paymentStatusEndpoint(String sessionId) =>
    '$_baseUrl$_apiPath/zoho/payments/status/$sessionId/';

  /// Check API health and configuration
  static Future<HealthCheckResponse> checkHealth() async {
    try {
      final response = await _client
          .get(Uri.parse(_healthEndpoint), headers: _defaultHeaders)
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return HealthCheckResponse.fromJson(data);
      } else {
        throw PaymentException(
          'Health check failed',
          statusCode: response.statusCode,
          details: response.body,
        );
      }
    } catch (e) {
      if (e is PaymentException) rethrow;
      throw PaymentException('Network error during health check: ${e.toString()}');
    }
  }

  /// Create a new payment session
  static Future<PaymentSessionResponse> createPaymentSession(PaymentRequest request) async {
    try {
      // Validate request
      _validatePaymentRequest(request);

      final response = await _client
          .post(
            Uri.parse(_createSessionEndpoint),
            headers: _defaultHeaders,
            body: jsonEncode(request.toJson()),
          )
          .timeout(_timeout);

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        return PaymentSessionResponse.fromJson(data);
      } else {
        final error = jsonDecode(response.body);
        throw PaymentException(
          error['message'] ?? 'Payment session creation failed',
          statusCode: response.statusCode,
          errorCode: error['error'],
          details: response.body,
        );
      }
    } catch (e) {
      if (e is PaymentException) rethrow;
      throw PaymentException('Network error during payment creation: ${e.toString()}');
    }
  }

  /// Get payment status by session ID
  static Future<PaymentStatusResponse> getPaymentStatus(String sessionId) async {
    try {
      if (sessionId.isEmpty) {
        throw PaymentException('Session ID cannot be empty');
      }

      final response = await _client
          .get(
            Uri.parse(_paymentStatusEndpoint(sessionId)),
            headers: _defaultHeaders,
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return PaymentStatusResponse.fromJson(data);
      } else {
        final error = jsonDecode(response.body);
        throw PaymentException(
          error['message'] ?? 'Failed to get payment status',
          statusCode: response.statusCode,
          errorCode: error['error'],
          details: response.body,
        );
      }
    } catch (e) {
      if (e is PaymentException) rethrow;
      throw PaymentException('Network error during status check: ${e.toString()}');
    }
  }

  /// Get customer payment list with pagination
  static Future<PaymentListResponse> getCustomerPayments({
    required String customerId,
    int page = 1,
    int limit = 10,
    String? status,
    String? fromDate,
    String? toDate,
  }) async {
    try {
      if (customerId.isEmpty) {
        throw PaymentException('Customer ID cannot be empty');
      }

      final queryParams = <String, String>{
        'customer_id': customerId,
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (status != null && status.isNotEmpty) queryParams['status'] = status;
      if (fromDate != null && fromDate.isNotEmpty) queryParams['from_date'] = fromDate;
      if (toDate != null && toDate.isNotEmpty) queryParams['to_date'] = toDate;

      final uri = Uri.parse(_paymentListEndpoint).replace(queryParameters: queryParams);

      final response = await _client
          .get(uri, headers: _defaultHeaders)
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return PaymentListResponse.fromJson(data);
      } else {
        final error = jsonDecode(response.body);
        throw PaymentException(
          error['message'] ?? 'Failed to get payment list',
          statusCode: response.statusCode,
          errorCode: error['error'],
          details: response.body,
        );
      }
    } catch (e) {
      if (e is PaymentException) rethrow;
      throw PaymentException('Network error during payment list retrieval: ${e.toString()}');
    }
  }

  /// Validate payment request before sending
  static void _validatePaymentRequest(PaymentRequest request) {
    if (request.amount <= 0) {
      throw PaymentException('Amount must be greater than 0');
    }

    if (request.description.isEmpty) {
      throw PaymentException('Description cannot be empty');
    }

    if (request.invoiceNumber.isEmpty) {
      throw PaymentException('Invoice number cannot be empty');
    }

    if (request.customerId.isEmpty) {
      throw PaymentException('Customer ID cannot be empty');
    }

    if (request.currency != 'INR') {
      throw PaymentException('Only INR currency is supported');
    }

    // Validate email format if provided
    if (request.customerEmail != null && request.customerEmail!.isNotEmpty) {
      final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
      if (!emailRegex.hasMatch(request.customerEmail!)) {
        throw PaymentException('Invalid email format');
      }
    }

    // Validate phone format if provided
    if (request.customerPhone != null && request.customerPhone!.isNotEmpty) {
      final phoneRegex = RegExp(r'^\+?[1-9]\d{1,14}$');
      if (!phoneRegex.hasMatch(request.customerPhone!.replaceAll(RegExp(r'[-\s]'), ''))) {
        throw PaymentException('Invalid phone number format');
      }
    }
  }

  /// Dispose HTTP client
  static void dispose() {
    _client.close();
  }
}
```

## 🚨 Error Handling

### PaymentException Class

```dart
class PaymentException implements Exception {
  final String message;
  final int? statusCode;
  final String? errorCode;
  final String? details;
  final DateTime timestamp;

  PaymentException(
    this.message, {
    this.statusCode,
    this.errorCode,
    this.details,
  }) : timestamp = DateTime.now();

  @override
  String toString() {
    return 'PaymentException: $message'
        '${statusCode != null ? ' (HTTP $statusCode)' : ''}'
        '${errorCode != null ? ' [$errorCode]' : ''}';
  }

  /// Check if this is a network-related error
  bool get isNetworkError => statusCode == null || statusCode! >= 500;

  /// Check if this is a client error (4xx)
  bool get isClientError => statusCode != null && statusCode! >= 400 && statusCode! < 500;

  /// Check if this is a validation error
  bool get isValidationError => statusCode == 400;

  /// Check if this is an authentication error
  bool get isAuthError => statusCode == 401 || statusCode == 403;

  /// Check if this is a not found error
  bool get isNotFoundError => statusCode == 404;

  /// Get user-friendly error message
  String get userFriendlyMessage {
    if (isNetworkError) {
      return 'Network connection error. Please check your internet connection and try again.';
    } else if (isValidationError) {
      return message; // Validation messages are usually user-friendly
    } else if (isAuthError) {
      return 'Authentication error. Please contact support.';
    } else if (isNotFoundError) {
      return 'The requested payment session was not found.';
    } else {
      return 'An error occurred while processing your payment. Please try again.';
    }
  }
}
```

### Error Handling Patterns

```dart
class PaymentErrorHandler {
  /// Handle payment errors with retry logic
  static Future<T> handleWithRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration delay = const Duration(seconds: 2),
  }) async {
    int attempts = 0;

    while (attempts < maxRetries) {
      try {
        return await operation();
      } catch (e) {
        attempts++;

        if (e is PaymentException) {
          // Don't retry client errors (4xx)
          if (e.isClientError) {
            rethrow;
          }

          // Retry network errors and server errors
          if (attempts < maxRetries && e.isNetworkError) {
            await Future.delayed(delay * attempts); // Exponential backoff
            continue;
          }
        }

        rethrow;
      }
    }

    throw PaymentException('Operation failed after $maxRetries attempts');
  }

  /// Show user-friendly error dialog
  static void showErrorDialog(BuildContext context, PaymentException error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Payment Error'),
        content: Text(error.userFriendlyMessage),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
```
