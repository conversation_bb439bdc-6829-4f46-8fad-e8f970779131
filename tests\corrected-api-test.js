const https = require('https');

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const requestOptions = {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...options.headers
      }
    };

    const request = https.request(url, requestOptions, (response) => {
      let data = '';
      
      response.on('data', (chunk) => {
        data += chunk;
      });
      
      response.on('end', () => {
        resolve({
          statusCode: response.statusCode,
          headers: response.headers,
          data: data
        });
      });
    });
    
    request.on('error', (error) => {
      reject(error);
    });
    
    if (options.body) {
      request.write(options.body);
    }
    
    request.setTimeout(30000, () => {
      request.destroy();
      reject(new Error('Request timeout'));
    });
    
    request.end();
  });
}

async function testCorrectedEndpoints() {
  const PRODUCTION_URL = 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net';
  
  console.log('🔧 Testing Corrected API Endpoints');
  console.log('==================================');
  
  // Test 1: Health Check (with trailing slash)
  console.log('\n1. Testing Health Check Endpoint...');
  try {
    const healthResult = await makeRequest(`${PRODUCTION_URL}/api/zoho/health/`);
    console.log(`✅ Health Check: ${healthResult.statusCode}`);
    
    if (healthResult.statusCode === 200) {
      const healthData = JSON.parse(healthResult.data);
      console.log(`   Status: ${healthData.status}`);
      console.log(`   Domain: ${healthData.configuration?.domain}`);
    }
  } catch (error) {
    console.log(`❌ Health Check Failed: ${error.message}`);
  }
  
  // Test 2: Payment Session Creation
  console.log('\n2. Testing Payment Session Creation...');
  const testPaymentData = {
    amount: 100.0,
    currency: 'INR',
    description: 'Corrected API Test Payment',
    invoice_number: `CORRECTED_TEST_${Date.now()}`,
    customer_id: 'test_customer_corrected',
    customer_name: 'Test Customer Corrected',
    customer_email: '<EMAIL>',
    customer_phone: '+91-**********'
  };
  
  let sessionId = null;
  
  try {
    const createResult = await makeRequest(`${PRODUCTION_URL}/api/zoho/payments/create-session/`, {
      method: 'POST',
      body: JSON.stringify(testPaymentData)
    });
    
    console.log(`✅ Payment Creation: ${createResult.statusCode}`);
    
    if (createResult.statusCode === 200) {
      const createData = JSON.parse(createResult.data);
      sessionId = createData.data?.payment_session_id;
      console.log(`   Session ID: ${sessionId}`);
    } else {
      console.log(`   Error: ${createResult.data}`);
    }
  } catch (error) {
    console.log(`❌ Payment Creation Failed: ${error.message}`);
  }
  
  // Test 3: Payment Status (if we have a session ID)
  if (sessionId) {
    console.log('\n3. Testing Payment Status Retrieval...');
    try {
      const statusResult = await makeRequest(`${PRODUCTION_URL}/api/zoho/payments/status/${sessionId}/`);
      console.log(`✅ Payment Status: ${statusResult.statusCode}`);
      
      if (statusResult.statusCode === 200) {
        const statusData = JSON.parse(statusResult.data);
        console.log(`   Success: ${statusData.success}`);
        console.log(`   Payment Session: ${statusData.payment_session ? 'Present' : 'Missing'}`);
      } else {
        console.log(`   Error: ${statusResult.data}`);
      }
    } catch (error) {
      console.log(`❌ Payment Status Failed: ${error.message}`);
    }
  }
  
  // Test 4: Payment List
  console.log('\n4. Testing Payment List...');
  try {
    const listResult = await makeRequest(`${PRODUCTION_URL}/api/zoho/payments/list/?customer_id=test_customer_corrected&limit=5`);
    console.log(`✅ Payment List: ${listResult.statusCode}`);
    
    if (listResult.statusCode === 200) {
      const listData = JSON.parse(listResult.data);
      console.log(`   Success: ${listData.success}`);
      console.log(`   Transactions: ${Array.isArray(listData.transactions) ? listData.transactions.length : 'Not an array'}`);
    } else {
      console.log(`   Error: ${listResult.data}`);
    }
  } catch (error) {
    console.log(`❌ Payment List Failed: ${error.message}`);
  }
  
  // Test 5: Webhook Endpoint
  console.log('\n5. Testing Webhook Endpoint...');
  try {
    const webhookResult = await makeRequest(`${PRODUCTION_URL}/api/zoho/webhooks/payment/`);
    console.log(`✅ Webhook Endpoint: ${webhookResult.statusCode}`);
    
    if (webhookResult.statusCode === 200) {
      const webhookData = JSON.parse(webhookResult.data);
      console.log(`   Endpoint: ${webhookData.endpoint ? 'Configured' : 'Missing'}`);
      console.log(`   Events: ${webhookData.supported_events ? webhookData.supported_events.length : 0} supported`);
    }
  } catch (error) {
    console.log(`❌ Webhook Test Failed: ${error.message}`);
  }
  
  console.log('\n🏁 Corrected API Testing Complete');
}

testCorrectedEndpoints().catch(console.error);
