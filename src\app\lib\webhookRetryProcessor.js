/**
 * Webhook Retry Processor
 * 
 * Background job processor for handling failed webhook retries
 * Can be run as a scheduled job or triggered manually
 */

import WebhookEvent from '@/app/models/WebhookEvent'
import { Logger } from './monitoring.js'
import { webhookRetryManager } from './webhookSecurity.js'
import zohoPaymentService from './zohoPaymentService.js'
import connectedDB from '@/app/config/database'

/**
 * Process all pending webhook retries
 */
export async function processWebhookRetries() {
  try {
    await connectedDB()
    
    Logger.info('Starting webhook retry processing job', {
      method: 'processWebhookRetries',
      timestamp: new Date().toISOString()
    })

    // Find all retryable webhook events
    const retryableEvents = await WebhookEvent.findRetryableEvents()
    
    if (retryableEvents.length === 0) {
      Logger.info('No webhook retries pending', {
        method: 'processWebhookRetries'
      })
      return {
        success: true,
        processed: 0,
        message: 'No retries pending'
      }
    }

    Logger.info('Found webhook events to retry', {
      method: 'processWebhookRetries',
      count: retryableEvents.length
    })

    let successCount = 0
    let failureCount = 0
    const results = []

    // Process each retryable event
    for (const webhookEvent of retryableEvents) {
      try {
        Logger.info('Processing webhook retry', {
          method: 'processWebhookRetries',
          webhookId: webhookEvent.webhook_id,
          eventId: webhookEvent.event_id,
          attempt: webhookEvent.retry_count + 1,
          eventType: webhookEvent.event_type
        })

        // Create processing function for this webhook
        const processingFunction = createWebhookProcessingFunction(webhookEvent)
        
        // Process with retry manager
        const result = await webhookRetryManager.processWithRetry(webhookEvent, processingFunction)
        
        // Mark as completed
        await webhookEvent.markAsCompleted(
          result.transactionId,
          result.statusData?.status,
          result.processingTime
        )

        successCount++
        results.push({
          webhookId: webhookEvent.webhook_id,
          eventId: webhookEvent.event_id,
          status: 'success',
          result
        })

        Logger.info('Webhook retry succeeded', {
          method: 'processWebhookRetries',
          webhookId: webhookEvent.webhook_id,
          eventId: webhookEvent.event_id,
          finalAttempt: webhookEvent.retry_count + 1
        })

      } catch (error) {
        // Mark as permanently failed
        await webhookEvent.markAsFailed(error, null)
        
        failureCount++
        results.push({
          webhookId: webhookEvent.webhook_id,
          eventId: webhookEvent.event_id,
          status: 'failed',
          error: error.message
        })

        Logger.error('Webhook retry permanently failed', {
          method: 'processWebhookRetries',
          webhookId: webhookEvent.webhook_id,
          eventId: webhookEvent.event_id,
          error: error.message,
          totalAttempts: webhookEvent.retry_count + 1
        })
      }
    }

    const summary = {
      success: true,
      processed: retryableEvents.length,
      successful: successCount,
      failed: failureCount,
      results
    }

    Logger.info('Webhook retry processing completed', {
      method: 'processWebhookRetries',
      ...summary
    })

    return summary

  } catch (error) {
    Logger.error('Webhook retry processing job failed', {
      method: 'processWebhookRetries',
      error: error.message,
      stack: error.stack
    })

    return {
      success: false,
      error: error.message,
      processed: 0
    }
  }
}

/**
 * Create processing function for a specific webhook event
 */
function createWebhookProcessingFunction(webhookEvent) {
  return async () => {
    const webhookData = webhookEvent.raw_data
    const {
      event_type,
      payment_session_id,
      payment_link_id,
      payment_id,
      error_code,
      error_message
    } = webhookData

    // Determine transaction ID
    const transactionId = payment_session_id || `payment_link_${payment_link_id}`

    // Get transaction
    const transaction = await zohoPaymentService.getTransaction(transactionId)
    
    if (!transaction) {
      const error = new Error(`Transaction not found for ID: ${transactionId}`)
      error.status = 404
      throw error
    }

    // Add webhook event to transaction
    await transaction.addWebhookEvent(event_type, webhookData)

    // Determine status data based on event type
    let statusData = {}

    switch (event_type) {
      case 'payment.succeeded':
        statusData = {
          status: 'succeeded',
          payment_id,
          payment_method: webhookData.payment_method,
        }
        break

      case 'payment.failed':
        statusData = {
          status: 'failed',
          error_code,
          error_message,
        }
        break

      case 'payment.pending':
        statusData = {
          status: 'pending',
          payment_id,
        }
        break

      case 'payment.cancelled':
        statusData = {
          status: 'cancelled',
        }
        break

      case 'payment_session.expired':
        statusData = {
          status: 'expired',
        }
        break

      default:
        Logger.warn('Unhandled webhook event type in retry', {
          eventType: event_type,
          webhookId: webhookEvent.webhook_id,
          eventId: webhookEvent.event_id
        })
        return {
          success: true,
          message: 'Webhook received but not processed',
          event_type,
          unhandled: true,
          transactionId
        }
    }

    // Update transaction status
    if (Object.keys(statusData).length > 0) {
      await zohoPaymentService.updateTransactionStatus(transactionId, statusData)
    }

    return {
      success: true,
      transactionId,
      statusData,
      event_type,
      processingTime: Date.now() - webhookEvent.webhook_received_at.getTime()
    }
  }
}

/**
 * Get webhook processing statistics
 */
export async function getWebhookStats(hours = 24) {
  try {
    await connectedDB()
    
    const stats = await WebhookEvent.getProcessingStats(hours)
    const failedEvents = await WebhookEvent.findFailedEvents(10)
    const retryableEvents = await WebhookEvent.findRetryableEvents()

    return {
      success: true,
      timeframe_hours: hours,
      processing_stats: stats,
      failed_events_sample: failedEvents.map(event => ({
        webhook_id: event.webhook_id,
        event_id: event.event_id,
        event_type: event.event_type,
        error_message: event.processing_error,
        retry_count: event.retry_count,
        received_at: event.webhook_received_at
      })),
      pending_retries: retryableEvents.length,
      retry_queue_sample: retryableEvents.slice(0, 5).map(event => ({
        webhook_id: event.webhook_id,
        event_id: event.event_id,
        event_type: event.event_type,
        retry_count: event.retry_count,
        retry_after: event.retry_after
      }))
    }
  } catch (error) {
    Logger.error('Failed to get webhook stats', {
      method: 'getWebhookStats',
      error: error.message
    })
    
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * Manually retry a specific webhook event
 */
export async function retryWebhookEvent(eventId) {
  try {
    await connectedDB()
    
    const webhookEvent = await WebhookEvent.findByEventId(eventId)
    
    if (!webhookEvent) {
      return {
        success: false,
        error: 'Webhook event not found',
        event_id: eventId
      }
    }

    if (webhookEvent.processing_status === 'completed') {
      return {
        success: false,
        error: 'Webhook event already completed',
        event_id: eventId
      }
    }

    // Create processing function
    const processingFunction = createWebhookProcessingFunction(webhookEvent)
    
    // Process with retry manager
    const result = await webhookRetryManager.processWithRetry(webhookEvent, processingFunction)
    
    // Mark as completed
    await webhookEvent.markAsCompleted(
      result.transactionId,
      result.statusData?.status,
      result.processingTime
    )

    Logger.info('Manual webhook retry succeeded', {
      method: 'retryWebhookEvent',
      eventId,
      webhookId: webhookEvent.webhook_id
    })

    return {
      success: true,
      event_id: eventId,
      result
    }

  } catch (error) {
    Logger.error('Manual webhook retry failed', {
      method: 'retryWebhookEvent',
      eventId,
      error: error.message
    })

    return {
      success: false,
      event_id: eventId,
      error: error.message
    }
  }
}
