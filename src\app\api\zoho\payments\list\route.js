import zohoPaymentService from '@/app/lib/zohoPaymentService'

// Force dynamic rendering to prevent static generation
export const dynamic = 'force-dynamic'

/**
 * GET /api/zoho/payments/list
 * List payments for a customer with pagination and filtering
 */
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url)

    const customer_id = searchParams.get('customer_id')
    const page = parseInt(searchParams.get('page')) || 1
    const limit = parseInt(searchParams.get('limit')) || 10
    const status = searchParams.get('status')
    const from_date = searchParams.get('from_date')
    const to_date = searchParams.get('to_date')

    if (!customer_id) {
      return new Response(
        JSON.stringify({
          error: 'Missing customer ID',
          message: 'customer_id parameter is required',
        }),
        { status: 400 }
      )
    }

    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 100) {
      return new Response(
        JSON.stringify({
          error: 'Invalid pagination parameters',
          message: 'page must be >= 1, limit must be between 1 and 100',
        }),
        { status: 400 }
      )
    }

    // Validate status if provided
    if (status) {
      const validStatuses = ['created', 'pending', 'succeeded', 'failed', 'cancelled', 'expired']
      if (!validStatuses.includes(status)) {
        return new Response(
          JSON.stringify({
            error: 'Invalid status',
            message: `Status must be one of: ${validStatuses.join(', ')}`,
          }),
          { status: 400 }
        )
      }
    }

    const options = {
      page,
      limit,
      status,
      from_date,
      to_date,
    }

    const result = await zohoPaymentService.getCustomerTransactions(customer_id, options)

    // Format response
    const formattedTransactions = result.transactions.map((transaction) => ({
      id: transaction._id,
      payment_session_id: transaction.payments_session_id,
      payment_id: transaction.payment_id,
      amount: transaction.amount,
      currency: transaction.currency,
      description: transaction.description,
      invoice_number: transaction.invoice_number,
      status: transaction.status,
      payment_method: transaction.payment_method,
      customer_id: transaction.customer_id,
      customer_name: transaction.customer_name,
      reference_id: transaction.reference_id,
      created_at: transaction.createdAt,
      updated_at: transaction.updatedAt,
      session_expires_at: transaction.session_expires_at,
      payment_completed_time: transaction.payment_completed_time,
    }))

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Transactions retrieved successfully',
        data: {
          transactions: formattedTransactions,
          pagination: result.pagination,
          filters: {
            customer_id,
            status,
            from_date,
            to_date,
          },
        },
      }),
      { status: 200 }
    )
  } catch (error) {
    console.error('Error listing payments:', error.message)

    return new Response(
      JSON.stringify({
        error: 'Payment listing failed',
        message: error.message,
        details: 'An unexpected error occurred while retrieving payments',
      }),
      { status: 500 }
    )
  }
}

/**
 * POST /api/zoho/payments/list
 * Advanced search for payments with complex filters
 */
export async function POST(request) {
  try {
    const body = await request.json()

    const {
      customer_ids = [],
      statuses = [],
      amount_range = {},
      date_range = {},
      invoice_numbers = [],
      page = 1,
      limit = 10,
      sort_by = 'created_at',
      sort_order = 'desc',
    } = body

    // Validate pagination
    if (page < 1 || limit < 1 || limit > 100) {
      return new Response(
        JSON.stringify({
          error: 'Invalid pagination parameters',
          message: 'page must be >= 1, limit must be between 1 and 100',
        }),
        { status: 400 }
      )
    }

    // Validate sort parameters
    const validSortFields = ['created_at', 'updated_at', 'amount', 'status']
    const validSortOrders = ['asc', 'desc']

    if (!validSortFields.includes(sort_by) || !validSortOrders.includes(sort_order)) {
      return new Response(
        JSON.stringify({
          error: 'Invalid sort parameters',
          message: `sort_by must be one of: ${validSortFields.join(', ')}, sort_order must be: ${validSortOrders.join(', ')}`,
        }),
        { status: 400 }
      )
    }

    // Build search options
    const searchOptions = {
      customer_ids,
      statuses,
      amount_range,
      date_range,
      invoice_numbers,
      page,
      limit,
      sort_by,
      sort_order,
    }

    const result = await zohoPaymentService.advancedSearchTransactions(searchOptions)

    // Format response
    const formattedTransactions = result.transactions.map((transaction) => ({
      id: transaction._id,
      payment_session_id: transaction.payments_session_id,
      payment_id: transaction.payment_id,
      amount: transaction.amount,
      currency: transaction.currency,
      description: transaction.description,
      invoice_number: transaction.invoice_number,
      status: transaction.status,
      payment_method: transaction.payment_method,
      customer_id: transaction.customer_id,
      customer_name: transaction.customer_name,
      reference_id: transaction.reference_id,
      created_at: transaction.createdAt,
      updated_at: transaction.updatedAt,
      session_expires_at: transaction.session_expires_at,
      payment_completed_time: transaction.payment_completed_time,
    }))

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Advanced search completed successfully',
        data: {
          transactions: formattedTransactions,
          pagination: result.pagination,
          search_criteria: result.search_criteria,
        },
      }),
      { status: 200 }
    )
  } catch (error) {
    console.error('Error in advanced search:', error.message)

    return new Response(
      JSON.stringify({
        error: 'Advanced search failed',
        message: error.message,
        details: 'An unexpected error occurred during advanced search',
      }),
      { status: 500 }
    )
  }
}
