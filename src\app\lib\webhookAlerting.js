/**
 * Webhook Alerting System
 * Monitors webhook processing health and sends alerts for failures and security violations
 */

import { Logger } from './monitoring'
import Webhook<PERSON>vent from '@/app/models/WebhookEvent'
import connectedDB from '@/app/config/database'

// Alert thresholds configuration
const ALERT_THRESHOLDS = {
  FAILURE_RATE: 0.1, // 10% failure rate
  SECURITY_VIOLATION_RATE: 0.05, // 5% security violation rate
  PROCESSING_TIME_P95: 5000, // 5 seconds
  CONSECUTIVE_FAILURES: 5,
  TIME_WINDOW_MINUTES: 15,
  MIN_EVENTS_FOR_ALERT: 10
}

// Alert types
const ALERT_TYPES = {
  HIGH_FAILURE_RATE: 'high_failure_rate',
  SECURITY_VIOLATIONS: 'security_violations',
  SLOW_PROCESSING: 'slow_processing',
  CONSECUTIVE_FAILURES: 'consecutive_failures',
  WEBHOOK_ENDPOINT_DOWN: 'webhook_endpoint_down'
}

/**
 * Check webhook health and trigger alerts if necessary
 */
export async function checkWebhookHealth() {
  try {
    await connectedDB()
    
    const timeWindow = new Date(Date.now() - ALERT_THRESHOLDS.TIME_WINDOW_MINUTES * 60 * 1000)
    
    // Get recent webhook events
    const recentEvents = await WebhookEvent.find({
      webhook_received_at: { $gte: timeWindow }
    }).lean()

    if (recentEvents.length < ALERT_THRESHOLDS.MIN_EVENTS_FOR_ALERT) {
      Logger.info('Insufficient events for health check', {
        method: 'checkWebhookHealth',
        eventCount: recentEvents.length,
        minRequired: ALERT_THRESHOLDS.MIN_EVENTS_FOR_ALERT
      })
      return { alerts: [], healthy: true }
    }

    const alerts = []

    // Check failure rate
    const failureRateAlert = await checkFailureRate(recentEvents)
    if (failureRateAlert) alerts.push(failureRateAlert)

    // Check security violations
    const securityAlert = await checkSecurityViolations(recentEvents)
    if (securityAlert) alerts.push(securityAlert)

    // Check processing performance
    const performanceAlert = await checkProcessingPerformance(recentEvents)
    if (performanceAlert) alerts.push(performanceAlert)

    // Check consecutive failures
    const consecutiveFailuresAlert = await checkConsecutiveFailures()
    if (consecutiveFailuresAlert) alerts.push(consecutiveFailuresAlert)

    const healthy = alerts.length === 0

    Logger.info('Webhook health check completed', {
      method: 'checkWebhookHealth',
      healthy,
      alertCount: alerts.length,
      eventCount: recentEvents.length,
      timeWindow: ALERT_THRESHOLDS.TIME_WINDOW_MINUTES
    })

    return { alerts, healthy, eventCount: recentEvents.length }

  } catch (error) {
    Logger.error('Webhook health check failed', {
      method: 'checkWebhookHealth',
      error: error.message,
      stack: error.stack
    })
    
    return { 
      alerts: [{
        type: 'health_check_error',
        severity: 'critical',
        message: 'Webhook health check system failure',
        error: error.message
      }], 
      healthy: false 
    }
  }
}

/**
 * Check webhook failure rate
 */
async function checkFailureRate(events) {
  const totalEvents = events.length
  const failedEvents = events.filter(event => event.processing_status === 'failed').length
  const failureRate = failedEvents / totalEvents

  if (failureRate > ALERT_THRESHOLDS.FAILURE_RATE) {
    return {
      type: ALERT_TYPES.HIGH_FAILURE_RATE,
      severity: failureRate > 0.25 ? 'critical' : 'warning',
      message: `High webhook failure rate detected: ${(failureRate * 100).toFixed(1)}%`,
      details: {
        failureRate: failureRate,
        failedEvents: failedEvents,
        totalEvents: totalEvents,
        threshold: ALERT_THRESHOLDS.FAILURE_RATE
      },
      timestamp: new Date().toISOString()
    }
  }

  return null
}

/**
 * Check security violations
 */
async function checkSecurityViolations(events) {
  const totalEvents = events.length
  const securityViolations = events.filter(event => 
    !event.ip_whitelisted || !event.signature_verified || !event.timestamp_valid
  ).length
  
  const violationRate = securityViolations / totalEvents

  if (violationRate > ALERT_THRESHOLDS.SECURITY_VIOLATION_RATE) {
    return {
      type: ALERT_TYPES.SECURITY_VIOLATIONS,
      severity: violationRate > 0.15 ? 'critical' : 'warning',
      message: `High security violation rate detected: ${(violationRate * 100).toFixed(1)}%`,
      details: {
        violationRate: violationRate,
        securityViolations: securityViolations,
        totalEvents: totalEvents,
        threshold: ALERT_THRESHOLDS.SECURITY_VIOLATION_RATE
      },
      timestamp: new Date().toISOString()
    }
  }

  return null
}

/**
 * Check processing performance
 */
async function checkProcessingPerformance(events) {
  const eventsWithTiming = events.filter(event => event.processing_time_ms != null)
  
  if (eventsWithTiming.length === 0) return null

  // Calculate 95th percentile processing time
  const sortedTimes = eventsWithTiming
    .map(event => event.processing_time_ms)
    .sort((a, b) => a - b)
  
  const p95Index = Math.floor(sortedTimes.length * 0.95)
  const p95Time = sortedTimes[p95Index]

  if (p95Time > ALERT_THRESHOLDS.PROCESSING_TIME_P95) {
    return {
      type: ALERT_TYPES.SLOW_PROCESSING,
      severity: p95Time > 10000 ? 'critical' : 'warning',
      message: `Slow webhook processing detected: P95 = ${p95Time}ms`,
      details: {
        p95ProcessingTime: p95Time,
        threshold: ALERT_THRESHOLDS.PROCESSING_TIME_P95,
        eventsAnalyzed: eventsWithTiming.length
      },
      timestamp: new Date().toISOString()
    }
  }

  return null
}

/**
 * Check for consecutive failures
 */
async function checkConsecutiveFailures() {
  const recentEvents = await WebhookEvent.find({})
    .sort({ webhook_received_at: -1 })
    .limit(ALERT_THRESHOLDS.CONSECUTIVE_FAILURES + 5)
    .select('processing_status webhook_received_at')
    .lean()

  let consecutiveFailures = 0
  for (const event of recentEvents) {
    if (event.processing_status === 'failed') {
      consecutiveFailures++
    } else {
      break
    }
  }

  if (consecutiveFailures >= ALERT_THRESHOLDS.CONSECUTIVE_FAILURES) {
    return {
      type: ALERT_TYPES.CONSECUTIVE_FAILURES,
      severity: 'critical',
      message: `${consecutiveFailures} consecutive webhook failures detected`,
      details: {
        consecutiveFailures: consecutiveFailures,
        threshold: ALERT_THRESHOLDS.CONSECUTIVE_FAILURES
      },
      timestamp: new Date().toISOString()
    }
  }

  return null
}

/**
 * Send alert notification
 */
export async function sendAlert(alert) {
  try {
    Logger.error('WEBHOOK ALERT', {
      type: alert.type,
      severity: alert.severity,
      message: alert.message,
      details: alert.details,
      timestamp: alert.timestamp
    })

    // In a production environment, you would integrate with:
    // - Email notifications (SendGrid, AWS SES)
    // - Slack/Teams webhooks
    // - PagerDuty/Opsgenie
    // - SMS notifications
    
    // For now, we'll log the alert and could extend with actual notification services
    console.error(`🚨 WEBHOOK ALERT [${alert.severity.toUpperCase()}]: ${alert.message}`)
    
    return { success: true, notificationsSent: ['console', 'logger'] }
    
  } catch (error) {
    Logger.error('Failed to send webhook alert', {
      method: 'sendAlert',
      alert: alert,
      error: error.message
    })
    
    return { success: false, error: error.message }
  }
}

/**
 * Get alert configuration
 */
export function getAlertConfiguration() {
  return {
    thresholds: ALERT_THRESHOLDS,
    alertTypes: ALERT_TYPES,
    enabled: true,
    checkInterval: '5 minutes',
    notificationChannels: ['console', 'logger'] // Extend with actual channels
  }
}

/**
 * Update alert thresholds (for dynamic configuration)
 */
export function updateAlertThresholds(newThresholds) {
  Object.assign(ALERT_THRESHOLDS, newThresholds)
  
  Logger.info('Alert thresholds updated', {
    method: 'updateAlertThresholds',
    newThresholds: ALERT_THRESHOLDS
  })
  
  return ALERT_THRESHOLDS
}

/**
 * Run periodic health checks
 */
export async function runPeriodicHealthCheck() {
  try {
    const healthResult = await checkWebhookHealth()
    
    if (!healthResult.healthy && healthResult.alerts.length > 0) {
      for (const alert of healthResult.alerts) {
        await sendAlert(alert)
      }
    }
    
    return healthResult
    
  } catch (error) {
    Logger.error('Periodic health check failed', {
      method: 'runPeriodicHealthCheck',
      error: error.message
    })
    
    throw error
  }
}
