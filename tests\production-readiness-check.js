/**
 * Production Readiness Check for Payment API
 *
 * This script tests the current state of the production environment
 * and provides clear guidance on what's working and what needs to be fixed.
 */

const fetch = globalThis.fetch || require('node-fetch')

const PRODUCTION_URL = 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net'

async function checkProductionReadiness() {
  console.log('🔍 Production Readiness Check for Payment API')
  console.log('==============================================')
  console.log(`Environment: ${PRODUCTION_URL}`)
  console.log(`Date: ${new Date().toISOString()}`)
  console.log('==============================================\n')

  const results = {
    infrastructure: {},
    api_endpoints: {},
    payment_functionality: {},
    overall_status: 'unknown',
  }

  // 1. Infrastructure Health Check
  console.log('1️⃣ Infrastructure Health Check')
  console.log('--------------------------------')

  try {
    const response = await fetch(`${PRODUCTION_URL}/api/zoho/health`)
    const data = await response.json()

    const isHealthy = data.status === 'healthy'
    const allChecksHealthy = Object.values(data.checks || {}).every((check) => check.status === 'healthy')

    results.infrastructure = {
      status: isHealthy && allChecksHealthy ? 'healthy' : 'unhealthy',
      database: data.checks?.database?.status || 'unknown',
      environment: data.checks?.environment?.status || 'unknown',
      zoho_auth: data.checks?.zoho_auth?.status || 'unknown',
      zoho_api: data.checks?.zoho_api?.status || 'unknown',
      domain_configured: data.configuration?.domain !== 'not configured',
    }

    console.log(`✅ Overall Health: ${data.status}`)
    console.log(`✅ Database: ${data.checks?.database?.status}`)
    console.log(`✅ Environment Variables: ${data.checks?.environment?.status}`)
    console.log(`✅ Zoho Authentication: ${data.checks?.zoho_auth?.status}`)
    console.log(`✅ Zoho API Access: ${data.checks?.zoho_api?.status}`)
    console.log(`✅ Domain Configuration: ${data.configuration?.domain}`)
  } catch (error) {
    console.log(`❌ Infrastructure check failed: ${error.message}`)
    results.infrastructure.status = 'failed'
  }

  console.log('\n2️⃣ API Endpoint Accessibility')
  console.log('------------------------------')

  // Test GET endpoints (these should work)
  const getEndpoints = [
    { name: 'Health Check', path: '/api/zoho/health' },
    { name: 'Payment Creation Info', path: '/api/zoho/payments/create-session' },
    { name: 'Webhook Info', path: '/api/zoho/webhooks/payment' },
  ]

  for (const endpoint of getEndpoints) {
    try {
      const response = await fetch(`${PRODUCTION_URL}${endpoint.path}`)
      const isAccessible = response.status === 200

      results.api_endpoints[endpoint.name] = {
        status: isAccessible ? 'accessible' : 'failed',
        http_status: response.status,
      }

      if (isAccessible) {
        console.log(`✅ ${endpoint.name}: Accessible`)
      } else {
        console.log(`❌ ${endpoint.name}: Failed (${response.status})`)
      }
    } catch (error) {
      console.log(`❌ ${endpoint.name}: Error - ${error.message}`)
      results.api_endpoints[endpoint.name] = { status: 'error', error: error.message }
    }
  }

  console.log('\n3️⃣ Payment Functionality Test')
  console.log('------------------------------')

  // Test POST endpoint (this is currently failing)
  console.log('Testing payment session creation...')

  const testPaymentData = {
    amount: 1,
    currency: 'INR',
    description: 'Production Readiness Test',
    invoice_number: `READY_TEST_${Date.now()}`,
    customer_id: `ready_customer_${Date.now()}`,
    customer_name: 'Readiness Test Customer',
    customer_email: '<EMAIL>',
  }

  try {
    const response = await fetch(`${PRODUCTION_URL}/api/zoho/payments/create-session`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(testPaymentData),
    })

    const responseText = await response.text()

    // Check if we got a proper payment session response
    const isDocumentation = responseText.includes('Payment Session Creation Requirements')
    const isPaymentSession = responseText.includes('payment_session_id') || responseText.includes('payments_session_id')

    if (isPaymentSession) {
      console.log('✅ Payment session creation: Working')
      results.payment_functionality.session_creation = 'working'
    } else if (isDocumentation) {
      console.log('⚠️  Payment session creation: API routing issue detected')
      console.log('   POST request returning GET response (documentation)')
      results.payment_functionality.session_creation = 'routing_issue'
    } else {
      console.log('❌ Payment session creation: Unknown error')
      results.payment_functionality.session_creation = 'error'
    }
  } catch (error) {
    console.log(`❌ Payment session creation: ${error.message}`)
    results.payment_functionality.session_creation = 'failed'
  }

  // Overall Assessment
  console.log('\n🎯 Overall Assessment')
  console.log('=====================')

  const infrastructureHealthy = results.infrastructure.status === 'healthy'
  const endpointsAccessible = Object.values(results.api_endpoints).every((ep) => ep.status === 'accessible')
  const paymentWorking = results.payment_functionality.session_creation === 'working'
  const hasRoutingIssue = results.payment_functionality.session_creation === 'routing_issue'

  if (infrastructureHealthy && endpointsAccessible && paymentWorking) {
    results.overall_status = 'production_ready'
    console.log('🎉 Status: PRODUCTION READY')
    console.log('✨ All systems operational - Payment API is ready for use')
  } else if (infrastructureHealthy && endpointsAccessible && hasRoutingIssue) {
    results.overall_status = 'needs_routing_fix'
    console.log('⚠️  Status: NEEDS ROUTING FIX')
    console.log('🔧 Infrastructure is healthy but API routing needs to be fixed')
    console.log('\n📋 Required Actions:')
    console.log('   1. The staticwebapp.config.json file has been created')
    console.log('   2. Update GitHub workflow to remove api_location conflict')
    console.log('   3. Redeploy the application')
    console.log('   4. Retest payment functionality')
  } else {
    results.overall_status = 'not_ready'
    console.log('❌ Status: NOT PRODUCTION READY')
    console.log('🚨 Critical issues need to be resolved')
  }

  console.log('\n📊 Summary Report')
  console.log('-----------------')
  console.log(`Infrastructure Health: ${results.infrastructure.status}`)
  console.log(`API Endpoints: ${endpointsAccessible ? 'accessible' : 'issues detected'}`)
  console.log(`Payment Functionality: ${results.payment_functionality.session_creation}`)
  console.log(`Overall Status: ${results.overall_status}`)

  if (hasRoutingIssue) {
    console.log('\n🔗 Next Steps:')
    console.log('1. Check the created staticwebapp.config.json file')
    console.log('2. Update .github/workflows/azure-static-web-apps-yellow-sky-08e56d200.yml')
    console.log('3. Remove or comment out the "api_location: \'/api\'" line')
    console.log('4. Commit and push changes to trigger redeployment')
    console.log('5. Run this test again after deployment')
  }

  return results
}

// Run the check
if (typeof require !== 'undefined' && require.main === module) {
  checkProductionReadiness()
    .then((results) => {
      console.log('\n✅ Production readiness check completed')
      process.exit(results.overall_status === 'production_ready' ? 0 : 1)
    })
    .catch((error) => {
      console.error('\n❌ Production readiness check failed:', error.message)
      process.exit(1)
    })
}

module.exports = { checkProductionReadiness }
