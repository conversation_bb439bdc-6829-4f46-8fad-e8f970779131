'use client'

import { useRouter, useSearchParams } from 'next/navigation'
import { Suspense } from 'react'

function PaymentFailedPageContent() {
  const searchParams = useSearchParams()
  const router = useRouter()

  const errorMessage = searchParams.get('error')
  const invoiceNumber = searchParams.get('invoice')

  const handleRetryPayment = () => {
    // Go back to invoices page where user can try payment again
    router.push('/aquapartner/billingAndPayments')
  }

  const handleContactSupport = () => {
    // TODO: Implement support contact functionality
    console.log('Contact support for payment issue')
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <div className="rounded-lg bg-white p-8 shadow-lg">
          {/* Error Icon */}
          <div className="text-center">
            <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
              <svg className="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>

            <h2 className="mt-6 text-2xl font-bold text-gray-900">Payment Failed</h2>
            <p className="mt-2 text-sm text-gray-600">We couldn&apos;t process your payment. Please try again.</p>
          </div>

          {/* Error Details */}
          <div className="mt-8 border-t border-gray-200 pt-6">
            <dl className="space-y-4">
              {invoiceNumber && (
                <div className="flex justify-between">
                  <dt className="text-sm font-medium text-gray-500">Invoice Number</dt>
                  <dd className="text-sm text-gray-900">{invoiceNumber}</dd>
                </div>
              )}

              <div className="flex justify-between">
                <dt className="text-sm font-medium text-gray-500">Attempt Date</dt>
                <dd className="text-sm text-gray-900">{new Date().toLocaleDateString()}</dd>
              </div>

              <div className="flex justify-between">
                <dt className="text-sm font-medium text-gray-500">Status</dt>
                <dd className="text-sm">
                  <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                    Failed
                  </span>
                </dd>
              </div>

              {errorMessage && (
                <div className="col-span-2">
                  <dt className="text-sm font-medium text-gray-500">Error Details</dt>
                  <dd className="mt-1 rounded-md bg-gray-50 p-3 text-sm text-gray-900">{errorMessage}</dd>
                </div>
              )}
            </dl>
          </div>

          {/* Action Buttons */}
          <div className="mt-8 space-y-3">
            <button
              onClick={handleRetryPayment}
              className="flex w-full justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Try Again
            </button>

            <button
              onClick={handleContactSupport}
              className="flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Contact Support
            </button>
          </div>
        </div>

        {/* Troubleshooting Tips */}
        <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fillRule="evenodd"
                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">Common Issues</h3>
              <div className="mt-2 text-sm text-yellow-700">
                <ul className="list-inside list-disc space-y-1">
                  <li>Insufficient funds in your account</li>
                  <li>Card expired or blocked</li>
                  <li>Network connectivity issues</li>
                  <li>Bank server temporarily unavailable</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Support Information */}
        <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">Need Help?</h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>
                  If you continue to experience issues, please contact our support team with your invoice number and
                  error details.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function PaymentFailedPage() {
  return (
    <Suspense
      fallback={
        <div className="flex min-h-screen items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="mx-auto h-12 w-12 animate-spin rounded-full border-b-2 border-red-600"></div>
            <p className="mt-4 text-gray-600">Loading...</p>
          </div>
        </div>
      }
    >
      <PaymentFailedPageContent />
    </Suspense>
  )
}
