import { Schema, model, models } from 'mongoose'

const PaymentTransactionSchema = new Schema(
  {
    // Zoho Payment Session Details (for widget payments)
    payments_session_id: {
      type: String,
      sparse: true, // Only required for payment sessions, not payment links
    },

    // Zoho Payment Link Details (for direct link payments)
    payment_link_id: {
      type: String,
      sparse: true, // Only required for payment links, not payment sessions
    },
    payment_link_url: {
      type: String,
      sparse: true, // Only set for payment links
    },

    payment_id: {
      type: String,
      sparse: true, // Only set when payment is completed
    },

    // Transaction Details
    amount: {
      type: Number,
      required: true,
    },
    currency: {
      type: String,
      required: true,
      default: 'INR',
    },
    description: {
      type: String,
      required: true,
    },
    invoice_number: {
      type: String,
      required: true,
    },

    // Customer Information
    customer_id: {
      type: String,
      required: true,
    },
    customer_name: {
      type: String,
    },
    customer_email: {
      type: String,
    },
    customer_phone: {
      type: String,
    },

    // Payment Status
    status: {
      type: String,
      enum: ['created', 'pending', 'succeeded', 'failed', 'cancelled', 'expired'],
      default: 'created',
    },
    payment_method: {
      type: String,
    },

    // Metadata
    meta_data: [
      {
        key: String,
        value: String,
      },
    ],

    // URLs
    redirect_url: {
      type: String,
    },
    webhook_url: {
      type: String,
    },

    // Timestamps
    session_created_time: {
      type: Date,
    },
    payment_completed_time: {
      type: Date,
    },
    session_expires_at: {
      type: Date,
    },

    // Webhook Events
    webhook_events: [
      {
        event_type: String,
        event_data: Schema.Types.Mixed,
        received_at: {
          type: Date,
          default: Date.now,
        },
      },
    ],

    // Error Information
    error_code: {
      type: String,
    },
    error_message: {
      type: String,
    },

    // Reference Information
    reference_id: {
      type: String,
    },
    order_id: {
      type: String,
    },

    // Refund Information
    refunds: [
      {
        refund_id: String,
        amount: Number,
        status: String,
        created_at: Date,
        reason: String,
      },
    ],

    // Internal tracking
    created_by: {
      type: String,
    },
    updated_by: {
      type: String,
    },
  },
  {
    collection: 'PaymentTransactions',
    timestamps: true,
  }
)

// Custom validation to ensure either payments_session_id or payment_link_id is provided
PaymentTransactionSchema.pre('validate', function (next) {
  if (!this.payments_session_id && !this.payment_link_id) {
    this.invalidate('payments_session_id', 'Either payments_session_id or payment_link_id must be provided')
    this.invalidate('payment_link_id', 'Either payments_session_id or payment_link_id must be provided')
  }
  next()
})

// Indexes for efficient queries
// Note: Using background: true to prevent blocking during index creation
PaymentTransactionSchema.index(
  { payments_session_id: 1 },
  {
    unique: true,
    sparse: true,
    background: true,
    name: 'payments_session_id_unique_sparse',
  }
)
PaymentTransactionSchema.index(
  { payment_link_id: 1 },
  {
    unique: true,
    sparse: true,
    background: true,
    name: 'payment_link_id_unique_sparse',
  }
)
PaymentTransactionSchema.index(
  { payment_id: 1 },
  {
    background: true,
    name: 'payment_id_index',
  }
)
PaymentTransactionSchema.index({ customer_id: 1 }, { background: true })
PaymentTransactionSchema.index({ invoice_number: 1 }, { background: true })
PaymentTransactionSchema.index({ status: 1 }, { background: true })
PaymentTransactionSchema.index({ createdAt: -1 }, { background: true })
PaymentTransactionSchema.index({ session_expires_at: 1 }, { background: true })

// Method to check if session is expired
PaymentTransactionSchema.methods.isSessionExpired = function () {
  return new Date() >= this.session_expires_at
}

// Method to add webhook event
PaymentTransactionSchema.methods.addWebhookEvent = function (eventType, eventData) {
  this.webhook_events.push({
    event_type: eventType,
    event_data: eventData,
    received_at: new Date(),
  })
  return this.save()
}

// Method to update payment status
PaymentTransactionSchema.methods.updateStatus = function (newStatus, additionalData = {}) {
  this.status = newStatus
  this.updated_at = new Date()

  if (additionalData.payment_id) {
    this.payment_id = additionalData.payment_id
  }

  if (additionalData.payment_method) {
    this.payment_method = additionalData.payment_method
  }

  if (newStatus === 'succeeded') {
    this.payment_completed_time = new Date()
  }

  if (additionalData.error_code) {
    this.error_code = additionalData.error_code
    this.error_message = additionalData.error_message
  }

  return this.save()
}

const PaymentTransaction = models.PaymentTransaction || model('PaymentTransaction', PaymentTransactionSchema)

export default PaymentTransaction
