# Flutter WebView Payment Integration Guide (Official Zoho SDK)

This guide explains how to integrate the **official Zoho Payment SDK** with your Flutter app using WebView.

## ⚠️ Important Update

This implementation now uses the **official Zoho Payments JavaScript SDK** with:

- ✅ Official `ZPayments` class
- ✅ Promise-based `requestPaymentMethod()`
- ✅ Proper API key authentication
- ✅ Official script from `https://static.zohocdn.com/zpay/zpay-js/v1/zpayments.js`

## Architecture Overview

```
Flutter App → API Call → Next.js Backend → Zoho Payment Session → Official SDK WebView → Payment Processing → Flutter Callback
```

## 1. Flutter Dependencies

Add these dependencies to your `pubspec.yaml`:

```yaml
dependencies:
  flutter_inappwebview: ^6.0.0
  http: ^1.1.0
  # Add other dependencies as needed
```

## 2. Payment Initiation in Flutter

### API Call to Initiate Payment

```dart
import 'dart:convert';
import 'package:http/http.dart' as http;

class PaymentService {
  static const String baseUrl = 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net'; // e.g., 'https://your-app.azurewebsites.net'

  static Future<PaymentSession?> initiatePayment({
    required double amount,
    required String invoiceNo,
    required String customerId,
    String? customerName,
    String? customerEmail,
    String? customerPhone,
    String? description,
    String currency = 'INR',
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/flutter/payment/initiate'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'amount': amount,
          'invoiceNo': invoiceNo,
          'customerId': customerId,
          'customerName': customerName,
          'customerEmail': customerEmail,
          'customerPhone': customerPhone,
          'description': description,
          'currency': currency,
          'businessName': 'Your Business Name', // Add business name
          'referenceNumber': 'REF-${DateTime.now().millisecondsSinceEpoch}', // Add reference
          'metadata': metadata ?? {},
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success']) {
          return PaymentSession.fromJson(data);
        } else {
          throw Exception(data['message'] ?? 'Payment initiation failed');
        }
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      print('Payment initiation error: $e');
      rethrow;
    }
  }

  static Future<PaymentStatus?> checkPaymentStatus(String sessionId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/flutter/payment/status/$sessionId'),
        headers: {
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success']) {
          return PaymentStatus.fromJson(data);
        }
      }
      return null;
    } catch (e) {
      print('Payment status check error: $e');
      return null;
    }
  }
}

class PaymentSession {
  final String sessionId;
  final double amount;
  final String currency;
  final String description;
  final String webviewUrl;
  final String transactionId;
  final int expiresInMinutes;

  PaymentSession({
    required this.sessionId,
    required this.amount,
    required this.currency,
    required this.description,
    required this.webviewUrl,
    required this.transactionId,
    required this.expiresInMinutes,
  });

  factory PaymentSession.fromJson(Map<String, dynamic> json) {
    return PaymentSession(
      sessionId: json['payment_session']['id'],
      amount: json['payment_session']['amount'].toDouble(),
      currency: json['payment_session']['currency'],
      description: json['payment_session']['description'],
      webviewUrl: json['webview_url'],
      transactionId: json['transaction_id'],
      expiresInMinutes: json['expires_in_minutes'],
    );
  }
}

class PaymentStatus {
  final String status;
  final bool canRetry;
  final bool shouldCloseWebview;
  final String nextAction;
  final Map<String, dynamic>? paymentDetails;

  PaymentStatus({
    required this.status,
    required this.canRetry,
    required this.shouldCloseWebview,
    required this.nextAction,
    this.paymentDetails,
  });

  factory PaymentStatus.fromJson(Map<String, dynamic> json) {
    final flutterStatus = json['flutter_status'];
    return PaymentStatus(
      status: json['payment_session']['status'],
      canRetry: flutterStatus['can_retry'],
      shouldCloseWebview: flutterStatus['should_close_webview'],
      nextAction: flutterStatus['next_action'],
      paymentDetails: json['payment_details'],
    );
  }
}
```

## 3. WebView Payment Widget

```dart
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

class PaymentWebView extends StatefulWidget {
  final String webviewUrl;
  final Function(PaymentResult) onPaymentComplete;

  const PaymentWebView({
    Key? key,
    required this.webviewUrl,
    required this.onPaymentComplete,
  }) : super(key: key);

  @override
  State<PaymentWebView> createState() => _PaymentWebViewState();
}

class _PaymentWebViewState extends State<PaymentWebView> {
  InAppWebViewController? webViewController;
  bool isLoading = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment'),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () {
            widget.onPaymentComplete(PaymentResult(
              status: 'cancelled',
              message: 'Payment cancelled by user',
            ));
          },
        ),
      ),
      body: Stack(
        children: [
          InAppWebView(
            initialUrlRequest: URLRequest(url: WebUri(widget.webviewUrl)),
            onWebViewCreated: (controller) {
              webViewController = controller;

              // Add JavaScript handler for payment callbacks
              controller.addJavaScriptHandler(
                handlerName: 'paymentCallback',
                callback: (args) {
                  if (args.isNotEmpty) {
                    final result = args[0] as Map<String, dynamic>;
                    _handlePaymentCallback(result);
                  }
                },
              );
            },
            onLoadStart: (controller, url) {
              setState(() {
                isLoading = true;
              });
            },
            onLoadStop: (controller, url) {
              setState(() {
                isLoading = false;
              });
            },
            onReceivedError: (controller, request, error) {
              widget.onPaymentComplete(PaymentResult(
                status: 'error',
                message: 'WebView error: ${error.description}',
              ));
            },
            initialSettings: InAppWebViewSettings(
              javaScriptEnabled: true,
              domStorageEnabled: true,
              allowsInlineMediaPlayback: true,
              mediaPlaybackRequiresUserGesture: false,
            ),
          ),
          if (isLoading)
            const Center(
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }

  void _handlePaymentCallback(Map<String, dynamic> result) {
    final status = result['status'] as String;
    final data = result['data'] as Map<String, dynamic>?;

    print('Payment callback received: $status');
    print('Payment data: $data');

    PaymentResult paymentResult;

    switch (status) {
      case 'success':
        paymentResult = PaymentResult(
          status: 'success',
          message: 'Payment completed successfully',
          paymentId: data?['payment_id'],
          transactionId: data?['transaction_id'],
          amount: data?['amount'],
          currency: data?['currency'],
        );
        break;
      case 'failure':
        paymentResult = PaymentResult(
          status: 'failure',
          message: data?['error_message'] ?? 'Payment failed',
          errorCode: data?['error_code'],
        );
        break;
      case 'cancelled':
        paymentResult = PaymentResult(
          status: 'cancelled',
          message: 'Payment was cancelled',
        );
        break;
      case 'error':
        paymentResult = PaymentResult(
          status: 'error',
          message: data?['message'] ?? 'An error occurred',
          errorCode: data?['code'],
        );
        break;
      default:
        paymentResult = PaymentResult(
          status: 'unknown',
          message: 'Unknown payment status: $status',
        );
    }

    widget.onPaymentComplete(paymentResult);
  }
}

class PaymentResult {
  final String status;
  final String message;
  final String? paymentId;
  final String? transactionId;
  final double? amount;
  final String? currency;
  final String? errorCode;

  PaymentResult({
    required this.status,
    required this.message,
    this.paymentId,
    this.transactionId,
    this.amount,
    this.currency,
    this.errorCode,
  });

  bool get isSuccess => status == 'success';
  bool get isFailure => status == 'failure';
  bool get isCancelled => status == 'cancelled';
  bool get isError => status == 'error';
}
```

## 4. Usage Example

```dart
class PaymentScreen extends StatefulWidget {
  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  bool isInitiating = false;

  Future<void> _startPayment() async {
    setState(() {
      isInitiating = true;
    });

    try {
      final paymentSession = await PaymentService.initiatePayment(
        amount: 100.0,
        invoiceNo: 'INV-001',
        customerId: 'CUST-123',
        customerName: 'John Doe',
        customerEmail: '<EMAIL>',
        description: 'Test Payment',
      );

      if (paymentSession != null) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PaymentWebView(
              webviewUrl: paymentSession.webviewUrl,
              onPaymentComplete: _handlePaymentResult,
            ),
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to initiate payment: $e')),
      );
    } finally {
      setState(() {
        isInitiating = false;
      });
    }
  }

  void _handlePaymentResult(PaymentResult result) {
    Navigator.pop(context); // Close WebView

    String message;
    Color backgroundColor;

    if (result.isSuccess) {
      message = 'Payment successful! Payment ID: ${result.paymentId}';
      backgroundColor = Colors.green;
    } else if (result.isFailure) {
      message = 'Payment failed: ${result.message}';
      backgroundColor = Colors.red;
    } else if (result.isCancelled) {
      message = 'Payment cancelled';
      backgroundColor = Colors.orange;
    } else {
      message = 'Payment error: ${result.message}';
      backgroundColor = Colors.red;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor,
        duration: const Duration(seconds: 5),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Payment')),
      body: Center(
        child: ElevatedButton(
          onPressed: isInitiating ? null : _startPayment,
          child: isInitiating
              ? const CircularProgressIndicator()
              : const Text('Start Payment'),
        ),
      ),
    );
  }
}
```

## 5. Next.js Configuration Updates

Update your `next.config.js` to remove the Express route conflict:

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'standalone',

  experimental: {
    serverComponentsExternalPackages: ['mongodb'],
  },

  // Remove any Express server configurations
  // Add headers for WebView compatibility
  async headers() {
    return [
      {
        source: '/api/payment-page',
        headers: [
          { key: 'X-Frame-Options', value: 'SAMEORIGIN' },
          { key: 'Content-Security-Policy', value: "frame-ancestors 'self'" },
        ],
      },
      {
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET, POST, PUT, DELETE, OPTIONS' },
          { key: 'Access-Control-Allow-Headers', value: 'Content-Type, Authorization' },
        ],
      },
    ]
  },
}

module.exports = nextConfig
```

## 6. Testing

1. **Test the API endpoints** using Postman or similar tools
2. **Test the WebView URL** in a browser first
3. **Test the Flutter integration** with the WebView
4. **Test payment callbacks** with different scenarios (success, failure, cancellation)

## 7. Production Considerations

1. **Error Handling**: Implement comprehensive error handling
2. **Security**: Validate all payment data server-side
3. **Logging**: Add proper logging for debugging
4. **Timeouts**: Handle payment session timeouts
5. **Network Issues**: Handle network connectivity issues
6. **User Experience**: Add loading states and clear messaging

## 8. Official SDK Migration Notes

### Key Changes from Previous Implementation

1. **JavaScript SDK Class**: Changed from `ZPay` to `ZPayments`
2. **Payment Method**: Changed from `zpay.renderPaymentForm()` to `instance.requestPaymentMethod()`
3. **Response Handling**: Changed from callbacks to Promise-based approach
4. **Configuration**: Now requires API key and account ID

### Updated WebView Response Format

The official SDK returns responses in this format:

```javascript
// Success Response
{
  "payment_id": "****************",
  "message": "Payment successful!"
}

// Error Response
{
  "code": "widget_closed", // or other error codes
  "message": "Payment failed!"
}
```

### Environment Variables Required

Ensure these are set in your environment:

```bash
ZOHO_PAY_ACCOUNT_ID=your_account_id
ZOHO_PAY_API_KEY=your_api_key
ZOHO_PAYMENT_SESSION_URL=https://payments.zoho.in/api/v1/paymentsessions
```

### Testing the Official SDK

1. **Verify API Key**: Test with a small amount first
2. **Check Account ID**: Ensure it matches your Zoho Payments account
3. **Test Error Scenarios**: Verify error handling works correctly
4. **Validate Callbacks**: Ensure Flutter receives all callback types

This integration now uses the **official Zoho Payments SDK** and maintains your existing service architecture while providing a clean Flutter WebView interface.
