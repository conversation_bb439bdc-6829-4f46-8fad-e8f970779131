import connectedDB from '@/app/config/database'
import { Logger } from '@/app/lib/monitoring'
import { withRateLimit } from '@/app/lib/rateLimiting'
import WebhookEvent from '@/app/models/WebhookEvent'
import { NextResponse } from 'next/server'

/**
 * GET /api/zoho/webhooks/monitor
 * Enhanced webhook monitoring endpoint with filtering and real-time analytics
 */
async function handleMonitorGET(request) {
  try {
    await connectedDB()

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page')) || 1
    const limit = Math.min(parseInt(searchParams.get('limit')) || 50, 100)
    const skip = (page - 1) * limit

    // Build filter query
    const filter = {}

    // Status filter
    const status = searchParams.get('status')
    if (status && status !== 'all') {
      filter.processing_status = status
    }

    // Event type filter
    const eventType = searchParams.get('eventType') || searchParams.get('event_type')
    if (eventType && eventType !== 'all') {
      filter.event_type = eventType
    }

    // Date range filter
    const dateRange = searchParams.get('dateRange') || searchParams.get('date_range')
    if (dateRange) {
      const now = new Date()
      let startDate

      switch (dateRange) {
        case '1h':
          startDate = new Date(now.getTime() - 60 * 60 * 1000)
          break
        case '24h':
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000)
          break
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        default:
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000) // Default to 24h
      }

      filter.webhook_received_at = { $gte: startDate }
    }

    // Search filter
    const search = searchParams.get('searchTerm') || searchParams.get('search')
    if (search) {
      filter.$or = [
        { event_id: { $regex: search, $options: 'i' } },
        { event_type: { $regex: search, $options: 'i' } },
        { payment_session_id: { $regex: search, $options: 'i' } },
        { payment_link_id: { $regex: search, $options: 'i' } },
        { payment_id: { $regex: search, $options: 'i' } },
      ]
    }

    // Get events with pagination and enhanced fields for monitoring
    const events = await WebhookEvent.find(filter)
      .sort({ webhook_received_at: -1 })
      .skip(skip)
      .limit(limit)
      .select({
        event_id: 1,
        event_type: 1,
        payment_session_id: 1,
        payment_link_id: 1,
        payment_id: 1,
        processing_status: 1,
        webhook_received_at: 1,
        processed_at: 1,
        processing_time_ms: 1,
        retry_count: 1,
        source_ip: 1,
        ip_whitelisted: 1,
        signature_verified: 1,
        timestamp_valid: 1,
        error_message: 1,
        security_flags: 1,
        webhook_data: 1
      })
      .lean()

    // Get total count for pagination
    const total = await WebhookEvent.countDocuments(filter)

    // Get comprehensive statistics
    const stats = await WebhookEvent.aggregate([
      { $match: filter },
      {
        $group: {
          _id: '$processing_status',
          count: { $sum: 1 },
          avgProcessingTime: { $avg: '$processing_time_ms' },
          totalRetries: { $sum: '$retry_count' }
        },
      },
    ])

    const statsMap = stats.reduce((acc, stat) => {
      acc[stat._id] = {
        count: stat.count,
        avgProcessingTime: Math.round(stat.avgProcessingTime || 0),
        totalRetries: stat.totalRetries || 0
      }
      return acc
    }, {})

    // Get security statistics
    const securityStats = await WebhookEvent.aggregate([
      { $match: filter },
      {
        $group: {
          _id: null,
          totalEvents: { $sum: 1 },
          ipWhitelistPassed: { $sum: { $cond: ['$ip_whitelisted', 1, 0] } },
          signatureVerified: { $sum: { $cond: ['$signature_verified', 1, 0] } },
          timestampValid: { $sum: { $cond: ['$timestamp_valid', 1, 0] } },
          securityFailures: { 
            $sum: { 
              $cond: [
                { 
                  $or: [
                    { $eq: ['$ip_whitelisted', false] },
                    { $eq: ['$signature_verified', false] },
                    { $eq: ['$timestamp_valid', false] }
                  ]
                }, 
                1, 
                0
              ]
            }
          }
        }
      }
    ])

    // Get performance metrics
    const performanceStats = await WebhookEvent.aggregate([
      { $match: { ...filter, processing_time_ms: { $exists: true, $ne: null } } },
      {
        $group: {
          _id: null,
          avgProcessingTime: { $avg: '$processing_time_ms' },
          minProcessingTime: { $min: '$processing_time_ms' },
          maxProcessingTime: { $max: '$processing_time_ms' },
          p95ProcessingTime: { $percentile: { input: '$processing_time_ms', p: [0.95], method: 'approximate' } }
        }
      }
    ])

    // Get hourly event distribution for the last 24 hours
    const hourlyStats = await WebhookEvent.aggregate([
      { 
        $match: { 
          webhook_received_at: { 
            $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) 
          } 
        } 
      },
      {
        $group: {
          _id: { 
            hour: { $hour: '$webhook_received_at' },
            status: '$processing_status'
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.hour': 1 } }
    ])

    Logger.info('Webhook monitoring data requested', {
      method: 'handleMonitorGET',
      filters: { status, eventType, dateRange, search },
      resultCount: events.length,
      totalEvents: total
    })

    return NextResponse.json({
      success: true,
      events,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
      stats: {
        total,
        processing: statsMap.processing?.count || 0,
        completed: statsMap.completed?.count || 0,
        failed: statsMap.failed?.count || 0,
        duplicate: statsMap.duplicate?.count || 0,
        retry_pending: statsMap.retry_pending?.count || 0,
        avgProcessingTime: Object.values(statsMap).reduce((sum, stat) => sum + (stat.avgProcessingTime || 0), 0) / Object.keys(statsMap).length || 0,
        totalRetries: Object.values(statsMap).reduce((sum, stat) => sum + (stat.totalRetries || 0), 0)
      },
      security: securityStats[0] || {
        totalEvents: 0,
        ipWhitelistPassed: 0,
        signatureVerified: 0,
        timestampValid: 0,
        securityFailures: 0
      },
      performance: performanceStats[0] || {
        avgProcessingTime: 0,
        minProcessingTime: 0,
        maxProcessingTime: 0,
        p95ProcessingTime: [0]
      },
      hourlyDistribution: hourlyStats,
      filters: {
        status,
        eventType,
        dateRange,
        search,
      },
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    Logger.error('Webhook monitoring GET failed', {
      method: 'handleMonitorGET',
      error: error.message,
      stack: error.stack,
    })

    return NextResponse.json({
      success: false,
      error: 'Failed to fetch webhook monitoring data',
      message: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * POST /api/zoho/webhooks/monitor
 * Export webhook data or trigger monitoring actions
 */
async function handleMonitorPOST(request) {
  try {
    await connectedDB()

    const body = await request.json()
    const { action, filters = {} } = body

    Logger.info('Webhook monitoring action requested', {
      method: 'handleMonitorPOST',
      action,
      filters
    })

    switch (action) {
      case 'export':
        // Build filter query for export
        const exportFilter = {}
        
        if (filters.status && filters.status !== 'all') {
          exportFilter.processing_status = filters.status
        }
        
        if (filters.eventType && filters.eventType !== 'all') {
          exportFilter.event_type = filters.eventType
        }
        
        if (filters.dateRange) {
          const now = new Date()
          let startDate
          
          switch (filters.dateRange) {
            case '1h':
              startDate = new Date(now.getTime() - 60 * 60 * 1000)
              break
            case '24h':
              startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000)
              break
            case '7d':
              startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
              break
            case '30d':
              startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
              break
          }
          
          if (startDate) {
            exportFilter.webhook_received_at = { $gte: startDate }
          }
        }

        const exportData = await WebhookEvent.find(exportFilter)
          .sort({ webhook_received_at: -1 })
          .limit(1000) // Limit export to 1000 records
          .select({
            event_id: 1,
            event_type: 1,
            payment_session_id: 1,
            payment_link_id: 1,
            payment_id: 1,
            processing_status: 1,
            webhook_received_at: 1,
            processed_at: 1,
            processing_time_ms: 1,
            retry_count: 1,
            source_ip: 1,
            ip_whitelisted: 1,
            signature_verified: 1,
            timestamp_valid: 1,
            error_message: 1
          })
          .lean()

        return NextResponse.json({
          success: true,
          action: 'export',
          data: exportData,
          count: exportData.length,
          timestamp: new Date().toISOString()
        })

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action',
          validActions: ['export'],
          timestamp: new Date().toISOString()
        }, { status: 400 })
    }
  } catch (error) {
    Logger.error('Webhook monitoring POST failed', {
      method: 'handleMonitorPOST',
      error: error.message,
      stack: error.stack,
    })

    return NextResponse.json({
      success: false,
      error: 'Failed to process monitoring action',
      message: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

// Apply rate limiting
export const GET = withRateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  maxRequests: 60, // Allow frequent monitoring requests
  message: 'Too many monitoring requests. Please wait before trying again.',
})(handleMonitorGET)

export const POST = withRateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  maxRequests: 10, // Limit export/action requests
  message: 'Too many monitoring actions. Please wait before trying again.',
})(handleMonitorPOST)
