import { NextResponse } from 'next/server'
import connectedDB from '@/app/config/database'
import { Logger } from '@/app/lib/monitoring'
import { checkWebhookHealth } from '@/app/lib/webhookAlerting'
import WebhookEvent from '@/app/models/WebhookEvent'

/**
 * GET /api/health/webhook
 * Webhook health check endpoint for production monitoring
 */
export async function GET(request) {
  const startTime = Date.now()
  const healthCheck = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    checks: {},
    metrics: {},
    environment: process.env.NODE_ENV || 'development'
  }

  try {
    // 1. Database connectivity check
    try {
      await connectedDB()
      const dbTest = await WebhookEvent.findOne().limit(1)
      healthCheck.checks.database = {
        status: 'healthy',
        message: 'Database connection successful',
        responseTime: Date.now() - startTime
      }
    } catch (dbError) {
      healthCheck.checks.database = {
        status: 'unhealthy',
        message: `Database connection failed: ${dbError.message}`,
        error: dbError.message
      }
      healthCheck.status = 'unhealthy'
    }

    // 2. Webhook processing health
    try {
      const webhookHealth = await checkWebhookHealth()
      healthCheck.checks.webhookProcessing = {
        status: webhookHealth.healthy ? 'healthy' : 'unhealthy',
        message: webhookHealth.healthy ? 'Webhook processing is healthy' : 'Webhook processing issues detected',
        alerts: webhookHealth.alerts || [],
        eventCount: webhookHealth.eventCount || 0
      }

      if (!webhookHealth.healthy) {
        healthCheck.status = 'degraded'
      }
    } catch (webhookError) {
      healthCheck.checks.webhookProcessing = {
        status: 'unhealthy',
        message: `Webhook health check failed: ${webhookError.message}`,
        error: webhookError.message
      }
      healthCheck.status = 'unhealthy'
    }

    // 3. Environment variables check
    const requiredEnvVars = [
      'MONGODB_URI',
      'ZOHO_WEBHOOK_SECRET',
      'NEXT_PUBLIC_DOMAIN',
      'ZOHO_PAY_ACCOUNT_ID'
    ]

    const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar])
    healthCheck.checks.environment = {
      status: missingEnvVars.length === 0 ? 'healthy' : 'unhealthy',
      message: missingEnvVars.length === 0 ? 'All required environment variables present' : `Missing environment variables: ${missingEnvVars.join(', ')}`,
      missingVariables: missingEnvVars
    }

    if (missingEnvVars.length > 0) {
      healthCheck.status = 'unhealthy'
    }

    // 4. Recent webhook metrics
    try {
      const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000)
      const recentStats = await WebhookEvent.aggregate([
        { $match: { webhook_received_at: { $gte: last24Hours } } },
        {
          $group: {
            _id: '$processing_status',
            count: { $sum: 1 },
            avgProcessingTime: { $avg: '$processing_time_ms' }
          }
        }
      ])

      const statsMap = recentStats.reduce((acc, stat) => {
        acc[stat._id] = {
          count: stat.count,
          avgProcessingTime: Math.round(stat.avgProcessingTime || 0)
        }
        return acc
      }, {})

      healthCheck.metrics.last24Hours = {
        total: recentStats.reduce((sum, stat) => sum + stat.count, 0),
        completed: statsMap.completed?.count || 0,
        failed: statsMap.failed?.count || 0,
        processing: statsMap.processing?.count || 0,
        duplicate: statsMap.duplicate?.count || 0,
        avgProcessingTime: Object.values(statsMap).reduce((sum, stat) => sum + (stat.avgProcessingTime || 0), 0) / Object.keys(statsMap).length || 0
      }

      // Calculate failure rate
      const total = healthCheck.metrics.last24Hours.total
      const failed = healthCheck.metrics.last24Hours.failed
      const failureRate = total > 0 ? (failed / total) * 100 : 0

      healthCheck.metrics.last24Hours.failureRate = Math.round(failureRate * 100) / 100

      // Health degradation based on failure rate
      if (failureRate > 10 && healthCheck.status === 'healthy') {
        healthCheck.status = 'degraded'
      }

    } catch (metricsError) {
      healthCheck.checks.metrics = {
        status: 'unhealthy',
        message: `Metrics collection failed: ${metricsError.message}`,
        error: metricsError.message
      }
    }

    // 5. System information
    healthCheck.system = {
      uptime: process.uptime(),
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        external: Math.round(process.memoryUsage().external / 1024 / 1024)
      },
      nodeVersion: process.version,
      platform: process.platform
    }

    // 6. Response time
    healthCheck.responseTime = Date.now() - startTime

    // Log health check
    Logger.info('Webhook health check completed', {
      method: 'GET /api/health/webhook',
      status: healthCheck.status,
      responseTime: healthCheck.responseTime,
      checks: Object.keys(healthCheck.checks).map(key => ({
        name: key,
        status: healthCheck.checks[key].status
      }))
    })

    // Return appropriate HTTP status
    let httpStatus = 200
    if (healthCheck.status === 'degraded') {
      httpStatus = 200 // Still operational but with issues
    } else if (healthCheck.status === 'unhealthy') {
      httpStatus = 503 // Service unavailable
    }

    return NextResponse.json(healthCheck, { status: httpStatus })

  } catch (error) {
    Logger.error('Health check failed', {
      method: 'GET /api/health/webhook',
      error: error.message,
      stack: error.stack
    })

    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message,
      responseTime: Date.now() - startTime
    }, { status: 503 })
  }
}

/**
 * HEAD /api/health/webhook
 * Lightweight health check for load balancers
 */
export async function HEAD(request) {
  try {
    // Quick database connectivity check
    await connectedDB()
    
    // Return 200 if basic connectivity works
    return new Response(null, { status: 200 })
    
  } catch (error) {
    // Return 503 if basic connectivity fails
    return new Response(null, { status: 503 })
  }
}
