import zohoPaymentService from '@/app/lib/zohoPaymentService'

/**
 * POST /api/zoho/payments/create-payment
 * Create payment using automatic method selection (payment session or payment link)
 * with fallback logic and environment detection
 */
export async function POST(request) {
  try {
    const body = await request.json()
    const userAgent = request.headers.get('user-agent') || ''
    const forcePaymentMethod = 'links'

    console.log('🎯 CREATE PAYMENT: Request received')
    console.log('🎯 CREATE PAYMENT: User Agent:', userAgent)
    console.log('🎯 CREATE PAYMENT: Force Payment Method:', forcePaymentMethod)
    console.log('🎯 CREATE PAYMENT: Body:', body)

    const {
      amount,
      currency = 'INR',
      description,
      invoice_number,
      customer_id,
      customer_name,
      customer_email,
      customer_phone,
      redirect_url,
      reference_id,
      expires_at,
      notify_user,
      meta_data,
    } = body

    // Validate required fields
    if (!amount || !description) {
      return new Response(
        JSON.stringify({
          error: 'Missing required fields',
          message: 'amount and description are required',
        }),
        { status: 400 }
      )
    }

    // Handle missing customer email by generating a fallback
    const finalCustomerEmail = customer_email || `${customer_id}@aquapartner.com`

    console.log('📧 EMAIL HANDLING:', {
      original: customer_email,
      final: finalCustomerEmail,
      customer_id,
    })

    // Validate amount
    const numericAmount = parseFloat(amount)
    if (isNaN(numericAmount) || numericAmount <= 0) {
      return new Response(
        JSON.stringify({
          error: 'Invalid amount',
          message: 'Amount must be a positive number',
        }),
        { status: 400 }
      )
    }

    // Validate currency
    if (currency !== 'INR') {
      return new Response(
        JSON.stringify({
          error: 'Invalid currency',
          message: 'Only INR currency is supported',
        }),
        { status: 400 }
      )
    }

    const paymentData = {
      amount: numericAmount,
      currency,
      description,
      invoice_number,
      customer_id,
      customer_name,
      customer_email: finalCustomerEmail,
      customer_phone,
      redirect_url,
      reference_id,
      expires_at,
      notify_user,
      meta_data,
    }

    // Use the new createPayment method with automatic selection and fallback
    // Check if payment method is forced via header
    let result
    if (forcePaymentMethod === 'links') {
      console.log('🔗 CREATE PAYMENT: Forcing payment links method')
      result = await zohoPaymentService.createPaymentLink(paymentData)
    } else if (forcePaymentMethod === 'widget') {
      console.log('🎯 CREATE PAYMENT: Forcing payment session method')
      result = await zohoPaymentService.createPaymentSession(paymentData)
    } else {
      result = await zohoPaymentService.createPayment(paymentData, userAgent)
    }

    // Determine response format based on payment method used
    if (result.payment_link) {
      // Payment link response
      return new Response(
        JSON.stringify({
          success: true,
          message: 'Payment link created successfully',
          payment_method: 'payment_link',
          fallback_used: result.fallback_used || false,
          original_method: result.original_method,
          final_method: result.final_method,
          data: {
            payment_link_id: result.payment_link.payment_link_id,
            payment_link_url: result.payment_link.url,
            amount: result.payment_link.amount,
            currency: result.payment_link.currency,
            description: result.payment_link.description,
            reference_id: result.payment_link.reference_id,
            status: result.payment_link.status,
            expires_at: result.payment_link.expires_at,
            created_time: result.payment_link.created_time,
            transaction_id: result.transaction_id,
          },
          payment_link: result.payment_link,
        }),
        { status: 201 }
      )
    } else {
      // Payment session response (existing format for backward compatibility)
      return new Response(
        JSON.stringify({
          success: true,
          message: 'Payment session created successfully',
          payment_method: 'payment_session',
          fallback_used: result.fallback_used || false,
          original_method: result.original_method,
          final_method: result.final_method,
          data: {
            payment_session_id: result.payment_session.payments_session_id,
            amount: result.payment_session.amount,
            currency: result.payment_session.currency,
            description: result.payment_session.description,
            invoice_number: result.payment_session.invoice_number,
            created_time: result.payment_session.created_time,
            transaction_id: result.transaction_id,
            expires_in: '15 minutes',
          },
          payment_session: result.payment_session,
        }),
        { status: 201 }
      )
    }
  } catch (error) {
    console.error('❌ CREATE PAYMENT ERROR:', error.message)

    // Handle specific errors
    if (error.message.includes('Zoho API Error') || error.message.includes('Zoho Payment Links API Error')) {
      return new Response(
        JSON.stringify({
          error: 'Zoho Payment API Error',
          message: error.message,
          details: 'Payment creation failed due to Zoho API error',
        }),
        { status: 400 }
      )
    }

    if (error.message.includes('token')) {
      return new Response(
        JSON.stringify({
          error: 'Authentication Error',
          message: error.message,
          details: 'Please check your Zoho Payment configuration',
        }),
        { status: 401 }
      )
    }

    if (error.message.includes('Both payment methods failed')) {
      return new Response(
        JSON.stringify({
          error: 'Payment Creation Failed',
          message: error.message,
          details: 'Both payment session and payment link creation failed',
        }),
        { status: 500 }
      )
    }

    return new Response(
      JSON.stringify({
        error: 'Payment creation failed',
        message: error.message,
        details: 'An unexpected error occurred while creating payment',
      }),
      { status: 500 }
    )
  }
}
