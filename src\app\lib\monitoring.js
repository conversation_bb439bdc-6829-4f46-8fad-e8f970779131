/**
 * Monitoring and Metrics Collection Utilities
 *
 * Production-grade monitoring for Zoho Payment API
 */

import connectedDB from '@/app/config/database'
import PaymentTransaction from '@/app/models/PaymentTransaction'

/**
 * Structured logging utility
 */
export class Logger {
  static levels = {
    ERROR: 0,
    WARN: 1,
    INFO: 2,
    DEBUG: 3,
  }

  static log(level, message, data = {}) {
    const timestamp = new Date().toISOString()
    const logEntry = {
      timestamp,
      level,
      message,
      service: 'zoho-payment-api',
      environment: process.env.NODE_ENV || 'development',
      ...data,
    }

    // Console output with appropriate method
    switch (level) {
      case 'ERROR':
        console.error(`🔴 [${timestamp}] ERROR: ${message}`, data)
        break
      case 'WARN':
        console.warn(`🟡 [${timestamp}] WARN: ${message}`, data)
        break
      case 'INFO':
        console.log(`🔵 [${timestamp}] INFO: ${message}`, data)
        break
      case 'DEBUG':
        if (process.env.NODE_ENV === 'development') {
          console.debug(`⚪ [${timestamp}] DEBUG: ${message}`, data)
        }
        break
    }

    // In production, you would send this to your logging service
    // Example: await sendToLogService(logEntry)
  }

  static error(message, data = {}) {
    this.log('ERROR', message, data)
  }

  static warn(message, data = {}) {
    this.log('WARN', message, data)
  }

  static info(message, data = {}) {
    this.log('INFO', message, data)
  }

  static debug(message, data = {}) {
    this.log('DEBUG', message, data)
  }
}

/**
 * Performance metrics collector
 */
export class MetricsCollector {
  constructor() {
    this.metrics = new Map()
    this.startTimes = new Map()
  }

  // Start timing an operation
  startTimer(operationId) {
    this.startTimes.set(operationId, Date.now())
  }

  // End timing and record metric
  endTimer(operationId, metricName, tags = {}) {
    const startTime = this.startTimes.get(operationId)
    if (!startTime) {
      Logger.warn('Timer not found for operation', { operationId })
      return
    }

    const duration = Date.now() - startTime
    this.recordMetric(metricName, duration, 'ms', tags)
    this.startTimes.delete(operationId)

    return duration
  }

  // Record a metric
  recordMetric(name, value, unit = 'count', tags = {}) {
    const key = `${name}:${JSON.stringify(tags)}`
    const existing = this.metrics.get(key) || { count: 0, sum: 0, min: Infinity, max: -Infinity }

    existing.count++
    existing.sum += value
    existing.min = Math.min(existing.min, value)
    existing.max = Math.max(existing.max, value)
    existing.avg = existing.sum / existing.count
    existing.unit = unit
    existing.tags = tags
    existing.lastUpdated = new Date().toISOString()

    this.metrics.set(key, existing)
  }

  // Get all metrics
  getMetrics() {
    const result = {}
    for (const [key, value] of this.metrics.entries()) {
      const [name] = key.split(':')
      if (!result[name]) result[name] = []
      result[name].push(value)
    }
    return result
  }

  // Clear metrics (useful for periodic resets)
  clearMetrics() {
    this.metrics.clear()
  }
}

// Global metrics collector instance
export const metricsCollector = new MetricsCollector()

/**
 * Payment metrics calculator
 */
export async function getPaymentMetrics(timeRange = '24h') {
  try {
    await connectedDB()

    const now = new Date()
    let startDate

    switch (timeRange) {
      case '1h':
        startDate = new Date(now.getTime() - 60 * 60 * 1000)
        break
      case '24h':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000)
        break
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      default:
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000)
    }

    // Get payment statistics
    const stats = await PaymentTransaction.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate },
        },
      },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
          avgAmount: { $avg: '$amount' },
        },
      },
    ])

    // Calculate success rate
    const totalTransactions = stats.reduce((sum, stat) => sum + stat.count, 0)
    const successfulTransactions = stats.find((s) => s._id === 'succeeded')?.count || 0
    const successRate = totalTransactions > 0 ? (successfulTransactions / totalTransactions) * 100 : 0

    // Get webhook processing times
    const webhookStats = await PaymentTransaction.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate },
          'webhook_events.0': { $exists: true },
        },
      },
      {
        $project: {
          webhookCount: { $size: '$webhook_events' },
          lastWebhookTime: { $max: '$webhook_events.received_at' },
        },
      },
    ])

    return {
      timeRange,
      period: {
        start: startDate.toISOString(),
        end: now.toISOString(),
      },
      transactions: {
        total: totalTransactions,
        byStatus: stats.reduce((acc, stat) => {
          acc[stat._id] = {
            count: stat.count,
            totalAmount: stat.totalAmount,
            avgAmount: stat.avgAmount,
          }
          return acc
        }, {}),
        successRate: Math.round(successRate * 100) / 100,
      },
      webhooks: {
        totalProcessed: webhookStats.reduce((sum, stat) => sum + stat.webhookCount, 0),
        avgProcessingTime: 'N/A', // Would need to track processing times
      },
    }
  } catch (error) {
    Logger.error('Failed to calculate payment metrics', { error: error.message })
    throw error
  }
}

/**
 * System health checker
 */
export async function checkSystemHealth() {
  const health = {
    timestamp: new Date().toISOString(),
    status: 'healthy',
    checks: {},
  }

  // Database health
  try {
    await connectedDB()
    const dbStats = await PaymentTransaction.countDocuments()
    health.checks.database = {
      status: 'healthy',
      message: 'Database connection successful',
      transactionCount: dbStats,
    }
  } catch (error) {
    health.checks.database = {
      status: 'unhealthy',
      message: 'Database connection failed',
      error: error.message,
    }
    health.status = 'unhealthy'
  }

  // Memory usage
  const memUsage = process.memoryUsage()
  health.checks.memory = {
    status: memUsage.heapUsed < 500 * 1024 * 1024 ? 'healthy' : 'warning', // 500MB threshold
    heapUsed: `${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`,
    heapTotal: `${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`,
    external: `${Math.round(memUsage.external / 1024 / 1024)}MB`,
  }

  // Rate limiting status
  try {
    const { getRateLimitStatus } = await import('./rateLimiting.js')
    health.checks.rateLimiting = await getRateLimitStatus()
  } catch (error) {
    health.checks.rateLimiting = {
      status: 'error',
      message: 'Rate limiting status unavailable',
      error: error.message,
    }
  }

  return health
}

/**
 * Critical event alerting
 */
export function alertCriticalEvent(event, details = {}) {
  const alertData = {
    timestamp: new Date().toISOString(),
    event,
    severity: 'critical',
    service: 'zoho-payment-api',
    environment: process.env.NODE_ENV || 'development',
    details,
  }

  // Log the critical event
  Logger.error(`CRITICAL ALERT: ${event}`, alertData)

  // In production, you would send this to your alerting service
  // Example: await sendToAlertingService(alertData)

  return alertData
}

const monitoringModule = {
  Logger,
  MetricsCollector,
  metricsCollector,
  getPaymentMetrics,
  checkSystemHealth,
  alertCriticalEvent,
}

export default monitoringModule
