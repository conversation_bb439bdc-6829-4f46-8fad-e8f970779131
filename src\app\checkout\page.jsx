'use client'

import { createZohoPaymentSession } from '@/utils/zohoPayment'
import { useSearchParams } from 'next/navigation'
import { Suspense, useCallback, useEffect, useState } from 'react'

function CheckoutPageContent() {
  const searchParams = useSearchParams()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [paymentSession, setPaymentSession] = useState(null)
  const [debugInfo, setDebugInfo] = useState({})

  // Get parameters from Flutter WebView with safe defaults
  const customerId = searchParams?.get('customerId') || 'default_customer'
  const customerName = searchParams?.get('customerName') || 'Customer'
  const customerEmail = searchParams?.get('customerEmail') || '<EMAIL>'
  const invoiceNumber = searchParams?.get('invoiceNumber') || 'Unknown'
  const amount = searchParams?.get('amount') || '0'
  const currency = searchParams?.get('currency') || 'INR'

  const createPaymentSession = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      console.log('🎯 CHECKOUT: Creating payment session for invoice', invoiceNumber)
      console.log('💰 AMOUNT:', amount)
      console.log('👤 CUSTOMER:', customerName, customerEmail)

      // Create payment session using the working implementation
      const paymentSession = await createZohoPaymentSession({
        amount: parseFloat(amount),
        currency_code: currency,
        customer_id: customerId,
        invoice_number: invoiceNumber,
        description: `Payment for AquaPartner Invoice ${invoiceNumber}`,
        customer_name: customerName,
        customer_email: customerEmail,
      })

      console.log('✅ SESSION CREATED: Payment session response:', paymentSession)
      console.log('🔑 SESSION ID: Extracted session_id:', paymentSession.session_id)

      if (!paymentSession.session_id) {
        console.error('❌ SESSION ERROR: No session_id in response')
        throw new Error('Invalid payment session response')
      }

      setPaymentSession(paymentSession)

      // Initialize payment after successful session creation
      setTimeout(() => {
        initializePayment(paymentSession)
      }, 1000)
    } catch (err) {
      console.error('❌ Payment session creation error:', err)
      setError(`Failed to create payment session: ${err.message}`)
    } finally {
      setLoading(false)
    }
  }, [amount, currency, customerId, invoiceNumber, customerName, customerEmail, initializePayment])

  // New advanced payment method with automatic selection and fallback
  const createPaymentAdvanced = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      console.log('🎯 CHECKOUT ADVANCED: Creating payment with automatic method selection')
      console.log('💰 AMOUNT:', amount)
      console.log('👤 CUSTOMER:', customerName, customerEmail)

      // Create payment using automatic method selection
      const paymentResponse = await createZohoPayment({
        amount: parseFloat(amount),
        currency: currency,
        customer_id: customerId,
        customer_name: customerName,
        customer_email: customerEmail,
        invoice_number: invoiceNumber,
        description: `Payment for AquaPartner Invoice ${invoiceNumber}`,
        redirect_url: `${window.location.origin}/payment-success?invoice=${invoiceNumber}`,
        reference_id: `${customerId}_${invoiceNumber}_${Date.now()}`,
      })

      console.log('✅ PAYMENT CREATED: Payment response:', paymentResponse)
      console.log('🔧 PAYMENT METHOD: Used method:', paymentResponse.payment_method)

      if (paymentResponse.fallback_used) {
        console.log('🔄 FALLBACK: Original method failed, used fallback')
        console.log('  - Original:', paymentResponse.original_method)
        console.log('  - Final:', paymentResponse.final_method)
      }

      setPaymentSession(paymentResponse)

      // Initialize payment based on the method used
      setTimeout(() => {
        initializeZohoPaymentResponse(paymentResponse, invoiceNumber)
      }, 1000)
    } catch (err) {
      console.error('❌ Payment creation error:', err)
      setError(`Failed to create payment: ${err.message}`)
    } finally {
      setLoading(false)
    }
  }, [amount, currency, customerId, invoiceNumber, customerName, customerEmail])

  const initializePayment = useCallback(
    (sessionData) => {
      try {
        console.log('🚀 CHECKOUT: Initializing payment with session data:', sessionData)

        // Check if we're in a mobile WebView environment
        const isWebView = /wv|WebView/i.test(navigator.userAgent)
        console.log('📱 Is WebView:', isWebView)

        // For mobile WebView, use direct URL redirect
        if (isWebView) {
          console.log('📱 CHECKOUT: Initializing mobile payment...')

          // Use the session_id from the created payment session
          const sessionId = sessionData.session_id || sessionData.payment_session_id
          const zohoPaymentUrl = `https://payments.zoho.in/checkout/${sessionId}`
          console.log('🔗 Redirecting to Zoho payment URL:', zohoPaymentUrl)

          // Add a small delay to ensure the page is fully loaded
          setTimeout(() => {
            window.location.href = zohoPaymentUrl
          }, 2000)
        } else {
          // For web, use the working widget implementation
          console.log('🌐 CHECKOUT: Initializing web payment using working implementation...')

          // Use the working implementation from zohoPayment.ts
          const sessionId = sessionData.session_id || sessionData.payment_session_id
          const amountNum = parseFloat(amount)

          console.log('🚀 WIDGET INIT: Calling initializeZohoWidget with:')
          console.log('  - Session ID:', sessionId)
          console.log('  - Amount:', amountNum)
          console.log('  - Invoice:', invoiceNumber)

          // Import and use the working widget initialization
          import('@/utils/zohoPayment')
            .then(({ initializeZohoWidget }) => {
              initializeZohoWidget(sessionId, amountNum, invoiceNumber)
            })
            .catch((error) => {
              console.error('❌ Failed to load zohoPayment module:', error)
              setError('Failed to load payment system')
            })
        }
      } catch (err) {
        console.error('❌ Payment initialization error:', err)
        setError(`Payment initialization failed: ${err.message}`)
      }
    },
    [amount, invoiceNumber]
  )

  const handleBackToApp = useCallback(() => {
    if (window.history.length > 1) {
      window.history.back()
    } else {
      // Fallback for mobile WebView
      window.location.href = 'about:blank'
    }
  }, [])

  // useEffect to initialize the checkout process
  useEffect(() => {
    // Set debug info for troubleshooting
    const debug = {
      customerId,
      customerName,
      customerEmail,
      invoiceNumber,
      amount,
      currency,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'Unknown',
      isWebView: typeof navigator !== 'undefined' ? /wv|WebView/i.test(navigator.userAgent) : false,
      timestamp: new Date().toISOString(),
    }
    setDebugInfo(debug)
    console.log('🔍 CHECKOUT: Debug Info:', debug)

    // Validate required parameters
    if (!amount || parseFloat(amount) <= 0) {
      setError('Invalid amount parameter')
      setLoading(false)
      return
    }

    if (!invoiceNumber || invoiceNumber === 'Unknown') {
      setError('Missing invoice number parameter')
      setLoading(false)
      return
    }

    // Create payment using advanced method with automatic selection and fallback
    createPaymentAdvanced()
  }, [customerId, customerName, customerEmail, invoiceNumber, amount, currency, createPaymentAdvanced])

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="mx-auto max-w-md p-6 text-center">
          <div className="mx-auto mb-4 h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
          <h2 className="mb-2 text-lg font-medium text-gray-900">Loading Payment</h2>
          <p className="mb-4 text-sm text-gray-600">Please wait while we prepare your payment...</p>
          <div className="space-y-1 text-xs text-gray-500">
            <p>Amount: ₹{amount}</p>
            <p>Invoice: {invoiceNumber}</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50 p-4">
        <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-lg">
          <div className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
              <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <h3 className="mb-2 text-lg font-medium text-gray-900">Payment Error</h3>
            <p className="mb-4 text-sm text-gray-600">{error}</p>

            {/* Debug Information */}
            <details className="mb-4 text-left">
              <summary className="cursor-pointer text-xs text-gray-500">Debug Information</summary>
              <pre className="mt-2 overflow-auto rounded bg-gray-50 p-2 text-xs text-gray-400">
                {JSON.stringify(debugInfo, null, 2)}
              </pre>
            </details>

            <button
              onClick={handleBackToApp}
              className="w-full rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700"
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-4">
      <div className="mx-auto max-w-lg px-4">
        <div className="overflow-hidden rounded-lg bg-white shadow-lg">
          <div className="bg-blue-600 px-6 py-4">
            <h1 className="text-xl font-semibold text-white">Complete Payment</h1>
            <p className="mt-1 text-sm text-blue-100">Invoice: {invoiceNumber}</p>
          </div>

          <div className="border-b border-gray-200 px-6 py-4">
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-500">Amount:</span>
                <span className="font-medium">₹{paymentSession?.amount || amount}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Customer:</span>
                <span className="font-medium">{customerName}</span>
              </div>
            </div>
          </div>

          <div className="px-6 py-8">
            <div className="text-center">
              <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
              <p className="text-gray-600">Initializing secure payment...</p>
              <p className="mt-2 text-xs text-gray-500">This may take a few seconds</p>
            </div>
          </div>

          <div className="bg-gray-50 px-6 py-4">
            <div className="flex items-center justify-between">
              <button onClick={handleBackToApp} className="text-sm text-gray-600 hover:text-gray-800">
                ← Back
              </button>
              <div className="text-xs text-gray-500">Secure payment powered by Zoho</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function CheckoutPage() {
  return (
    <Suspense
      fallback={
        <div className="flex min-h-screen items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="mx-auto h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
            <p className="mt-4 text-gray-600">Loading checkout...</p>
          </div>
        </div>
      }
    >
      <CheckoutPageContent />
    </Suspense>
  )
}
