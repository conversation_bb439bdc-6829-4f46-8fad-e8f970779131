# Manual Pay Now Button Test Checklist

## Quick Test Instructions

### Step 1: Navigate to Invoices Page
1. ✅ Go to `/billingAndPayments` in your application
2. ✅ Ensure you're logged in as a customer with invoices
3. ✅ Open browser Developer Tools (F12)
4. ✅ Go to Console tab

### Step 2: Run Automated Test Script
1. ✅ Copy the content from `scripts/test-pay-now-button.js`
2. ✅ Paste it into the browser console
3. ✅ Press Enter to run the automated tests
4. ✅ Review the test results in the console

### Step 3: Manual Button Testing
1. ✅ Look for invoices with "Overdue" status
2. ✅ Verify "Pay Now" button is visible and styled correctly
3. ✅ Click the "Pay Now" button
4. ✅ Watch console logs for payment flow

### Step 4: Expected Console Output
```
🔗 PAY NOW: Initiating payment link for invoice INV-XXX
📧 EMAIL: Using customer.Email: <EMAIL>
🔗 PAYMENT DATA: Creating payment link with: {...}
✅ PAYMENT LINK CREATED: Response: {...}
✅ PAYMENT REDIRECT: User will be redirected to payment link
```

### Step 5: Verify Payment Flow
1. ✅ <PERSON><PERSON> shows loading state (spinner + "Processing...")
2. ✅ <PERSON><PERSON> becomes disabled during processing
3. ✅ User gets redirected to Zoho payment page
4. ✅ Payment page shows correct amount and invoice details

## Common Issues and Solutions

### Issue: No "Pay Now" buttons visible
**Cause**: No overdue invoices
**Solution**: Create test invoices with "Overdue" status

### Issue: Button click does nothing
**Cause**: JavaScript errors or missing functions
**Solution**: Check console for errors, verify imports

### Issue: "createZohoPaymentLink is not defined"
**Cause**: Missing import or function not available
**Solution**: Check imports in invoicesPage.jsx

### Issue: Payment link creation fails
**Cause**: API configuration or authentication issues
**Solution**: Verify environment variables and Zoho credentials

### Issue: Email fallback not working
**Cause**: Customer data missing or incorrect
**Solution**: Check customer object structure

## Test Data Requirements

### Customer Object Should Have:
```javascript
{
  customerId: "CUST001",
  customerName: "Test Customer", 
  Email: "<EMAIL>",        // Primary email field
  email: "<EMAIL>",        // Fallback email field
  mobileNumber: "+91 9876543210",
  companyName: "Test Company"
}
```

### Invoice Object Should Have:
```javascript
{
  invoiceId: "INV001",
  invoiceNumber: "INV-2024-001",
  total: 1500.75,                   // Numeric value
  invoiceStatus: "Overdue",         // Must be "Overdue" for button to show
  invoiceDate: "2024-01-01"
}
```

## Success Criteria

### ✅ Button Functionality
- [ ] Button visible only for overdue invoices
- [ ] Button shows correct text ("Pay Now")
- [ ] Button has proper styling (red background)
- [ ] Button shows loading state when clicked
- [ ] Button becomes disabled during processing

### ✅ Data Handling
- [ ] Customer email handled with fallbacks
- [ ] Payment amount correctly passed
- [ ] Invoice number included in description
- [ ] Reference ID generated correctly
- [ ] Redirect URL configured properly

### ✅ API Integration
- [ ] Payment link creation API called
- [ ] Correct request payload sent
- [ ] Response handled properly
- [ ] Error handling works for failures
- [ ] User redirected to payment page

### ✅ User Experience
- [ ] Clear visual feedback during processing
- [ ] Error messages displayed if something fails
- [ ] Smooth transition to payment page
- [ ] Proper return URL after payment

## Performance Expectations

- **Button Response**: Immediate (< 100ms)
- **Payment Link Creation**: < 3 seconds
- **Page Redirect**: < 1 second
- **Total Flow Time**: < 5 seconds

## Browser Compatibility

Test in these browsers:
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)
- [ ] Mobile Safari (iOS)
- [ ] Mobile Chrome (Android)

## Security Checks

- [ ] No sensitive data logged to console
- [ ] HTTPS used for all API calls
- [ ] Customer data properly sanitized
- [ ] Payment data encrypted in transit

## Final Verification

After testing, confirm:
1. ✅ All automated tests pass
2. ✅ Manual button click works
3. ✅ Payment page loads correctly
4. ✅ No console errors
5. ✅ User experience is smooth

## Troubleshooting Commands

If issues occur, run these in console:

```javascript
// Check if functions are available
console.log('createZohoPaymentLink:', typeof createZohoPaymentLink);
console.log('initializeZohoPaymentResponse:', typeof initializeZohoPaymentResponse);

// Check customer data
console.log('Customer data:', window.customerData || 'Not available');

// Check for Pay Now buttons
console.log('Pay Now buttons:', document.querySelectorAll('button[title*="Pay this overdue invoice"]').length);

// Test email handling
const testCustomer = {customerId: 'TEST', Email: '<EMAIL>'};
console.log('Email test:', testCustomer.Email || testCustomer.email || `${testCustomer.customerId}@aquapartner.com`);
```

## Contact for Issues

If tests fail or issues persist:
1. Check console errors first
2. Verify environment variables
3. Test with different customer/invoice data
4. Review network requests in DevTools
5. Check Zoho API credentials and permissions
