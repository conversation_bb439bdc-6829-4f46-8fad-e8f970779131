#!/usr/bin/env node

/**
 * Migration Script: Zoho Payment SDK Migration
 * 
 * This script helps migrate from the old ZPay implementation 
 * to the official Zoho Payments SDK.
 */

const fs = require('fs');
const path = require('path');

console.log('🔄 Zoho Payment SDK Migration Script');
console.log('=====================================\n');

// Check if we're in the right directory
const packageJsonPath = path.join(process.cwd(), 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ Error: package.json not found. Please run this script from your project root.');
  process.exit(1);
}

// Validation functions
function validateEnvironmentVariables() {
  console.log('1. 🔍 Checking Environment Variables...');
  
  const requiredVars = [
    'ZOHO_PAY_ACCOUNT_ID',
    'ZOHO_PAY_API_KEY',
    'MONGODB_URI',
    'NEXT_PUBLIC_DOMAIN'
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.log('   ⚠️  Missing environment variables:');
    missingVars.forEach(varName => console.log(`      - ${varName}`));
    console.log('   📝 Please check ENVIRONMENT_SETUP_GUIDE.md for setup instructions.\n');
    return false;
  } else {
    console.log('   ✅ All required environment variables are set.\n');
    return true;
  }
}

function checkFileStructure() {
  console.log('2. 📁 Checking File Structure...');
  
  const requiredFiles = [
    'public/payment-templates/zoho-payment.html',
    'src/app/api/payment-page/route.ts',
    'src/app/api/flutter/payment/initiate/route.ts',
    'src/app/lib/zohoPaymentService.js'
  ];

  const missingFiles = requiredFiles.filter(filePath => !fs.existsSync(filePath));
  
  if (missingFiles.length > 0) {
    console.log('   ⚠️  Missing required files:');
    missingFiles.forEach(filePath => console.log(`      - ${filePath}`));
    console.log('   📝 Please ensure all migration files are in place.\n');
    return false;
  } else {
    console.log('   ✅ All required files are present.\n');
    return true;
  }
}

function checkOldImplementation() {
  console.log('3. 🔍 Checking for Old Implementation...');
  
  const oldFiles = [
    'server/routes/paymentRoutes.js',
    'server/public/zoho-payment.html'
  ];

  const existingOldFiles = oldFiles.filter(filePath => fs.existsSync(filePath));
  
  if (existingOldFiles.length > 0) {
    console.log('   ⚠️  Old implementation files found:');
    existingOldFiles.forEach(filePath => console.log(`      - ${filePath}`));
    console.log('   📝 These should be removed as they conflict with the new implementation.\n');
    return false;
  } else {
    console.log('   ✅ No conflicting old files found.\n');
    return true;
  }
}

async function testAPIEndpoint() {
  console.log('4. 🧪 Testing API Endpoint...');
  
  try {
    const testPayload = {
      amount: 1.00,
      invoiceNo: 'MIGRATION-TEST-001',
      customerId: 'TEST-CUSTOMER',
      customerName: 'Test Customer',
      description: 'Migration Test Payment'
    };

    const response = await fetch('http://localhost:3000/api/flutter/payment/initiate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPayload)
    });

    if (response.ok) {
      const data = await response.json();
      if (data.success && data.webview_url) {
        console.log('   ✅ API endpoint is working correctly.');
        console.log(`   🔗 Test WebView URL: ${data.webview_url}\n`);
        return true;
      } else {
        console.log('   ❌ API returned unexpected response:', data);
        return false;
      }
    } else {
      console.log(`   ❌ API request failed with status: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log('   ⚠️  Could not test API endpoint (server may not be running)');
    console.log(`   Error: ${error.message}\n`);
    return false;
  }
}

function generateMigrationReport(envCheck, fileCheck, oldFileCheck, apiCheck) {
  console.log('📊 Migration Report');
  console.log('==================');
  
  const checks = [
    { name: 'Environment Variables', status: envCheck },
    { name: 'File Structure', status: fileCheck },
    { name: 'Old Files Cleanup', status: oldFileCheck },
    { name: 'API Endpoint Test', status: apiCheck }
  ];

  checks.forEach(check => {
    const icon = check.status ? '✅' : '❌';
    console.log(`${icon} ${check.name}`);
  });

  const allPassed = checks.every(check => check.status);
  
  console.log('\n' + '='.repeat(40));
  
  if (allPassed) {
    console.log('🎉 Migration Complete!');
    console.log('Your Zoho Payment integration is now using the official SDK.');
    console.log('\nNext Steps:');
    console.log('1. Test the payment flow in your Flutter app');
    console.log('2. Verify payment callbacks work correctly');
    console.log('3. Test with small amounts before going live');
  } else {
    console.log('⚠️  Migration Incomplete');
    console.log('Please address the issues above before proceeding.');
    console.log('\nFor help, check:');
    console.log('- ENVIRONMENT_SETUP_GUIDE.md');
    console.log('- FLUTTER_WEBVIEW_PAYMENT_INTEGRATION.md');
  }
  
  console.log('\n📚 Documentation:');
  console.log('- Environment Setup: ENVIRONMENT_SETUP_GUIDE.md');
  console.log('- Flutter Integration: FLUTTER_WEBVIEW_PAYMENT_INTEGRATION.md');
  console.log('- API Documentation: API_DOCUMENTATION.md');
}

// Main migration process
async function runMigration() {
  const envCheck = validateEnvironmentVariables();
  const fileCheck = checkFileStructure();
  const oldFileCheck = checkOldImplementation();
  const apiCheck = await testAPIEndpoint();
  
  generateMigrationReport(envCheck, fileCheck, oldFileCheck, apiCheck);
}

// Run the migration
runMigration().catch(error => {
  console.error('❌ Migration script failed:', error);
  process.exit(1);
});
