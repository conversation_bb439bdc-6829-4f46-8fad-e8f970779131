# Zoho Payment Webhook Security Configuration

This document outlines the enhanced security features implemented for the Zoho Payment webhook integration and their configuration requirements.

## 🔒 Security Features Implemented

### 1. **Idempotency Protection**
- **Purpose**: Prevents duplicate webhook processing
- **Implementation**: Unique event ID generation and database tracking
- **Configuration**: Enabled by default, no configuration required
- **Benefits**: Eliminates duplicate payment processing and data inconsistencies

### 2. **Replay Attack Prevention**
- **Purpose**: Rejects old webhook requests to prevent replay attacks
- **Implementation**: Timestamp validation with configurable age limits
- **Configuration**: 5-minute default window (configurable)
- **Benefits**: Protects against malicious replay of old webhook events

### 3. **IP Whitelisting**
- **Purpose**: Restricts webhook access to authorized IP addresses
- **Implementation**: Configurable IP and CIDR range validation
- **Configuration**: Environment variable `ZOHO_WEBHOOK_IP_WHITELIST`
- **Benefits**: Prevents unauthorized webhook submissions

### 4. **Enhanced Retry Mechanism**
- **Purpose**: Automatic retry with exponential backoff for failed processing
- **Implementation**: 3 retries with 1s, 2s, 4s delays
- **Configuration**: Built-in with configurable parameters
- **Benefits**: Improves reliability and handles temporary failures

## 🛠️ Environment Variables Configuration

Add these environment variables to your `.env.local` file:

```bash
# Webhook Security Configuration

# Required: Webhook signature verification secret
ZOHO_WEBHOOK_SECRET=your_zoho_webhook_secret_here

# Optional: IP Whitelisting (comma-separated IPs and CIDR ranges)
# Example: Single IPs and CIDR ranges
ZOHO_WEBHOOK_IP_WHITELIST=***********,***********/24,*************

# Optional: Zoho's official IP ranges (update as needed)
# ZOHO_WEBHOOK_IP_WHITELIST=*********/16,**********/16,**********/16

# Optional: Webhook processing configuration
WEBHOOK_MAX_AGE_MINUTES=5
WEBHOOK_MAX_RETRIES=3
WEBHOOK_BASE_DELAY_MS=1000

# Required: Domain for webhook URL generation
NEXT_PUBLIC_DOMAIN=https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net
```

## 📋 Zoho Payments Dashboard Configuration

### Step 1: Configure Webhook URL
1. Log into your Zoho Payments dashboard
2. Navigate to **Settings** → **Webhooks**
3. Add webhook URL: `https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api/zoho/webhooks/payment`
4. Select events to monitor:
   - `payment.succeeded`
   - `payment.failed`
   - `payment.pending`
   - `payment.cancelled`
   - `payment_session.expired`

### Step 2: Configure Webhook Secret
1. Generate a strong webhook secret (32+ characters)
2. Add the secret to Zoho Payments webhook configuration
3. Set the same secret in your `ZOHO_WEBHOOK_SECRET` environment variable

### Step 3: IP Whitelisting (Optional but Recommended)
1. Contact Zoho support to get their current webhook IP ranges
2. Add the IP ranges to your `ZOHO_WEBHOOK_IP_WHITELIST` environment variable
3. Test webhook delivery to ensure IPs are correctly whitelisted

## 🔍 Monitoring and Administration

### Webhook Statistics API
```bash
# Get webhook processing statistics
GET /api/zoho/webhooks/admin?hours=24&details=true

# Response includes:
# - Processing success/failure rates
# - Average processing times
# - Failed events sample
# - Pending retries count
```

### Manual Retry Processing
```bash
# Trigger manual retry of all pending webhooks
POST /api/zoho/webhooks/admin
Content-Type: application/json

{
  "action": "process_retries"
}
```

### Retry Specific Event
```bash
# Retry a specific webhook event
PUT /api/zoho/webhooks/admin
Content-Type: application/json

{
  "event_id": "webhook_event_id_here"
}
```

### Cleanup Old Events
```bash
# Clean up webhook events older than 90 days
POST /api/zoho/webhooks/admin
Content-Type: application/json

{
  "action": "cleanup_old_events"
}
```

## 🚨 Security Alerts and Monitoring

### Key Metrics to Monitor
1. **Webhook Success Rate**: Should be >98%
2. **Signature Verification Failures**: Should be near 0
3. **IP Blocking Events**: Monitor for unauthorized access attempts
4. **Replay Attack Blocks**: Monitor for suspicious timing patterns
5. **Processing Time**: Should be <500ms average

### Recommended Alerting Rules
```javascript
// Example alerting conditions
{
  "webhook_success_rate_below_95_percent": "CRITICAL",
  "signature_verification_failures_above_5_per_hour": "HIGH",
  "ip_blocking_events_above_10_per_hour": "MEDIUM",
  "processing_time_above_2_seconds": "MEDIUM",
  "retry_queue_above_100_events": "HIGH"
}
```

## 🔧 Troubleshooting

### Common Issues and Solutions

#### 1. Webhook Signature Verification Failures
- **Cause**: Incorrect webhook secret or payload modification
- **Solution**: Verify `ZOHO_WEBHOOK_SECRET` matches Zoho configuration
- **Check**: Ensure no middleware is modifying the request body

#### 2. IP Whitelisting Blocks
- **Cause**: Zoho IP ranges changed or misconfigured whitelist
- **Solution**: Update `ZOHO_WEBHOOK_IP_WHITELIST` with current Zoho IPs
- **Check**: Test with IP whitelisting temporarily disabled

#### 3. Timestamp Validation Failures
- **Cause**: Clock skew or delayed webhook delivery
- **Solution**: Increase `WEBHOOK_MAX_AGE_MINUTES` if needed
- **Check**: Verify server time synchronization

#### 4. High Retry Queue
- **Cause**: Database connectivity issues or external service failures
- **Solution**: Check database connection and external service health
- **Action**: Run manual retry processing: `POST /api/zoho/webhooks/admin`

### Debug Mode
Enable detailed logging by setting:
```bash
LOG_LEVEL=debug
```

This will provide detailed information about:
- Security check results
- Processing timing
- Error details
- Retry attempts

## 📊 Performance Optimization

### Database Indexes
The webhook system creates optimized indexes for:
- Event ID lookups (unique)
- Processing status queries
- Retry queue processing
- Time-based cleanup

### Automatic Cleanup
- Webhook events are automatically deleted after 90 days
- Failed events are retained for manual review
- Processing metrics are aggregated for reporting

### Scaling Considerations
- Rate limiting prevents abuse (100 requests per 15 minutes)
- Retry processing can be run as background jobs
- Database queries are optimized for high volume

## 🔐 Security Best Practices

1. **Rotate webhook secrets regularly** (every 90 days)
2. **Monitor webhook logs** for suspicious patterns
3. **Keep IP whitelists updated** with Zoho's current ranges
4. **Set up alerting** for security events
5. **Regular security audits** of webhook processing
6. **Backup webhook event data** for compliance
7. **Test disaster recovery** procedures

## 📞 Support and Maintenance

### Regular Maintenance Tasks
- Weekly review of failed webhook events
- Monthly cleanup of old webhook data
- Quarterly security audit and IP whitelist updates
- Annual webhook secret rotation

### Emergency Procedures
1. **Webhook Flood**: Enable stricter rate limiting
2. **Security Breach**: Rotate webhook secret immediately
3. **Processing Failures**: Run manual retry processing
4. **IP Changes**: Update whitelist and test connectivity

For technical support, check the webhook admin dashboard at:
`/api/zoho/webhooks/admin?details=true`
