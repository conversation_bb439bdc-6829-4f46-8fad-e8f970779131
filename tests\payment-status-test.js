const https = require('https');

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const requestOptions = {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...options.headers
      }
    };

    const request = https.request(url, requestOptions, (response) => {
      let data = '';
      
      response.on('data', (chunk) => {
        data += chunk;
      });
      
      response.on('end', () => {
        resolve({
          statusCode: response.statusCode,
          headers: response.headers,
          data: data
        });
      });
    });
    
    request.on('error', (error) => {
      reject(error);
    });
    
    if (options.body) {
      request.write(options.body);
    }
    
    request.setTimeout(30000, () => {
      request.destroy();
      reject(new Error('Request timeout'));
    });
    
    request.end();
  });
}

async function testPaymentStatus() {
  const PRODUCTION_URL = 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net';
  const sessionId = '5619000000230047'; // From previous test
  
  console.log('🔍 Testing Payment Status Endpoint Variations');
  console.log('=============================================');
  
  // Test different URL patterns
  const urlPatterns = [
    `/api/zoho/payments/status/${sessionId}`,
    `/api/zoho/payments/status/${sessionId}/`,
    `/api/zoho/payments/${sessionId}/status`,
    `/api/zoho/payments/${sessionId}/status/`,
    `/api/zoho/payments/${sessionId}`,
    `/api/zoho/payments/${sessionId}/`
  ];
  
  for (const pattern of urlPatterns) {
    console.log(`\nTesting: ${pattern}`);
    try {
      const result = await makeRequest(`${PRODUCTION_URL}${pattern}`);
      console.log(`Status: ${result.statusCode}`);
      
      if (result.statusCode === 200) {
        const data = JSON.parse(result.data);
        console.log(`✅ SUCCESS - Response format:`);
        console.log(`   Success: ${data.success}`);
        console.log(`   Payment Session: ${data.payment_session ? 'Present' : 'Missing'}`);
        console.log(`   Data Keys: ${Object.keys(data).join(', ')}`);
        break; // Found working endpoint
      } else if (result.statusCode === 404) {
        console.log(`❌ Not Found`);
      } else {
        console.log(`⚠️ Status ${result.statusCode}: ${result.data.substring(0, 100)}`);
      }
    } catch (error) {
      console.log(`💥 Error: ${error.message}`);
    }
  }
}

async function testPaymentList() {
  const PRODUCTION_URL = 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net';
  
  console.log('\n\n📋 Testing Payment List Endpoint Variations');
  console.log('==========================================');
  
  // Test different URL patterns and parameters
  const urlPatterns = [
    `/api/zoho/payments/list?customer_id=test_customer_corrected&limit=5`,
    `/api/zoho/payments/list/?customer_id=test_customer_corrected&limit=5`,
    `/api/zoho/payments/list`,
    `/api/zoho/payments/list/`,
    `/api/zoho/payments?customer_id=test_customer_corrected&limit=5`,
    `/api/zoho/payments/?customer_id=test_customer_corrected&limit=5`
  ];
  
  for (const pattern of urlPatterns) {
    console.log(`\nTesting: ${pattern}`);
    try {
      const result = await makeRequest(`${PRODUCTION_URL}${pattern}`);
      console.log(`Status: ${result.statusCode}`);
      
      if (result.statusCode === 200) {
        const data = JSON.parse(result.data);
        console.log(`✅ SUCCESS - Response format:`);
        console.log(`   Success: ${data.success}`);
        console.log(`   Transactions Type: ${typeof data.transactions}`);
        console.log(`   Transactions Array: ${Array.isArray(data.transactions)}`);
        console.log(`   Transactions Length: ${data.transactions?.length || 'N/A'}`);
        console.log(`   Data Keys: ${Object.keys(data).join(', ')}`);
        
        if (data.transactions && typeof data.transactions === 'object') {
          console.log(`   Transactions Content: ${JSON.stringify(data.transactions).substring(0, 200)}...`);
        }
      } else if (result.statusCode === 404) {
        console.log(`❌ Not Found`);
      } else {
        console.log(`⚠️ Status ${result.statusCode}: ${result.data.substring(0, 100)}`);
      }
    } catch (error) {
      console.log(`💥 Error: ${error.message}`);
    }
  }
}

async function runTests() {
  await testPaymentStatus();
  await testPaymentList();
  console.log('\n🏁 Payment Status and List Testing Complete');
}

runTests().catch(console.error);
