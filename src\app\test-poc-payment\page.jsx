'use client'

import { useState } from 'react'

export default function TestPOCPaymentPage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState(null)
  const [error, setError] = useState(null)
  const [paymentInstance, setPaymentInstance] = useState(null)

  const testDirectTokenRefresh = async () => {
    try {
      setLoading(true)
      setError(null)
      setResult(null)

      console.log('Testing direct OAuth token refresh...')

      const response = await fetch('/api/test-poc-token-refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Token refresh failed')
      }

      setResult(data)
      console.log('Token refresh result:', data)

    } catch (err) {
      console.error('Token refresh error:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const testPOCPayment = async () => {
    try {
      setLoading(true)
      setError(null)
      setResult(null)

      console.log('Testing POC payment approach...')

      // Load ZPayments SDK
      await loadZPaymentsSDK()

      if (typeof window.ZPayments === 'undefined') {
        throw new Error('ZPayments SDK not loaded')
      }

      // POC-style configuration
      let config = {
        account_id: process.env.NEXT_PUBLIC_ZOHO_PAY_ACCOUNT_ID,
        domain: 'IN',
        otherOptions: {
          api_key: process.env.NEXT_PUBLIC_ZOHO_PAY_API_KEY,
        },
      }

      console.log('Creating ZPayments instance with config:', config)

      // Create instance
      const instance = new window.ZPayments(config)
      setPaymentInstance(instance)

      // Test payment options
      let options = {
        amount: '1.0',
        currency_code: 'INR',
        payments_session_id: '****************', // Test session ID
        currency_symbol: '₹',
        business: 'AquaPartner Test',
        description: 'Test payment using POC approach',
        address: {
          name: 'Test Customer',
          email: '<EMAIL>',
        },
      }

      console.log('Initiating payment with options:', options)

      try {
        // Use POC approach
        let data = await instance.requestPaymentMethod(options)
        console.log('Payment successful:', JSON.stringify(data))
        setResult({ success: true, data })

      } catch (paymentError) {
        console.error('Payment error:', JSON.stringify(paymentError))
        
        if (paymentError.code !== 'widget_closed') {
          throw paymentError
        } else {
          setResult({ cancelled: true, message: 'Payment widget closed by user' })
        }
      } finally {
        // Cleanup
        if (instance) {
          try {
            await instance.close()
            console.log('Payment instance closed')
          } catch (closeError) {
            console.warn('Error closing payment instance:', closeError)
          }
        }
      }

    } catch (err) {
      console.error('POC payment test error:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const loadZPaymentsSDK = () => {
    return new Promise((resolve, reject) => {
      if (window.ZPayments) {
        resolve()
        return
      }

      const script = document.createElement('script')
      script.src = 'https://static.zohocdn.com/zpay/zpay-js/v1/zpayments.js'
      script.onload = () => {
        console.log('ZPayments SDK loaded successfully')
        resolve()
      }
      script.onerror = () => {
        reject(new Error('Failed to load ZPayments SDK'))
      }

      document.head.appendChild(script)
    })
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow-lg rounded-lg p-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-8">POC Payment Approach Test</h1>
          
          <div className="space-y-6">
            {/* Token Refresh Test */}
            <div className="border border-gray-200 rounded-lg p-6">
              <h2 className="text-lg font-semibold text-gray-800 mb-4">1. Direct OAuth Token Refresh Test</h2>
              <p className="text-sm text-gray-600 mb-4">
                Test the POC approach for direct OAuth token refresh using environment variables.
              </p>
              <button
                onClick={testDirectTokenRefresh}
                disabled={loading}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:bg-gray-400"
              >
                {loading ? 'Testing...' : 'Test Token Refresh'}
              </button>
            </div>

            {/* POC Payment Test */}
            <div className="border border-gray-200 rounded-lg p-6">
              <h2 className="text-lg font-semibold text-gray-800 mb-4">2. ZPayments SDK Test</h2>
              <p className="text-sm text-gray-600 mb-4">
                Test the POC approach using ZPayments SDK with requestPaymentMethod().
              </p>
              <button
                onClick={testPOCPayment}
                disabled={loading}
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 disabled:bg-gray-400"
              >
                {loading ? 'Testing...' : 'Test POC Payment'}
              </button>
            </div>

            {/* Results Display */}
            {(result || error) && (
              <div className="border border-gray-200 rounded-lg p-6">
                <h2 className="text-lg font-semibold text-gray-800 mb-4">Test Results</h2>
                
                {error && (
                  <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
                    <h3 className="text-sm font-medium text-red-800">Error</h3>
                    <p className="text-sm text-red-700 mt-1">{error}</p>
                  </div>
                )}

                {result && (
                  <div className="bg-green-50 border border-green-200 rounded-md p-4">
                    <h3 className="text-sm font-medium text-green-800">Success</h3>
                    <pre className="text-sm text-green-700 mt-2 whitespace-pre-wrap">
                      {JSON.stringify(result, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            )}

            {/* Environment Variables Info */}
            <div className="border border-gray-200 rounded-lg p-6">
              <h2 className="text-lg font-semibold text-gray-800 mb-4">Required Environment Variables</h2>
              <div className="text-sm text-gray-600 space-y-2">
                <p><strong>For Direct OAuth Refresh:</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>ZOHO_OAUTH_GENERATE_ACCESS_TOKEN_URL</li>
                  <li>ZOHO_OAUTH_REFRESH_TOKEN</li>
                  <li>ZOHO_OAUTH_CLIENT_ID</li>
                  <li>ZOHO_OAUTH_CLIENT_SECRET</li>
                  <li>ZOHO_OAUTH_REDIRECT_URI</li>
                </ul>
                
                <p className="mt-4"><strong>For POC Payment:</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>NEXT_PUBLIC_ZOHO_PAY_ACCOUNT_ID</li>
                  <li>NEXT_PUBLIC_ZOHO_PAY_API_KEY</li>
                  <li>ZOHO_PAYMENT_SESSION_URL (optional)</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
