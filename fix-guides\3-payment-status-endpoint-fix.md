# Fix Guide: Payment Status Endpoint 500 Errors

## Problem

Both local and production environments return Internal Server Error (500) on `/api/zoho/payments/status/[sessionId]`.

## Root Cause Analysis

### Step 1: Check Current Implementation

```bash
# Examine the status endpoint file
cat src/app/api/zoho/payments/status/[sessionId]/route.js
```

### Step 2: Test with Valid Session ID

```bash
# First create a payment session to get valid ID
curl -X POST http://localhost:3000/api/zoho/payments/create-session \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 100,
    "currency": "INR",
    "description": "Test",
    "invoice_number": "TEST_001",
    "customer_id": "test"
  }'

# Use the returned session ID to test status endpoint
curl http://localhost:3000/api/zoho/payments/status/RETURNED_SESSION_ID
```

### Step 3: Check Server Logs

```bash
# Check Next.js development logs for error details
npm run dev
# Look for error stack traces when accessing status endpoint
```

## Solution Steps

### Step 1: Fix Route Parameter Handling

Update `src/app/api/zoho/payments/status/[sessionId]/route.js`:

```javascript
import zohoPaymentService from '@/app/lib/zohoPaymentService'

/**
 * GET /api/zoho/payments/status/[sessionId]
 * Get payment session status and details
 */
export async function GET(request, { params }) {
  try {
    // Extract sessionId from params
    const { sessionId } = params

    // Validate sessionId parameter
    if (!sessionId) {
      return new Response(
        JSON.stringify({
          error: 'Missing session ID',
          message: 'Payment session ID is required',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      )
    }

    // Validate sessionId format (basic check)
    if (typeof sessionId !== 'string' || sessionId.length < 5) {
      return new Response(
        JSON.stringify({
          error: 'Invalid session ID',
          message: 'Payment session ID format is invalid',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      )
    }

    console.log(`Fetching payment status for session: ${sessionId}`)

    // Get payment session from Zoho
    let paymentSession
    try {
      paymentSession = await zohoPaymentService.getPaymentSession(sessionId)
    } catch (zohoError) {
      console.error('Zoho API error:', zohoError)

      // Handle specific Zoho errors
      if (zohoError.message.includes('not found') || zohoError.message.includes('404')) {
        return new Response(
          JSON.stringify({
            error: 'Payment session not found',
            message: `No payment session found with ID: ${sessionId}`,
          }),
          {
            status: 404,
            headers: { 'Content-Type': 'application/json' },
          }
        )
      }

      throw zohoError // Re-throw for general error handling
    }

    // Get local transaction record
    let transaction
    try {
      transaction = await zohoPaymentService.getTransaction(sessionId)
    } catch (dbError) {
      console.error('Database error:', dbError)
      // Continue without local transaction if DB fails
      transaction = null
    }

    // Prepare response data
    const responseData = {
      success: true,
      payment_session: paymentSession,
      transaction: transaction,
      session_id: sessionId,
      status: paymentSession?.status || 'unknown',
      amount: paymentSession?.amount,
      currency: paymentSession?.currency,
      created_time: paymentSession?.created_time,
      last_updated: new Date().toISOString(),
    }

    return new Response(JSON.stringify(responseData), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    })
  } catch (error) {
    console.error('Payment status retrieval error:', error)

    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        message: 'Failed to retrieve payment status',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined,
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    )
  }
}
```

### Step 2: Fix ZohoPaymentService Methods

Update `src/app/lib/zohoPaymentService.js` to add missing methods:

```javascript
/**
 * Get payment session details from Zoho
 */
async getPaymentSession(sessionId) {
  try {
    const accessToken = await this.getValidAccessToken()

    const response = await fetch(
      `${this.baseURL}/paymentsessions/${sessionId}?account_id=${this.accountId}`,
      {
        method: 'GET',
        headers: {
          'Authorization': `Zoho-oauthtoken ${accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    )

    if (!response.ok) {
      if (response.status === 404) {
        throw new Error(`Payment session not found: ${sessionId}`)
      }
      throw new Error(`Zoho API error: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()

    if (data.code !== 0) {
      throw new Error(`Zoho API Error: ${data.message}`)
    }

    return data.payments_session
  } catch (error) {
    console.error('Error retrieving payment session:', error)
    throw error
  }
}

/**
 * Get local transaction record
 */
async getTransaction(sessionId) {
  try {
    await connectedDB()

    const transaction = await PaymentTransaction.findOne({
      payments_session_id: sessionId
    })

    return transaction
  } catch (error) {
    console.error('Error retrieving transaction:', error)
    throw error
  }
}
```

### Step 3: Add Error Handling for Database Connection

Update database connection handling:

```javascript
// In zohoPaymentService.js, improve error handling
async getValidAccessToken() {
  try {
    await connectedDB()
    const result = await PaymentAccessToken.findOne({})

    if (!result || !result.access_token) {
      throw new Error('No Zoho Payment token found. Please check token management service.')
    }

    return result.access_token
  } catch (dbError) {
    console.error('Database connection error:', dbError)
    throw new Error(`Database connection failed: ${dbError.message}`)
  }
}
```

### Step 4: Add Route File if Missing

If the route file doesn't exist, create it:

```bash
# Create directory structure
mkdir -p src/app/api/zoho/payments/status/[sessionId]

# Create the route file
touch src/app/api/zoho/payments/status/[sessionId]/route.js
```

## Testing Procedures

### Test 1: Valid Session ID

```bash
# Create payment session first
RESPONSE=$(curl -s -X POST http://localhost:3000/api/zoho/payments/create-session \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 100,
    "currency": "INR",
    "description": "Status Test",
    "invoice_number": "STATUS_001",
    "customer_id": "status_test"
  }')

# Extract session ID (assuming jq is available)
SESSION_ID=$(echo $RESPONSE | jq -r '.data.payment_session_id')

# Test status endpoint
curl http://localhost:3000/api/zoho/payments/status/$SESSION_ID
```

Expected: Status 200 with payment session data

### Test 2: Invalid Session ID

```bash
curl http://localhost:3000/api/zoho/payments/status/invalid_session_id
```

Expected: Status 404 with error message

### Test 3: Missing Session ID

```bash
curl http://localhost:3000/api/zoho/payments/status/
```

Expected: Status 400 with error message

### Test 4: Production Testing

```bash
# Test production endpoint (after deployment)
curl https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api/zoho/payments/status/VALID_SESSION_ID
```

## Debugging Steps

### Step 1: Add Detailed Logging

```javascript
// Add to route handler
console.log('Request params:', params)
console.log('Session ID:', sessionId)
console.log('Zoho API URL:', `${this.baseURL}/paymentsessions/${sessionId}`)
```

### Step 2: Test Database Connection

```javascript
// Add database connection test
try {
  await connectedDB()
  console.log('Database connected successfully')
} catch (dbError) {
  console.error('Database connection failed:', dbError)
}
```

### Step 3: Test Zoho API Connection

```javascript
// Test Zoho API accessibility
try {
  const accessToken = await this.getValidAccessToken()
  console.log('Access token retrieved:', accessToken ? 'Yes' : 'No')
} catch (tokenError) {
  console.error('Token retrieval failed:', tokenError)
}
```

## Verification Script

Create `tests/status-endpoint-test.js`:

```javascript
const fetch = require('node-fetch')

async function testStatusEndpoint() {
  try {
    // Create payment session
    const createResponse = await fetch('http://localhost:3000/api/zoho/payments/create-session', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        amount: 100,
        currency: 'INR',
        description: 'Status Test',
        invoice_number: 'STATUS_TEST_001',
        customer_id: 'status_test_customer',
      }),
    })

    if (!createResponse.ok) {
      console.log('❌ Failed to create payment session')
      return
    }

    const createData = await createResponse.json()
    const sessionId = createData.data?.payment_session_id

    if (!sessionId) {
      console.log('❌ No session ID returned')
      return
    }

    console.log(`✅ Payment session created: ${sessionId}`)

    // Test status endpoint
    const statusResponse = await fetch(`http://localhost:3000/api/zoho/payments/status/${sessionId}`)

    if (statusResponse.ok) {
      const statusData = await statusResponse.json()
      console.log('✅ Status endpoint working')
      console.log('Status:', statusData.status)
    } else {
      console.log(`❌ Status endpoint failed: ${statusResponse.status}`)
      const errorData = await statusResponse.text()
      console.log('Error:', errorData)
    }
  } catch (error) {
    console.log('❌ Test failed:', error.message)
  }
}

testStatusEndpoint()
```

## Rollback Procedure

### Step 1: Revert Changes

```bash
git checkout HEAD~1 -- src/app/api/zoho/payments/status/
git commit -m "Revert status endpoint changes"
```

### Step 2: Temporary Fix

```javascript
// Simple status endpoint that returns basic info
export async function GET(request, { params }) {
  return new Response(
    JSON.stringify({
      message: 'Status endpoint temporarily disabled',
      session_id: params.sessionId,
      status: 'unknown',
    }),
    { status: 200 }
  )
}
```

## Success Criteria

- Status endpoint returns 200 for valid session IDs
- Returns 404 for non-existent sessions
- Returns 400 for invalid session ID format
- No 500 errors in both local and production
- Proper error messages for all failure cases
