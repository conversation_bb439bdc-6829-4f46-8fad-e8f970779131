import connectedDB from '@/app/config/database'
import { withRateLimit } from '@/app/lib/rateLimiting'
import zohoPaymentService from '@/app/lib/zohoPaymentService'

/**
 * GET /api/zoho/health
 * Health check and system status for Zoho Payment integration
 */
async function handleHealthCheck() {
  const startTime = Date.now()
  Logger.info('Health check requested')

  const healthCheck = {
    timestamp: new Date().toISOString(),
    service: 'Zoho Payment Integration',
    version: '1.0.0',
    status: 'checking...',
    checks: {},
    metrics: {},
    performance: {},
  }

  try {
    // Check database connection
    try {
      await connectedDB()
      healthCheck.checks.database = {
        status: 'healthy',
        message: 'Database connection successful',
      }
    } catch (error) {
      healthCheck.checks.database = {
        status: 'unhealthy',
        message: 'Database connection failed',
        error: error.message,
      }
    }

    // Check environment variables
    const requiredEnvVars = ['ZOHO_PAY_ACCOUNT_ID', 'MONGODB_URI']

    const missingEnvVars = requiredEnvVars.filter((varName) => !process.env[varName])

    healthCheck.checks.environment = {
      status: missingEnvVars.length === 0 ? 'healthy' : 'unhealthy',
      message:
        missingEnvVars.length === 0
          ? 'All required environment variables are set'
          : `Missing environment variables: ${missingEnvVars.join(', ')}`,
      required_variables: requiredEnvVars,
      missing_variables: missingEnvVars,
    }

    // Check Zoho token status
    try {
      const token = await zohoPaymentService.getValidAccessToken()
      healthCheck.checks.zoho_auth = {
        status: 'healthy',
        message: 'Zoho authentication token is available',
        has_token: !!token,
        token_source: 'external_service',
      }
    } catch (error) {
      healthCheck.checks.zoho_auth = {
        status: 'unhealthy',
        message: 'Zoho authentication token not available',
        error: error.message,
        token_source: 'external_service',
        note: 'Tokens are managed by external backend service',
      }
    }

    // Check Zoho API connectivity
    try {
      // Try to make a simple API call to test connectivity
      await zohoPaymentService.getValidAccessToken()
      healthCheck.checks.zoho_api = {
        status: 'healthy',
        message: 'Zoho API is accessible',
      }
    } catch (error) {
      healthCheck.checks.zoho_api = {
        status: 'unhealthy',
        message: 'Zoho API connectivity failed',
        error: error.message,
      }
    }

    // Overall health status
    const allChecks = Object.values(healthCheck.checks)
    const unhealthyChecks = allChecks.filter((check) => check.status === 'unhealthy')

    healthCheck.status = unhealthyChecks.length === 0 ? 'healthy' : 'unhealthy'
    healthCheck.summary = {
      total_checks: allChecks.length,
      healthy_checks: allChecks.length - unhealthyChecks.length,
      unhealthy_checks: unhealthyChecks.length,
    }

    // Add rate limiting status
    try {
      healthCheck.checks.rateLimiting = await getRateLimitStatus()
    } catch (error) {
      healthCheck.checks.rateLimiting = {
        status: 'error',
        message: 'Rate limiting status unavailable',
        error: error.message,
      }
    }

    // Add payment metrics
    try {
      healthCheck.metrics.payments_24h = await getPaymentMetrics('24h')
      healthCheck.metrics.payments_1h = await getPaymentMetrics('1h')
    } catch (error) {
      Logger.warn('Failed to get payment metrics', { error: error.message })
      healthCheck.metrics.error = 'Payment metrics unavailable'
    }

    // Add performance metrics
    const processingTime = Date.now() - startTime
    healthCheck.performance = {
      healthCheckDuration: `${processingTime}ms`,
      memoryUsage: {
        heapUsed: `${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`,
        heapTotal: `${Math.round(process.memoryUsage().heapTotal / 1024 / 1024)}MB`,
      },
      uptime: `${Math.round(process.uptime())}s`,
      nodeVersion: process.version,
    }

    // Add current metrics from collector
    healthCheck.metrics.realtime = metricsCollector.getMetrics()

    // Add configuration info
    healthCheck.configuration = {
      account_id: process.env.ZOHO_PAY_ACCOUNT_ID ? 'configured' : 'missing',
      webhook_secret: process.env.ZOHO_WEBHOOK_SECRET ? 'configured' : 'not configured',
      domain: process.env.NEXT_PUBLIC_DOMAIN || 'not configured',
      rateLimitEnabled: process.env.RATE_LIMIT_ENABLED !== 'false',
      apiTimeout: process.env.API_TIMEOUT_MS || '30000',
      maxRetries: process.env.MAX_RETRY_ATTEMPTS || '3',
    }

    const statusCode = healthCheck.status === 'healthy' ? 200 : 503

    // Log health check completion
    Logger.info('Health check completed', {
      status: healthCheck.status,
      duration: processingTime,
      checks: Object.keys(healthCheck.checks).length,
    })

    return new Response(JSON.stringify(healthCheck), {
      status: statusCode,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
      },
    })
  } catch (error) {
    return new Response(
      JSON.stringify({
        timestamp: new Date().toISOString(),
        service: 'Zoho Payment Integration',
        status: 'error',
        message: 'Health check failed',
        error: error.message,
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    )
  }
}

/**
 * POST /api/zoho/health
 * Run detailed diagnostics
 */
async function handleHealthDiagnostics(request) {
  try {
    const body = await request.json()
    const { include_sensitive = false } = body

    const diagnostics = {
      timestamp: new Date().toISOString(),
      service: 'Zoho Payment Integration',
      diagnostics: {},
    }

    // Database diagnostics
    try {
      await connectedDB()

      // Check collections
      const mongoose = require('mongoose')
      const collections = await mongoose.connection.db.listCollections().toArray()

      diagnostics.diagnostics.database = {
        status: 'healthy',
        connection_state: mongoose.connection.readyState,
        collections: collections.map((col) => col.name),
      }
    } catch (error) {
      diagnostics.diagnostics.database = {
        status: 'unhealthy',
        error: error.message,
      }
    }

    // Token diagnostics
    try {
      const PaymentAccessToken = require('@/app/models/PaymentAccessToken').default
      const tokenRecord = await PaymentAccessToken.findOne({})

      if (tokenRecord && tokenRecord.access_token) {
        diagnostics.diagnostics.tokens = {
          status: 'healthy',
          message: 'Token available from external service',
          has_token: true,
          token_source: 'external_service',
        }

        if (include_sensitive) {
          diagnostics.diagnostics.tokens.access_token_preview = tokenRecord.access_token
            ? `${tokenRecord.access_token.substring(0, 10)}...`
            : 'none'
        }
      } else {
        diagnostics.diagnostics.tokens = {
          status: 'unhealthy',
          message: 'No token found in database',
          has_token: false,
          token_source: 'external_service',
          note: 'Check external token management service',
        }
      }
    } catch (error) {
      diagnostics.diagnostics.tokens = {
        status: 'error',
        error: error.message,
        token_source: 'external_service',
      }
    }

    // Transaction diagnostics
    try {
      const PaymentTransaction = require('@/app/models/PaymentTransaction').default
      const transactionStats = await PaymentTransaction.aggregate([
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 },
            total_amount: { $sum: '$amount' },
          },
        },
      ])

      const totalTransactions = await PaymentTransaction.countDocuments()
      const recentTransactions = await PaymentTransaction.countDocuments({
        createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) },
      })

      diagnostics.diagnostics.transactions = {
        status: 'healthy',
        total_transactions: totalTransactions,
        recent_transactions_24h: recentTransactions,
        status_breakdown: transactionStats,
      }
    } catch (error) {
      diagnostics.diagnostics.transactions = {
        status: 'error',
        error: error.message,
      }
    }

    // API endpoints status
    diagnostics.diagnostics.endpoints = {
      authentication: '/api/zoho/auth/*',
      payments: '/api/zoho/payments/*',
      refunds: '/api/zoho/refunds/*',
      webhooks: '/api/zoho/webhooks/*',
      health: '/api/zoho/health',
    }

    return new Response(JSON.stringify(diagnostics), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  } catch (error) {
    return new Response(
      JSON.stringify({
        timestamp: new Date().toISOString(),
        service: 'Zoho Payment Integration',
        status: 'error',
        message: 'Diagnostics failed',
        error: error.message,
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    )
  }
}

// Apply rate limiting to health endpoints
export const GET = withRateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 500, // Higher limit for health checks
  message: 'Too many health check requests.',
})(handleHealthCheck)

export const POST = withRateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 100, // Lower limit for diagnostics
  message: 'Too many diagnostic requests.',
})(handleHealthDiagnostics)
