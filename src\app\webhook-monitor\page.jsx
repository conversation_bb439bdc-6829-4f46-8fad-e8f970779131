'use client'

import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useCallback, useEffect, useState } from 'react'
// Simple icon components to replace lucide-react
const RefreshCw = ({ className, ...props }) => (
  <span className={className} {...props}>
    🔄
  </span>
)
const Search = ({ className, ...props }) => (
  <span className={className} {...props}>
    🔍
  </span>
)
const Download = ({ className, ...props }) => (
  <span className={className} {...props}>
    📥
  </span>
)
const AlertTriangle = ({ className, ...props }) => (
  <span className={className} {...props}>
    ⚠️
  </span>
)
const CheckCircle = ({ className, ...props }) => (
  <span className={className} {...props}>
    ✅
  </span>
)
const Clock = ({ className, ...props }) => (
  <span className={className} {...props}>
    ⏰
  </span>
)
const XCircle = ({ className, ...props }) => (
  <span className={className} {...props}>
    ❌
  </span>
)

/**
 * Webhook Monitoring Dashboard
 * Real-time monitoring of Zoho payment webhook events with comprehensive filtering and analytics
 */
export default function WebhookMonitorPage() {
  const [webhookEvents, setWebhookEvents] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [filters, setFilters] = useState({
    status: 'all',
    eventType: 'all',
    dateRange: '24h',
    searchTerm: '',
  })
  const [stats, setStats] = useState({
    total: 0,
    processing: 0,
    completed: 0,
    failed: 0,
    duplicate: 0,
  })
  const [healthStatus, setHealthStatus] = useState(null)
  const [alerts, setAlerts] = useState([])
  const [autoRefresh, setAutoRefresh] = useState(true)

  // Fetch webhook events from admin API
  const fetchWebhookEvents = useCallback(async () => {
    try {
      setLoading(true)
      const queryParams = new URLSearchParams({
        ...filters,
        limit: 100,
        sort: '-webhook_received_at',
      })

      const response = await fetch(`/api/zoho/webhooks/monitor?${queryParams}`)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      setWebhookEvents(data.events || [])
      setStats(data.stats || stats)
      setError(null)

      // Fetch health status and alerts
      try {
        const healthResponse = await fetch('/api/health/webhook')
        const healthData = await healthResponse.json()
        setHealthStatus(healthData)

        const alertsResponse = await fetch('/api/zoho/webhooks/alerts?action=status')
        const alertsData = await alertsResponse.json()
        if (alertsData.success && alertsData.health.alerts) {
          setAlerts(alertsData.health.alerts)
        }
      } catch (healthError) {
        console.warn('Failed to fetch health status:', healthError)
      }
    } catch (err) {
      console.error('Failed to fetch webhook events:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }, [filters])

  // Auto-refresh functionality
  useEffect(() => {
    fetchWebhookEvents()

    if (autoRefresh) {
      const interval = setInterval(fetchWebhookEvents, 30000) // Refresh every 30 seconds
      return () => clearInterval(interval)
    }
  }, [fetchWebhookEvents, autoRefresh])

  // Status badge component
  const StatusBadge = ({ status }) => {
    const statusConfig = {
      processing: { color: 'bg-blue-100 text-blue-800', icon: Clock },
      completed: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      failed: { color: 'bg-red-100 text-red-800', icon: XCircle },
      duplicate: { color: 'bg-gray-100 text-gray-800', icon: AlertTriangle },
      retry_pending: { color: 'bg-yellow-100 text-yellow-800', icon: RefreshCw },
    }

    const config = statusConfig[status] || statusConfig.processing
    const Icon = config.icon

    return (
      <Badge className={`${config.color} flex items-center gap-1`}>
        <Icon size={12} />
        {status}
      </Badge>
    )
  }

  // Security validation indicator
  const SecurityIndicator = ({ event }) => {
    const securityChecks = [
      { name: 'IP', passed: event.ip_whitelisted },
      { name: 'Signature', passed: event.signature_verified },
      { name: 'Timestamp', passed: event.timestamp_valid },
    ]

    return (
      <div className="flex gap-1">
        {securityChecks.map((check) => (
          <Badge
            key={check.name}
            className={`text-xs ${check.passed ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}
          >
            {check.name}: {check.passed ? '✓' : '✗'}
          </Badge>
        ))}
      </div>
    )
  }

  // Export webhook data
  const exportData = async () => {
    try {
      const response = await fetch('/api/zoho/webhooks/monitor', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'export',
          filters,
        }),
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          // Convert to CSV
          const csvContent = convertToCSV(data.data)
          const blob = new Blob([csvContent], { type: 'text/csv' })
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = `webhook-events-${new Date().toISOString().split('T')[0]}.csv`
          document.body.appendChild(a)
          a.click()
          window.URL.revokeObjectURL(url)
          document.body.removeChild(a)
        }
      }
    } catch (err) {
      console.error('Export failed:', err)
    }
  }

  // Convert data to CSV format
  const convertToCSV = (data) => {
    if (!data || data.length === 0) return ''

    const headers = [
      'Event ID',
      'Event Type',
      'Payment ID',
      'Status',
      'Received At',
      'Processing Time (ms)',
      'Retries',
      'Source IP',
      'Security Checks',
    ]

    const rows = data.map((event) => [
      event.event_id || '',
      event.event_type || '',
      event.payment_session_id || event.payment_link_id || event.payment_id || '',
      event.processing_status || '',
      new Date(event.webhook_received_at).toISOString(),
      event.processing_time_ms || '',
      event.retry_count || 0,
      event.source_ip || '',
      `IP:${event.ip_whitelisted ? 'Pass' : 'Fail'} Sig:${event.signature_verified ? 'Pass' : 'Fail'} Time:${event.timestamp_valid ? 'Pass' : 'Fail'}`,
    ])

    return [headers, ...rows]
      .map((row) => row.map((field) => `"${String(field).replace(/"/g, '""')}"`).join(','))
      .join('\n')
  }

  return (
    <div className="container mx-auto space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Webhook Monitor</h1>
          <p className="text-gray-600">Real-time Zoho payment webhook monitoring</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={autoRefresh ? 'bg-green-50' : ''}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${autoRefresh ? 'animate-spin' : ''}`} />
            Auto Refresh: {autoRefresh ? 'ON' : 'OFF'}
          </Button>
          <Button onClick={fetchWebhookEvents} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" onClick={exportData}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Health Status Alert */}
      {healthStatus && healthStatus.status !== 'healthy' && (
        <Alert variant={healthStatus.status === 'unhealthy' ? 'destructive' : 'default'}>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Webhook system status: <strong>{healthStatus.status.toUpperCase()}</strong>
            {healthStatus.checks &&
              Object.entries(healthStatus.checks).map(
                ([key, check]) =>
                  check.status !== 'healthy' && (
                    <div key={key} className="mt-1 text-sm">
                      {key}: {check.message}
                    </div>
                  )
              )}
          </AlertDescription>
        </Alert>
      )}

      {/* Active Alerts */}
      {alerts.length > 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>
              {alerts.length} Active Alert{alerts.length > 1 ? 's' : ''}:
            </strong>
            {alerts.map((alert, index) => (
              <div key={index} className="mt-1 text-sm">
                [{alert.severity?.toUpperCase()}] {alert.message}
              </div>
            ))}
          </AlertDescription>
        </Alert>
      )}

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-6">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{stats.total}</div>
            <div className="text-sm text-gray-600">Total Events</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">{stats.processing}</div>
            <div className="text-sm text-gray-600">Processing</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
            <div className="text-sm text-gray-600">Completed</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
            <div className="text-sm text-gray-600">Failed</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-gray-600">{stats.duplicate}</div>
            <div className="text-sm text-gray-600">Duplicates</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-purple-600">
              {healthStatus?.status === 'healthy' ? '✅' : healthStatus?.status === 'degraded' ? '⚠️' : '❌'}
            </div>
            <div className="text-sm text-gray-600">System Health</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
            <div>
              <label className="text-sm font-medium">Status</label>
              <Select value={filters.status} onValueChange={(value) => setFilters({ ...filters, status: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="processing">Processing</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                  <SelectItem value="duplicate">Duplicate</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium">Event Type</label>
              <Select value={filters.eventType} onValueChange={(value) => setFilters({ ...filters, eventType: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="payment.succeeded">Payment Succeeded</SelectItem>
                  <SelectItem value="payment.failed">Payment Failed</SelectItem>
                  <SelectItem value="payment.pending">Payment Pending</SelectItem>
                  <SelectItem value="payment.cancelled">Payment Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium">Date Range</label>
              <Select value={filters.dateRange} onValueChange={(value) => setFilters({ ...filters, dateRange: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1h">Last Hour</SelectItem>
                  <SelectItem value="24h">Last 24 Hours</SelectItem>
                  <SelectItem value="7d">Last 7 Days</SelectItem>
                  <SelectItem value="30d">Last 30 Days</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by ID, event type..."
                  value={filters.searchTerm}
                  onChange={(e) => setFilters({ ...filters, searchTerm: e.target.value })}
                  className="pl-10"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>Error loading webhook events: {error}</AlertDescription>
        </Alert>
      )}

      {/* Webhook Events Table */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Webhook Events</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex h-32 items-center justify-center">
              <RefreshCw className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading webhook events...</span>
            </div>
          ) : webhookEvents.length === 0 ? (
            <div className="py-8 text-center text-gray-500">No webhook events found matching the current filters.</div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="p-2 text-left">Timestamp</th>
                    <th className="p-2 text-left">Event ID</th>
                    <th className="p-2 text-left">Type</th>
                    <th className="p-2 text-left">Payment ID</th>
                    <th className="p-2 text-left">Status</th>
                    <th className="p-2 text-left">Security</th>
                    <th className="p-2 text-left">Processing Time</th>
                    <th className="p-2 text-left">Retries</th>
                  </tr>
                </thead>
                <tbody>
                  {webhookEvents.map((event) => (
                    <tr key={event._id} className="border-b hover:bg-gray-50">
                      <td className="p-2">{new Date(event.webhook_received_at).toLocaleString()}</td>
                      <td className="p-2 font-mono text-xs">{event.event_id?.substring(0, 12)}...</td>
                      <td className="p-2">
                        <Badge variant="outline">{event.event_type}</Badge>
                      </td>
                      <td className="p-2 font-mono text-xs">
                        {event.payment_session_id || event.payment_link_id || 'N/A'}
                      </td>
                      <td className="p-2">
                        <StatusBadge status={event.processing_status} />
                      </td>
                      <td className="p-2">
                        <SecurityIndicator event={event} />
                      </td>
                      <td className="p-2">{event.processing_time_ms ? `${event.processing_time_ms}ms` : 'N/A'}</td>
                      <td className="p-2">{event.retry_count || 0}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
