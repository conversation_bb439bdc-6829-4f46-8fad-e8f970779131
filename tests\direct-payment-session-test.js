/**
 * Direct Payment Session Creation Test
 * 
 * Attempts to create a payment session with detailed debugging
 */

const fetch = globalThis.fetch || require('node-fetch')

const PRODUCTION_URL = 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net'

async function createPaymentSession() {
  console.log('💳 Direct Payment Session Creation Test')
  console.log('======================================')
  console.log(`Production URL: ${PRODUCTION_URL}`)
  console.log(`Timestamp: ${new Date().toISOString()}`)
  console.log('======================================\n')

  // Payment data for ₹1 test payment
  const paymentData = {
    amount: 1.0,
    currency: 'INR',
    description: 'Direct Test Payment Session - Safe Amount',
    invoice_number: `DIRECT_TEST_${Date.now()}`,
    customer_id: `direct_customer_${Date.now()}`,
    customer_name: 'Direct Test Customer',
    customer_email: '<EMAIL>',
    customer_phone: '+91-9876543210',
    redirect_url: `${PRODUCTION_URL}/payment/success`,
    reference_id: `DIRECT_REF_${Date.now()}`,
    meta_data: [
      { key: 'test_type', value: 'direct_session_creation' },
      { key: 'amount_safe', value: 'true' },
      { key: 'environment', value: 'production' },
      { key: 'timestamp', value: new Date().toISOString() }
    ]
  }

  console.log('📋 Payment Session Data:')
  console.log(JSON.stringify(paymentData, null, 2))
  console.log('\n🚀 Attempting to create payment session...\n')

  try {
    const startTime = Date.now()
    
    // Make the API call
    const response = await fetch(`${PRODUCTION_URL}/api/zoho/payments/create-session`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'AquaPartner-PaymentTest/1.0',
        'Cache-Control': 'no-cache'
      },
      body: JSON.stringify(paymentData)
    })

    const endTime = Date.now()
    const responseTime = endTime - startTime

    console.log(`⏱️  Response Time: ${responseTime}ms`)
    console.log(`📊 HTTP Status: ${response.status} ${response.statusText}`)
    
    // Log all response headers
    console.log('📋 Response Headers:')
    response.headers.forEach((value, key) => {
      console.log(`   ${key}: ${value}`)
    })

    // Get response body
    const responseText = await response.text()
    console.log(`\n📄 Response Body Length: ${responseText.length} characters`)
    console.log(`📄 Response Body (first 1000 chars):`)
    console.log(responseText.substring(0, 1000))
    if (responseText.length > 1000) {
      console.log('...(truncated)')
    }

    // Try to parse as JSON
    let responseData
    try {
      responseData = JSON.parse(responseText)
      console.log('\n✅ Response is valid JSON')
    } catch (parseError) {
      console.log('\n❌ Response is not valid JSON:', parseError.message)
      return {
        success: false,
        error: 'Invalid JSON response',
        raw_response: responseText,
        response_time: responseTime
      }
    }

    // Analyze the response
    console.log('\n🔍 Response Analysis:')
    
    if (responseData.success && responseData.data && responseData.data.payment_session_id) {
      // SUCCESS CASE
      console.log('🎉 SUCCESS: Payment session created!')
      console.log(`💳 Payment Session ID: ${responseData.data.payment_session_id}`)
      console.log(`💰 Amount: ₹${responseData.data.amount}`)
      console.log(`🏷️  Invoice Number: ${responseData.data.invoice_number}`)
      console.log(`📅 Created Time: ${new Date(responseData.data.created_time * 1000).toISOString()}`)
      console.log(`⏰ Expires In: ${responseData.data.expires_in}`)
      
      if (responseData.payment_session) {
        console.log(`🔗 Payment URL: ${responseData.payment_session.payment_url || 'Not provided'}`)
        console.log(`📊 Session Status: ${responseData.payment_session.status || 'Unknown'}`)
      }
      
      return {
        success: true,
        payment_session_id: responseData.data.payment_session_id,
        payment_url: responseData.payment_session?.payment_url,
        amount: responseData.data.amount,
        currency: responseData.data.currency,
        invoice_number: responseData.data.invoice_number,
        created_time: responseData.data.created_time,
        expires_in: responseData.data.expires_in,
        response_time: responseTime,
        full_response: responseData
      }
      
    } else if (responseData.message && responseData.message.includes('Payment Session Creation Requirements')) {
      // ROUTING ISSUE CASE
      console.log('⚠️  ROUTING ISSUE DETECTED')
      console.log('   POST request is being handled as GET request')
      console.log('   Receiving API documentation instead of payment session')
      console.log('   Azure Static Web Apps routing configuration issue')
      
      return {
        success: false,
        error: 'routing_issue',
        message: 'POST request returning GET response (documentation)',
        response_time: responseTime,
        deployment_status: 'routing_fix_not_deployed_yet'
      }
      
    } else if (responseData.error) {
      // API ERROR CASE
      console.log('❌ API ERROR')
      console.log(`   Error Type: ${responseData.error}`)
      console.log(`   Message: ${responseData.message || 'No message provided'}`)
      console.log(`   Details: ${responseData.details || 'No details provided'}`)
      
      return {
        success: false,
        error: responseData.error,
        message: responseData.message,
        details: responseData.details,
        response_time: responseTime
      }
      
    } else {
      // UNKNOWN RESPONSE CASE
      console.log('❓ UNKNOWN RESPONSE FORMAT')
      console.log('   Response does not match expected patterns')
      
      return {
        success: false,
        error: 'unknown_response_format',
        response_data: responseData,
        response_time: responseTime
      }
    }

  } catch (error) {
    console.log('💥 REQUEST FAILED')
    console.log(`   Error: ${error.message}`)
    console.log(`   Type: ${error.name}`)
    
    if (error.name === 'AbortError') {
      console.log('   Reason: Request timeout')
    } else if (error.message.includes('fetch')) {
      console.log('   Reason: Network connectivity issue')
    } else {
      console.log('   Reason: Unknown error')
    }
    
    return {
      success: false,
      error: 'request_failed',
      message: error.message,
      error_type: error.name
    }
  }
}

// Run the test
if (typeof require !== 'undefined' && require.main === module) {
  createPaymentSession()
    .then(result => {
      console.log('\n' + '='.repeat(60))
      console.log('🏁 PAYMENT SESSION CREATION TEST COMPLETED')
      console.log('='.repeat(60))
      
      if (result.success) {
        console.log('✅ RESULT: SUCCESS')
        console.log(`💳 Payment Session ID: ${result.payment_session_id}`)
        console.log(`💰 Amount: ₹${result.amount} ${result.currency}`)
        console.log(`🔗 Payment URL: ${result.payment_url || 'Not provided'}`)
        console.log(`⏱️  Response Time: ${result.response_time}ms`)
        console.log('\n🎉 Payment session successfully created in production!')
        process.exit(0)
      } else {
        console.log('❌ RESULT: FAILED')
        console.log(`🔍 Error: ${result.error}`)
        console.log(`📝 Message: ${result.message || 'No message'}`)
        console.log(`⏱️  Response Time: ${result.response_time || 'Unknown'}ms`)
        
        if (result.error === 'routing_issue') {
          console.log('\n💡 SOLUTION: Wait for Azure deployment to complete')
          console.log('   The routing fix is committed but not yet deployed')
          console.log('   Check GitHub Actions or try again in 5-10 minutes')
        }
        
        process.exit(1)
      }
    })
    .catch(error => {
      console.error('\n💥 Test execution failed:', error.message)
      process.exit(1)
    })
}

module.exports = { createPaymentSession }
