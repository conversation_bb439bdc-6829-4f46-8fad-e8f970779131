'use client'

import { useRouter, useSearchParams } from 'next/navigation'
import { Suspense, useCallback, useEffect, useState } from 'react'

function PaymentPageContent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [paymentSession, setPaymentSession] = useState(null)

  const sessionId = searchParams.get('session_id')
  const invoiceNumber = searchParams.get('invoice')

  const fetchPaymentSession = useCallback(async () => {
    try {
      const response = await fetch(`/api/zoho/payments/status/${sessionId}/`)

      if (!response.ok) {
        throw new Error('Failed to fetch payment session')
      }

      const data = await response.json()

      if (!data.success) {
        throw new Error(data.message || 'Failed to load payment session')
      }

      setPaymentSession(data.data)

      // Initialize Zoho Payment SDK
      initializeZohoPayment(data.data)
    } catch (err) {
      console.error('Payment session fetch error:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }, [sessionId, initializeZohoPayment, setError, setLoading, setPaymentSession])

  const initializeZohoPayment = useCallback((sessionData) => {
    // Create a script element to load Zoho Payment SDK
    const script = document.createElement('script')
    script.src = 'https://js.zohostatic.com/books/zohopay/zpay.js'
    script.onload = () => {
      // Initialize payment after SDK loads
      if (window.ZPay) {
        const paymentConfig = {
          account_id: process.env.NEXT_PUBLIC_ZOHO_ACCOUNT_ID || '***********',
          payments_session_id: sessionData.payment_session_id,
          amount: sessionData.amount,
          currency: sessionData.currency,
          description: sessionData.description,
          invoice_number: sessionData.invoice_number,
          customer_name: sessionData.customer_name,
          customer_email: sessionData.customer_email,
          redirect_url: `${window.location.origin}/payment-success`,
          cancel_url: `${window.location.origin}/payment-cancel`,
        }

        // Create payment instance
        const zpay = new window.ZPay(paymentConfig)

        // Handle payment events
        zpay.on('payment_success', (data) => {
          console.log('Payment successful:', data)
          router.push(`/payment-success?payment_id=${data.payment_id}&invoice=${invoiceNumber}`)
        })

        zpay.on('payment_failed', (data) => {
          console.log('Payment failed:', data)
          router.push(`/payment-failed?error=${data.error_message}&invoice=${invoiceNumber}`)
        })

        zpay.on('payment_cancelled', () => {
          console.log('Payment cancelled')
          router.push(`/payment-cancel?invoice=${invoiceNumber}`)
        })

        // Render payment form
        zpay.render('payment-container')
      }
    }

    script.onerror = () => {
      setError('Failed to load payment system')
    }

    document.head.appendChild(script)
  }, [router, invoiceNumber, setError])

  useEffect(() => {
    if (!sessionId) {
      setError('Invalid payment session')
      setLoading(false)
      return
    }

    // Fetch payment session details
    fetchPaymentSession()
  }, [sessionId, fetchPaymentSession])

  const handleBackToInvoices = () => {
    router.push('/aquapartner/billingAndPayments')
  }

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
          <p className="mt-4 text-gray-600">Loading payment...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-lg">
          <div className="text-center">
            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
              <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <h3 className="mt-4 text-lg font-medium text-gray-900">Payment Error</h3>
            <p className="mt-2 text-sm text-gray-500">{error}</p>
            <div className="mt-6">
              <button
                onClick={handleBackToInvoices}
                className="flex w-full justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Back to Invoices
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-3xl px-4 sm:px-6 lg:px-8">
        <div className="overflow-hidden rounded-lg bg-white shadow-lg">
          {/* Header */}
          <div className="bg-blue-600 px-6 py-4">
            <h1 className="text-xl font-semibold text-white">Complete Payment</h1>
            {invoiceNumber && <p className="mt-1 text-sm text-blue-100">Invoice: {invoiceNumber}</p>}
          </div>

          {/* Payment Details */}
          {paymentSession && (
            <div className="border-b border-gray-200 px-6 py-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">Amount:</span>
                  <span className="ml-2 font-medium">₹{paymentSession.amount}</span>
                </div>
                <div>
                  <span className="text-gray-500">Currency:</span>
                  <span className="ml-2 font-medium">{paymentSession.currency}</span>
                </div>
                <div className="col-span-2">
                  <span className="text-gray-500">Description:</span>
                  <span className="ml-2 font-medium">{paymentSession.description}</span>
                </div>
              </div>
            </div>
          )}

          {/* Payment Container */}
          <div className="px-6 py-8">
            <div id="payment-container" className="min-h-[400px]">
              <div className="text-center text-gray-500">
                <div className="mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
                <p className="mt-2">Initializing payment system...</p>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-6 py-4">
            <div className="flex items-center justify-between">
              <button onClick={handleBackToInvoices} className="text-sm text-gray-600 hover:text-gray-800">
                ← Back to Invoices
              </button>
              <div className="text-xs text-gray-500">Secure payment powered by Zoho</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function PaymentPage() {
  return (
    <Suspense
      fallback={
        <div className="flex min-h-screen items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="mx-auto h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
            <p className="mt-4 text-gray-600">Loading payment...</p>
          </div>
        </div>
      }
    >
      <PaymentPageContent />
    </Suspense>
  )
}
