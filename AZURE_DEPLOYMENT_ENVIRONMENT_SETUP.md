# Azure Static Web Apps Environment Variables Setup Guide

## Overview

This guide provides step-by-step instructions to configure environment variables for your AquaPartner application deployed on Azure Static Web Apps via GitHub Actions.

## Current Issue

The health endpoint (`GET /api/zoho/health`) returns "unhealthy" status because the `ZOHO_PAY_ACCOUNT_ID` environment variable is missing in the Azure deployment environment.

## Solution Overview

Azure Static Web Apps supports environment variables through two methods:

1. **GitHub Secrets** (Recommended for sensitive data)
2. **Azure Portal Configuration** (Alternative method)

## Method 1: GitHub Secrets Configuration (Recommended)

### Step 1: Add GitHub Repository Secrets

Navigate to your GitHub repository and add the following secrets:

1. Go to your repository on GitHub
2. Click **Settings** → **Secrets and variables** → **Actions**
3. Click **New repository secret** for each of the following:

#### Required Secrets:

```bash
# Domain Configuration
NEXT_PUBLIC_DOMAIN=https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net
NEXT_PUBLIC_API_DOMAIN=/api

# Database Configuration
MONGODB_URI=mongodb+srv://admin:<EMAIL>/aquapartner

# Zoho OAuth Configuration
ZOHO_OAUTH_CLIENT_ID=1000.OYY8H71ELT0DNW5CFFBE124H7GILMP
ZOHO_OAUTH_CLIENT_SECRET=67ca50629a18ba97eb9868be9c8faacf6645edf13d
ZOHO_OAUTH_REFRESH_TOKEN=**********************************************************************

# Zoho Payment Configuration
ZOHO_PAYMENT_SESSION_URL=https://payments.zoho.in/api/v1/paymentsessions
ZOHO_PAY_ACCOUNT_ID=***********
ZOHO_PAY_API_KEY=1003.f35b9411653295bb03db1e8490dc6cdd.0f625d54ec97f4eba7bedd0dc6fc23b8

# Webhook Configuration
ZOHO_WEBHOOK_SECRET=21155a0f5fd70801737195ba85492b7f622fe3a39b1da0a13a76635bfe3b0c1c56c0d7a833170831ac446bcf4ec801288a0e3c4ecf1347d5ceac9f6c54faa43ad9f40e40d9cc77733fe0afed6018bdee
```

### Step 2: Verify GitHub Actions Workflow

The GitHub Actions workflow has been updated to include environment variables. The updated workflow will:

1. Pass all environment variables from GitHub Secrets to the Azure deployment
2. Make them available to your API routes during runtime
3. Ensure the health check endpoint can access `ZOHO_PAY_ACCOUNT_ID`

### Step 3: Deploy and Test

1. **Trigger Deployment:**

   ```bash
   git add .
   git commit -m "Configure environment variables for Azure deployment"
   git push origin main
   ```

2. **Monitor Deployment:**

   - Go to **Actions** tab in your GitHub repository
   - Watch the deployment process
   - Ensure no errors occur during the build and deploy steps

3. **Test Health Endpoint:**

   ```bash
   curl https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api/zoho/health
   ```

   Expected response:

   ```json
   {
     "status": "healthy",
     "checks": {
       "environment": {
         "status": "healthy",
         "message": "All required environment variables are set"
       }
     }
   }
   ```

## Method 2: Azure Portal Configuration (Alternative)

If you prefer to configure environment variables directly in Azure:

### Step 1: Access Azure Portal

1. Go to [Azure Portal](https://portal.azure.com)
2. Navigate to your Static Web App resource
3. Go to **Settings** → **Configuration**

### Step 2: Add Application Settings

Add each environment variable as an application setting:

- Click **+ Add**
- Enter **Name** and **Value** for each variable
- Click **OK** and then **Save**

### Step 3: Important Notes for Azure Portal Method

- Environment variables set in Azure Portal take precedence over GitHub Secrets
- Changes require a restart of the Static Web App
- This method is less secure for sensitive data like API keys

## Security Best Practices

### 1. Environment Variable Security

- ✅ **Use GitHub Secrets** for sensitive data (API keys, database credentials)
- ✅ **Rotate secrets regularly** (especially API keys and tokens)
- ✅ **Use different credentials** for production vs development
- ❌ **Never commit secrets** to your repository

### 2. Production Considerations

For production deployment, consider:

```bash
# Use production domain
NEXT_PUBLIC_DOMAIN=https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net

# Use production database
MONGODB_URI=mongodb+srv://prod-user:<EMAIL>/aquapartner

# Use production Zoho credentials
ZOHO_PAY_ACCOUNT_ID=your_production_account_id
```

## Troubleshooting

### Issue: Environment Variables Not Loading

**Symptoms:**

- Health endpoint still shows "unhealthy"
- Missing environment variables error

**Solutions:**

1. **Verify GitHub Secrets:**

   - Check all secrets are added with correct names
   - Ensure no typos in secret names

2. **Check Deployment Logs:**

   - Go to GitHub Actions → Latest workflow run
   - Check for any environment variable related errors

3. **Verify Azure Configuration:**
   - Ensure the Static Web App is properly configured
   - Check that the API location is set to `/api`

### Issue: Deployment Fails

**Symptoms:**

- GitHub Actions workflow fails
- Build or deployment errors

**Solutions:**

1. **Check Workflow Syntax:**

   - Ensure proper YAML indentation
   - Verify all secret references are correct

2. **Review Build Logs:**
   - Look for specific error messages
   - Check if environment variables are being loaded

## Verification Checklist

After completing the setup:

- [ ] All GitHub Secrets are added
- [ ] GitHub Actions workflow runs successfully
- [ ] Health endpoint returns "healthy" status
- [ ] All required environment variables are present
- [ ] Zoho Payment API functionality works
- [ ] Database connectivity is successful

## Next Steps

Once environment variables are properly configured:

1. **Test Payment Flow:**

   - Create a test payment session
   - Verify payment processing works

2. **Monitor Health Endpoint:**

   - Set up monitoring for the health endpoint
   - Create alerts for unhealthy status

3. **Update Documentation:**
   - Document any production-specific configurations
   - Update deployment procedures

## Support

If you encounter issues:

1. Check the health endpoint for specific error messages
2. Review GitHub Actions logs for deployment errors
3. Verify all environment variables are correctly set
4. Test locally to ensure the application works with the same environment variables
