/**
 * Test script for Zoho Payment Link API
 * Usage: node tests/api/zoho-payment-link-test.js
 */

const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000'

async function testPaymentLinkAPI() {
  console.log('🧪 Testing Zoho Payment Link API...\n')

  // Test 1: GET requirements
  console.log('📋 Test 1: GET /api/zoho/payments/create-link (Requirements)')
  try {
    const response = await fetch(`${API_BASE_URL}/api/zoho/payments/create-link`)
    const data = await response.json()
    
    if (response.ok) {
      console.log('✅ GET request successful')
      console.log('📄 Required fields:', data.required_fields.map(f => f.field).join(', '))
      console.log('📄 Optional fields:', data.optional_fields.length, 'fields available')
    } else {
      console.log('❌ GET request failed:', data)
    }
  } catch (error) {
    console.log('❌ GET request error:', error.message)
  }

  console.log('\n' + '='.repeat(50) + '\n')

  // Test 2: POST with missing required fields
  console.log('📋 Test 2: POST with missing required fields')
  try {
    const response = await fetch(`${API_BASE_URL}/api/zoho/payments/create-link`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        amount: 100,
        // Missing description and customer_email
      }),
    })
    const data = await response.json()
    
    if (response.status === 400) {
      console.log('✅ Validation working - correctly rejected missing fields')
      console.log('📄 Error message:', data.message)
    } else {
      console.log('❌ Validation failed - should have rejected missing fields')
    }
  } catch (error) {
    console.log('❌ POST validation test error:', error.message)
  }

  console.log('\n' + '='.repeat(50) + '\n')

  // Test 3: POST with invalid email
  console.log('📋 Test 3: POST with invalid email')
  try {
    const response = await fetch(`${API_BASE_URL}/api/zoho/payments/create-link`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        amount: 100,
        description: 'Test payment',
        customer_email: 'invalid-email',
      }),
    })
    const data = await response.json()
    
    if (response.status === 400) {
      console.log('✅ Email validation working - correctly rejected invalid email')
      console.log('📄 Error message:', data.message)
    } else {
      console.log('❌ Email validation failed - should have rejected invalid email')
    }
  } catch (error) {
    console.log('❌ POST email validation test error:', error.message)
  }

  console.log('\n' + '='.repeat(50) + '\n')

  // Test 4: POST with valid data (will likely fail due to auth, but tests structure)
  console.log('📋 Test 4: POST with valid data')
  try {
    const testPayload = {
      amount: 100.50,
      currency: 'INR',
      description: 'Test payment for API validation',
      customer_email: '<EMAIL>',
      customer_name: 'Test Customer',
      customer_id: 'TEST-001',
      invoice_number: 'INV-TEST-001',
      send_email: true,
      meta_data: [
        { key: 'test_mode', value: 'true' },
        { key: 'api_test', value: 'payment_link' }
      ]
    }

    const response = await fetch(`${API_BASE_URL}/api/zoho/payments/create-link`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPayload),
    })
    const data = await response.json()
    
    if (response.status === 201) {
      console.log('✅ Payment link created successfully!')
      console.log('🔗 Payment Link ID:', data.data.payment_link_id)
      console.log('🔗 Payment Link URL:', data.data.payment_link_url)
      console.log('💰 Amount:', data.data.amount, data.data.currency)
    } else if (response.status === 401) {
      console.log('⚠️ Authentication error (expected in test environment)')
      console.log('📄 Error:', data.message)
    } else {
      console.log('❌ Payment link creation failed')
      console.log('📄 Response:', data)
    }
  } catch (error) {
    console.log('❌ POST valid data test error:', error.message)
  }

  console.log('\n' + '='.repeat(50) + '\n')
  console.log('🏁 Payment Link API tests completed!')
}

// Run tests if this file is executed directly
if (require.main === module) {
  testPaymentLinkAPI().catch(console.error)
}

module.exports = { testPaymentLinkAPI }
