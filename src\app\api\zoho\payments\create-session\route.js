import connectedDB from '@/app/config/database'
import { withRateLimit } from '@/app/lib/rateLimiting'
import zohoPaymentService from '@/app/lib/zohoPaymentService'

/**
 * POST /api/zoho/payments/create-session
 * Create a new payment session
 */
async function handleCreateSession(request) {
  const sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  const startTime = Date.now()

  try {
    console.log(`🚀 PAYMENT SESSION: Starting creation`, {
      sessionId,
      timestamp: new Date().toISOString(),
      method: 'createPaymentSession',
    })

    // Connect to the database
    await connectedDB()

    const body = await request.json()

    const {
      amount,
      currency = 'INR',
      description,
      invoice_number,
      customer_id,
      customer_name,
      customer_email,
      customer_phone,
      redirect_url,
      reference_id,
      meta_data = [],
    } = body

    // Validate required fields
    if (!amount || !description || !invoice_number || !customer_id) {
      return new Response(
        JSON.stringify({
          error: 'Missing required fields',
          message: 'amount, description, invoice_number, and customer_id are required',
          required_fields: ['amount', 'description', 'invoice_number', 'customer_id'],
        }),
        { status: 400 }
      )
    }

    // Validate amount
    const numericAmount = parseFloat(amount)
    if (isNaN(numericAmount) || numericAmount <= 0) {
      return new Response(
        JSON.stringify({
          error: 'Invalid amount',
          message: 'Amount must be a positive number',
        }),
        { status: 400 }
      )
    }

    // Validate currency
    if (currency !== 'INR') {
      return new Response(
        JSON.stringify({
          error: 'Invalid currency',
          message: 'Only INR currency is supported',
        }),
        { status: 400 }
      )
    }

    const paymentData = {
      amount: numericAmount,
      currency,
      description,
      invoice_number,
      customer_id,
      customer_name,
      customer_email,
      customer_phone,
      redirect_url,
      reference_id,
      meta_data,
    }

    const result = await zohoPaymentService.createPaymentSession(paymentData)

    // Log successful payment session creation
    const processingTime = Date.now() - startTime
    console.log(`✅ PAYMENT SESSION: Created successfully`, {
      sessionId,
      paymentSessionId: result.payment_session.payments_session_id,
      transactionId: result.transaction_id,
      amount: result.payment_session.amount,
      currency: result.payment_session.currency,
      processingTime: `${processingTime}ms`,
      timestamp: new Date().toISOString(),
    })

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Payment session created successfully',
        data: {
          payment_session_id: result.payment_session.payments_session_id,
          amount: result.payment_session.amount,
          currency: result.payment_session.currency,
          description: result.payment_session.description,
          invoice_number: result.payment_session.invoice_number,
          created_time: result.payment_session.created_time,
          transaction_id: result.transaction_id,
          expires_in: '15 minutes',
          processing_time_ms: processingTime,
        },
        payment_session: result.payment_session,
      }),
      { status: 201 }
    )
  } catch (error) {
    // Log error with structured information
    const processingTime = Date.now() - startTime
    console.error(`❌ PAYMENT SESSION: Creation failed`, {
      sessionId,
      error: error.message,
      stack: error.stack,
      processingTime: `${processingTime}ms`,
      timestamp: new Date().toISOString(),
    })

    // Handle specific Zoho API errors
    if (error.message.includes('Zoho API Error')) {
      return new Response(
        JSON.stringify({
          error: 'Zoho Payment API Error',
          message: error.message,
          details: 'Please check your payment data and try again',
        }),
        { status: 400 }
      )
    }

    // Handle token errors
    if (error.message.includes('token')) {
      return new Response(
        JSON.stringify({
          error: 'Authentication Error',
          message: error.message,
          details: 'Please check your Zoho Payment configuration',
        }),
        { status: 401 }
      )
    }

    return new Response(
      JSON.stringify({
        error: 'Payment session creation failed',
        message: error.message,
        details: 'An unexpected error occurred while creating the payment session',
      }),
      { status: 500 }
    )
  }
}

/**
 * GET /api/zoho/payments/create-session
 * Get payment session creation requirements
 */
export async function GET() {
  const requirements = {
    message: 'Payment Session Creation Requirements',
    required_fields: [
      {
        field: 'amount',
        type: 'number',
        description: 'Payment amount (must be positive)',
        example: 100.5,
      },
      {
        field: 'description',
        type: 'string',
        description: 'Payment description (max 500 characters)',
        example: 'Payment for Order #12345',
      },
      {
        field: 'invoice_number',
        type: 'string',
        description: 'Invoice number (max 50 characters)',
        example: 'INV-12345',
      },
      {
        field: 'customer_id',
        type: 'string',
        description: 'Customer identifier',
        example: 'CUST-001',
      },
    ],
    optional_fields: [
      {
        field: 'currency',
        type: 'string',
        description: 'Payment currency (default: INR)',
        example: 'INR',
      },
      {
        field: 'customer_name',
        type: 'string',
        description: 'Customer name',
        example: 'John Doe',
      },
      {
        field: 'customer_email',
        type: 'string',
        description: 'Customer email',
        example: '<EMAIL>',
      },
      {
        field: 'customer_phone',
        type: 'string',
        description: 'Customer phone number',
        example: '+919876543210',
      },
      {
        field: 'redirect_url',
        type: 'string',
        description: 'URL to redirect after payment',
        example: 'https://yourapp.com/payment/success',
      },
      {
        field: 'reference_id',
        type: 'string',
        description: 'Your internal reference ID',
        example: 'REF-12345',
      },
      {
        field: 'meta_data',
        type: 'array',
        description: 'Additional metadata (max 5 items)',
        example: [{ key: 'order_id', value: 'ORD-123' }],
      },
    ],
    example_payload: {
      amount: 100.5,
      currency: 'INR',
      description: 'Payment for Order #12345',
      invoice_number: 'INV-12345',
      customer_id: 'CUST-001',
      customer_name: 'John Doe',
      customer_email: '<EMAIL>',
      customer_phone: '+919876543210',
      redirect_url: 'https://yourapp.com/payment/success',
      reference_id: 'REF-12345',
      meta_data: [
        { key: 'order_id', value: 'ORD-123' },
        { key: 'product_type', value: 'aquaculture' },
      ],
    },
  }

  return new Response(JSON.stringify(requirements), { status: 200 })
}

// Apply rate limiting to payment creation endpoint
export const POST = withRateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 100, // Strict limit for payment creation
  message: 'Too many payment session creation requests. Please try again later.',
})(handleCreateSession)
