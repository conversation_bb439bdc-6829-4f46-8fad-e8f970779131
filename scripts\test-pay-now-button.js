/**
 * Interactive Pay Now Button Test Script
 * 
 * This script can be run in the browser console on the invoices page
 * to test the Pay Now button functionality comprehensively.
 * 
 * Usage:
 * 1. Navigate to the invoices page
 * 2. Open browser developer tools (F12)
 * 3. Go to Console tab
 * 4. Paste this entire script and press Enter
 * 5. Follow the prompts to run tests
 */

class PayNowButtonTester {
  constructor() {
    this.testResults = [];
    this.mockData = {
      customer: {
        customerId: 'TEST_CUSTOMER_001',
        customerName: 'Test Customer',
        Email: '<EMAIL>',
        mobileNumber: '+91 9876543210',
        companyName: 'Test Company'
      },
      invoice: {
        invoiceId: 'TEST_INV_001',
        invoiceNumber: 'INV-2024-001',
        total: 1500.75,
        invoiceStatus: 'Overdue',
        invoiceDate: new Date().toISOString()
      }
    };
  }

  log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const emoji = {
      info: 'ℹ️',
      success: '✅',
      error: '❌',
      warning: '⚠️',
      test: '🧪'
    }[type] || 'ℹ️';
    
    console.log(`${emoji} [${timestamp}] ${message}`);
  }

  async runAllTests() {
    this.log('Starting Pay Now Button Test Suite', 'test');
    console.log('='.repeat(50));

    try {
      await this.testEnvironmentSetup();
      await this.testButtonVisibility();
      await this.testEmailHandling();
      await this.testPaymentDataStructure();
      await this.testErrorHandling();
      await this.testAPIIntegration();
      
      this.displayResults();
    } catch (error) {
      this.log(`Test suite failed: ${error.message}`, 'error');
    }
  }

  async testEnvironmentSetup() {
    this.log('Testing Environment Setup...', 'test');
    
    const tests = [
      {
        name: 'React Components Available',
        test: () => typeof React !== 'undefined',
        expected: true
      },
      {
        name: 'Payment Functions Available',
        test: () => typeof window.createZohoPaymentLink !== 'undefined' || 
                   typeof createZohoPaymentLink !== 'undefined',
        expected: true
      },
      {
        name: 'DOM Elements Present',
        test: () => document.querySelector('table') !== null,
        expected: true
      },
      {
        name: 'Pay Now Buttons Found',
        test: () => document.querySelectorAll('button[title*="Pay this overdue invoice"]').length > 0,
        expected: true
      }
    ];

    tests.forEach(test => {
      const result = test.test();
      const passed = result === test.expected;
      this.testResults.push({
        category: 'Environment',
        name: test.name,
        passed,
        result,
        expected: test.expected
      });
      this.log(`${test.name}: ${passed ? 'PASS' : 'FAIL'}`, passed ? 'success' : 'error');
    });
  }

  async testButtonVisibility() {
    this.log('Testing Button Visibility Logic...', 'test');
    
    const payNowButtons = document.querySelectorAll('button[title*="Pay this overdue invoice"]');
    const paidLabels = document.querySelectorAll('span:contains("Paid")');
    const noPaymentLabels = document.querySelectorAll('span:contains("No payment required")');
    
    const tests = [
      {
        name: 'Pay Now Buttons Present',
        result: payNowButtons.length > 0,
        expected: true
      },
      {
        name: 'Button Text Correct',
        result: payNowButtons.length > 0 ? payNowButtons[0].textContent.includes('Pay Now') : false,
        expected: true
      },
      {
        name: 'Button Styling Applied',
        result: payNowButtons.length > 0 ? payNowButtons[0].classList.contains('bg-red-600') : false,
        expected: true
      }
    ];

    tests.forEach(test => {
      this.testResults.push({
        category: 'Button Visibility',
        name: test.name,
        passed: test.result === test.expected,
        result: test.result,
        expected: test.expected
      });
      this.log(`${test.name}: ${test.result === test.expected ? 'PASS' : 'FAIL'}`, 
               test.result === test.expected ? 'success' : 'error');
    });
  }

  async testEmailHandling() {
    this.log('Testing Email Handling Logic...', 'test');
    
    const emailTestCases = [
      {
        name: 'Valid Email (Capital E)',
        customer: { ...this.mockData.customer, Email: '<EMAIL>' },
        expected: '<EMAIL>'
      },
      {
        name: 'Valid Email (lowercase e)',
        customer: { ...this.mockData.customer, Email: undefined, email: '<EMAIL>' },
        expected: '<EMAIL>'
      },
      {
        name: 'Fallback Email',
        customer: { ...this.mockData.customer, Email: undefined, email: undefined },
        expected: '<EMAIL>'
      },
      {
        name: 'Empty Email Fallback',
        customer: { ...this.mockData.customer, Email: '', email: '' },
        expected: '<EMAIL>'
      }
    ];

    emailTestCases.forEach(testCase => {
      const result = this.simulateEmailHandling(testCase.customer);
      const passed = result === testCase.expected;
      
      this.testResults.push({
        category: 'Email Handling',
        name: testCase.name,
        passed,
        result,
        expected: testCase.expected
      });
      
      this.log(`${testCase.name}: ${passed ? 'PASS' : 'FAIL'} (${result})`, 
               passed ? 'success' : 'error');
    });
  }

  simulateEmailHandling(customer) {
    let finalCustomerEmail = null;
    
    if (customer.Email && customer.Email.trim() !== '') {
      finalCustomerEmail = customer.Email.trim();
    } else if (customer['email'] && customer['email'].trim() !== '') {
      finalCustomerEmail = customer['email'].trim();
    } else {
      finalCustomerEmail = `${customer.customerId}@aquapartner.com`;
    }
    
    return finalCustomerEmail;
  }

  async testPaymentDataStructure() {
    this.log('Testing Payment Data Structure...', 'test');
    
    const paymentData = this.generatePaymentData(this.mockData.customer, this.mockData.invoice);
    
    const tests = [
      {
        name: 'Amount is Number',
        result: typeof paymentData.amount === 'number',
        expected: true
      },
      {
        name: 'Currency is INR',
        result: paymentData.currency === 'INR',
        expected: true
      },
      {
        name: 'Description Contains Invoice Number',
        result: paymentData.description.includes(this.mockData.invoice.invoiceNumber),
        expected: true
      },
      {
        name: 'Customer Email Present',
        result: !!paymentData.customer_email,
        expected: true
      },
      {
        name: 'Reference ID Format Correct',
        result: paymentData.reference_id.includes(this.mockData.customer.customerId) &&
                paymentData.reference_id.includes(this.mockData.invoice.invoiceNumber),
        expected: true
      },
      {
        name: 'Redirect URL Valid',
        result: paymentData.redirect_url.includes('/payment-success'),
        expected: true
      },
      {
        name: 'Notify User is Boolean',
        result: typeof paymentData.notify_user === 'boolean',
        expected: true
      }
    ];

    tests.forEach(test => {
      this.testResults.push({
        category: 'Payment Data',
        name: test.name,
        passed: test.result === test.expected,
        result: test.result,
        expected: test.expected
      });
      this.log(`${test.name}: ${test.result === test.expected ? 'PASS' : 'FAIL'}`, 
               test.result === test.expected ? 'success' : 'error');
    });

    this.log('Generated Payment Data:', 'info');
    console.table(paymentData);
  }

  generatePaymentData(customer, invoice) {
    const finalCustomerEmail = this.simulateEmailHandling(customer);
    
    return {
      amount: invoice.total,
      currency: 'INR',
      description: `Payment for AquaPartner Invoice ${invoice.invoiceNumber}`,
      invoice_number: invoice.invoiceNumber,
      customer_id: customer.customerId,
      customer_name: customer.customerName,
      customer_email: finalCustomerEmail,
      customer_phone: customer.mobileNumber,
      redirect_url: `${window.location.origin}/payment-success?invoice=${invoice.invoiceNumber}`,
      reference_id: `${customer.customerId}_${invoice.invoiceNumber}_${Date.now()}`,
      notify_user: false
    };
  }

  async testErrorHandling() {
    this.log('Testing Error Handling...', 'test');
    
    const errorTests = [
      {
        name: 'Missing Customer ID',
        customer: { ...this.mockData.customer, customerId: undefined },
        shouldFail: true
      },
      {
        name: 'Invalid Amount',
        invoice: { ...this.mockData.invoice, total: 'invalid' },
        shouldFail: true
      },
      {
        name: 'Missing Invoice Number',
        invoice: { ...this.mockData.invoice, invoiceNumber: undefined },
        shouldFail: false // Should use fallback
      }
    ];

    errorTests.forEach(test => {
      try {
        const customer = test.customer || this.mockData.customer;
        const invoice = test.invoice || this.mockData.invoice;
        const paymentData = this.generatePaymentData(customer, invoice);
        
        const hasError = !paymentData.customer_id || 
                        typeof paymentData.amount !== 'number' ||
                        isNaN(paymentData.amount);
        
        const passed = test.shouldFail ? hasError : !hasError;
        
        this.testResults.push({
          category: 'Error Handling',
          name: test.name,
          passed,
          result: hasError ? 'Error detected' : 'No error',
          expected: test.shouldFail ? 'Error expected' : 'No error expected'
        });
        
        this.log(`${test.name}: ${passed ? 'PASS' : 'FAIL'}`, passed ? 'success' : 'error');
      } catch (error) {
        const passed = test.shouldFail;
        this.testResults.push({
          category: 'Error Handling',
          name: test.name,
          passed,
          result: `Exception: ${error.message}`,
          expected: test.shouldFail ? 'Error expected' : 'No error expected'
        });
        this.log(`${test.name}: ${passed ? 'PASS' : 'FAIL'} (Exception caught)`, 
                 passed ? 'success' : 'error');
      }
    });
  }

  async testAPIIntegration() {
    this.log('Testing API Integration (Mock)...', 'test');
    
    // Since we can't make actual API calls in testing, we'll test the structure
    const tests = [
      {
        name: 'API Endpoint Available',
        result: window.location.origin.includes('localhost') || 
                window.location.origin.includes('aquapartner'),
        expected: true
      },
      {
        name: 'Fetch Function Available',
        result: typeof fetch !== 'undefined',
        expected: true
      },
      {
        name: 'Console Logging Working',
        result: typeof console.log !== 'undefined',
        expected: true
      }
    ];

    tests.forEach(test => {
      this.testResults.push({
        category: 'API Integration',
        name: test.name,
        passed: test.result === test.expected,
        result: test.result,
        expected: test.expected
      });
      this.log(`${test.name}: ${test.result === test.expected ? 'PASS' : 'FAIL'}`, 
               test.result === test.expected ? 'success' : 'error');
    });
  }

  displayResults() {
    this.log('Test Results Summary', 'test');
    console.log('='.repeat(50));
    
    const categories = [...new Set(this.testResults.map(r => r.category))];
    
    categories.forEach(category => {
      const categoryTests = this.testResults.filter(r => r.category === category);
      const passed = categoryTests.filter(r => r.passed).length;
      const total = categoryTests.length;
      
      this.log(`${category}: ${passed}/${total} tests passed`, 
               passed === total ? 'success' : 'warning');
      
      categoryTests.forEach(test => {
        console.log(`  ${test.passed ? '✅' : '❌'} ${test.name}`);
        if (!test.passed) {
          console.log(`    Expected: ${test.expected}, Got: ${test.result}`);
        }
      });
    });
    
    const totalPassed = this.testResults.filter(r => r.passed).length;
    const totalTests = this.testResults.length;
    const percentage = Math.round((totalPassed / totalTests) * 100);
    
    console.log('='.repeat(50));
    this.log(`Overall: ${totalPassed}/${totalTests} tests passed (${percentage}%)`, 
             percentage >= 80 ? 'success' : 'warning');
    
    if (percentage < 100) {
      this.log('Some tests failed. Check the details above for issues.', 'warning');
    } else {
      this.log('All tests passed! Pay Now button is working correctly.', 'success');
    }
  }

  // Manual test helper
  async simulateButtonClick() {
    this.log('Simulating Pay Now button click...', 'test');
    
    const payNowButtons = document.querySelectorAll('button[title*="Pay this overdue invoice"]');
    
    if (payNowButtons.length === 0) {
      this.log('No Pay Now buttons found. Make sure there are overdue invoices.', 'warning');
      return;
    }
    
    const button = payNowButtons[0];
    this.log(`Found button: ${button.textContent.trim()}`, 'info');
    
    // Check if button is already disabled
    if (button.disabled) {
      this.log('Button is already disabled (payment in progress?)', 'warning');
      return;
    }
    
    this.log('Button is ready for testing. Click it manually to test the full flow.', 'info');
    this.log('Watch the console for payment flow logs.', 'info');
    
    // Highlight the button for easy identification
    button.style.border = '3px solid yellow';
    button.style.boxShadow = '0 0 10px yellow';
    
    setTimeout(() => {
      button.style.border = '';
      button.style.boxShadow = '';
    }, 5000);
  }
}

// Initialize and run tests
const tester = new PayNowButtonTester();

// Expose functions to global scope for manual testing
window.payNowTester = tester;
window.testPayNowButton = () => tester.runAllTests();
window.simulatePayNowClick = () => tester.simulateButtonClick();

// Auto-run tests
console.log('🧪 Pay Now Button Tester Loaded!');
console.log('📋 Available commands:');
console.log('  - testPayNowButton() - Run all automated tests');
console.log('  - simulatePayNowClick() - Highlight and prepare for manual testing');
console.log('  - payNowTester.displayResults() - Show last test results');
console.log('');
console.log('🚀 Running automated tests now...');
tester.runAllTests();
