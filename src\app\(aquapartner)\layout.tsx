import Auth<PERSON>hecker from '@/customComponents/AuthChecker'
import { Providers } from '@/customComponents/providers'
import '@/styles/tailwind.css'
import type { Metadata } from 'next'
import Script from 'next/script'

import type React from 'react'
import { SnackbarProvider } from '../context/SnackbarContext'
import { ApplicationLayout } from './application-layout'
export const metadata: Metadata = {
  title: {
    template: 'Aquapartner',
    default: 'Aquapartner App',
  },
  description: 'One stop solution for Aqua culture needs.',
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html
      lang="en"
      className="text-zinc-950 antialiased dark:bg-zinc-900 dark:text-white lg:bg-zinc-100 dark:lg:bg-zinc-950"
    >
      <head>
        <link rel="preconnect" href="https://rsms.me/" />
        <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />
        <link rel="icon" href="/favicon.png" />
        {/* Use Script for external script loading */}
        <Script
          id="clarity-script"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              (function(c,l,a,r,i,t,y){
                  c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);})(window, document, "clarity", "script", "oo075y2b8j");`,
          }}
        />
        <Script
          id="gt-script"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);})(window,document,'script','dataLayer','GTM-TK7WNRCM');`,
          }}
        />
        {/* Zoho Payments Script - Load early like POC */}
        <Script
          src="https://static.zohocdn.com/zpay/zpay-js/v1/zpayments.js"
          strategy="beforeInteractive"
          id="zoho-payments-script"
        />
      </head>
      <body className="">
        <SnackbarProvider>
          <Providers>
            <AuthChecker>
              <ApplicationLayout>{children}</ApplicationLayout>
            </AuthChecker>
          </Providers>
        </SnackbarProvider>
        <noscript>
          <iframe
            src="https://www.googletagmanager.com/ns.html?id=GTM-TK7WNRCM"
            height="0"
            width="0"
            className="invisible hidden"
          ></iframe>
        </noscript>
      </body>
    </html>
  )
}
