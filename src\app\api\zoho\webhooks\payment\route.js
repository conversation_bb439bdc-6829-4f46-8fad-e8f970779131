import zohoPaymentService from '@/app/lib/zohoPaymentService'
import connectedDB from '@/app/config/database'
import WebhookEvent from '@/app/models/WebhookEvent'
import WebhookMonitor from '@/app/lib/webhookMonitor'
import crypto from 'crypto'

/**
 * POST /api/zoho/webhooks/payment
 * Handle payment webhooks from Zoho Payments with comprehensive monitoring
 */
export async function POST(request) {
  const monitor = new WebhookMonitor()
  let requestData = null
  let webhookData = null
  let responseData = null
  let processingError = null

  try {
    const body = await request.text()
    const signature = request.headers.get('x-zoho-webhook-signature')

    // Log incoming request details
    requestData = monitor.logIncomingRequest(request, body, signature)

    // Verify webhook signature if secret is configured
    let signatureValid = true
    if (process.env.ZOHO_WEBHOOK_SECRET) {
      if (!signature) {
        monitor.logSignatureVerification(false, signature, true)
        responseData = monitor.logResponse(401, { error: 'Missing webhook signature' })
        return new Response(
          JSON.stringify({
            error: 'Missing webhook signature',
            message: 'Webhook signature is required for verification',
          }),
          { status: 401 }
        )
      }

      const expectedSignature = crypto.createHmac('sha256', process.env.ZOHO_WEBHOOK_SECRET).update(body).digest('hex')
      signatureValid = signature === expectedSignature

      if (!signatureValid) {
        monitor.logSignatureVerification(false, signature, true)
        responseData = monitor.logResponse(401, { error: 'Invalid webhook signature' })
        return new Response(
          JSON.stringify({
            error: 'Invalid webhook signature',
            message: 'Webhook signature verification failed',
          }),
          { status: 401 }
        )
      }
    }

    // Log successful signature verification
    monitor.logSignatureVerification(signatureValid, signature, !!process.env.ZOHO_WEBHOOK_SECRET)

    webhookData = JSON.parse(body)

    // Log parsed webhook data
    monitor.logWebhookData(webhookData)

    // Extract event data - support both payment session and payment link webhooks
    const {
      event_type,
      payment_session_id,
      payment_link_id,
      payment_id,
      status,
      amount,
      currency,
      payment_method,
      created_time,
      error_code,
      error_message,
    } = webhookData

    // Validate required fields based on webhook type
    if (!event_type) {
      responseData = monitor.logResponse(400, { error: 'Invalid webhook data - missing event_type' })
      await monitor.saveWebhookLog(requestData, webhookData, responseData, new Error('Missing event_type'))
      return new Response(
        JSON.stringify({
          error: 'Invalid webhook data',
          message: 'event_type is required',
        }),
        { status: 400 }
      )
    }

    // Determine if this is a payment link or payment session webhook
    const isPaymentLinkWebhook = event_type.startsWith('payment_link.')
    const identifier = isPaymentLinkWebhook ? payment_link_id : payment_session_id

    if (!identifier) {
      const errorMessage = isPaymentLinkWebhook
        ? 'payment_link_id is required for payment link webhooks'
        : 'payment_session_id is required for payment session webhooks'

      responseData = monitor.logResponse(400, { error: 'Invalid webhook data - missing identifier' })
      await monitor.saveWebhookLog(requestData, webhookData, responseData, new Error(errorMessage))
      return new Response(
        JSON.stringify({
          error: 'Invalid webhook data',
          message: errorMessage,
        }),
        { status: 400 }
      )
    }

    // Get the transaction based on webhook type
    let transaction
    try {
      if (isPaymentLinkWebhook) {
        transaction = await zohoPaymentService.getTransactionByPaymentLinkId(payment_link_id)
      } else {
        transaction = await zohoPaymentService.getTransaction(payment_session_id)
      }
    } catch (error) {
      console.error('Error fetching transaction:', error.message)
    }

    // Log transaction lookup result
    monitor.logTransactionLookup(transaction, identifier, isPaymentLinkWebhook)

    if (!transaction) {
      const errorMessage = `No transaction found for the provided ${isPaymentLinkWebhook ? 'payment link ID' : 'payment session ID'}`
      responseData = monitor.logResponse(404, { error: 'Transaction not found' })
      await monitor.saveWebhookLog(requestData, webhookData, responseData, new Error(errorMessage))
      return new Response(
        JSON.stringify({
          error: 'Transaction not found',
          message: errorMessage,
        }),
        { status: 404 }
      )
    }

    // Add webhook event to transaction
    await transaction.addWebhookEvent(event_type, webhookData)

    // Save webhook event to WebhookEvent collection for tracking
    await connectedDB()
    const webhookEvent = new WebhookEvent({
      event_id: webhookData.event_id || `${event_type}_${Date.now()}`,
      event_type,
      payment_session_id: isPaymentLinkWebhook ? undefined : payment_session_id,
      payment_link_id: isPaymentLinkWebhook ? payment_link_id : undefined,
      payment_id,
      amount: parseFloat(amount) || 0,
      currency: currency || 'INR',
      customer_id: transaction.customer_id,
      customer_email: transaction.customer_email,
      transaction_id: transaction._id.toString(),
      invoice_number: transaction.invoice_number,
      reference_id: transaction.reference_id,
      raw_data: webhookData,
      signature_verified: !!process.env.ZOHO_WEBHOOK_SECRET,
      processed: true,
      processed_at: new Date(),
    })

    await webhookEvent.save()

    // Update transaction status based on event type
    let statusData = {}

    switch (event_type) {
      // Payment Session Events
      case 'payment.succeeded':
        statusData = {
          status: 'succeeded',
          payment_id,
          payment_method,
        }
        break

      case 'payment.failed':
        statusData = {
          status: 'failed',
          error_code,
          error_message,
        }
        break

      case 'payment.pending':
        statusData = {
          status: 'pending',
          payment_id,
        }
        break

      case 'payment.cancelled':
        statusData = {
          status: 'cancelled',
        }
        break

      case 'payment_session.expired':
        statusData = {
          status: 'expired',
        }
        break

      // Payment Link Events
      case 'payment_link.paid':
        statusData = {
          status: 'succeeded',
          payment_id,
          payment_method,
        }
        break

      case 'payment_link.cancelled':
        statusData = {
          status: 'cancelled',
        }
        break

      case 'payment_link.expired':
        statusData = {
          status: 'expired',
        }
        break

      default:
        console.log(`Unhandled event type: ${event_type}`)
        return new Response(
          JSON.stringify({
            success: true,
            message: 'Webhook received but not processed',
            event_type,
          }),
          { status: 200 }
        )
    }

    // Update transaction status
    let statusUpdateSuccess = false
    if (Object.keys(statusData).length > 0) {
      try {
        if (isPaymentLinkWebhook) {
          await zohoPaymentService.updateTransactionStatusByPaymentLinkId(payment_link_id, statusData)
        } else {
          await zohoPaymentService.updateTransactionStatus(payment_session_id, statusData)
        }
        statusUpdateSuccess = true
        monitor.logStatusUpdate(event_type, statusData, true)
      } catch (updateError) {
        monitor.logStatusUpdate(event_type, statusData, false)
        throw updateError
      }
    }

    // Prepare success response data
    const successResponseData = {
      success: true,
      message: 'Webhook processed successfully',
      data: {
        event_type,
        payment_session_id: isPaymentLinkWebhook ? undefined : payment_session_id,
        payment_link_id: isPaymentLinkWebhook ? payment_link_id : undefined,
        status: statusData.status,
        processed_at: new Date().toISOString(),
        transaction_id: transaction._id.toString(),
      },
    }

    // Log successful response
    responseData = monitor.logResponse(200, successResponseData)

    // Save webhook log to database
    await monitor.saveWebhookLog(requestData, webhookData, responseData)

    // Log processing summary
    monitor.logProcessingSummary(event_type, true, responseData.processingTime)

    // Send success response to Zoho
    return new Response(JSON.stringify(successResponseData), { status: 200 })
  } catch (error) {
    processingError = error

    // Log error response
    responseData = monitor.logResponse(500, { error: 'Internal server error' }, error)

    // Save webhook log with error details
    await monitor.saveWebhookLog(requestData, webhookData, responseData, error)

    // Log processing summary
    monitor.logProcessingSummary(webhookData?.event_type || 'unknown', false, responseData?.processingTime || 0)

    // Return success to prevent Zoho from retrying, but log the error
    return new Response(
      JSON.stringify({
        success: false,
        message: 'Webhook processing failed',
        error: error.message,
        processed_at: new Date().toISOString(),
      }),
      { status: 200 } // Return 200 to prevent retries
    )
  }
}

/**
 * GET /api/zoho/webhooks/payment
 * Get webhook configuration information
 */
export async function GET() {
  const webhookInfo = {
    message: 'Zoho Payment Webhook Endpoint',
    endpoint: '/api/zoho/webhooks/payment',
    method: 'POST',
    supported_events: [
      // Payment Session Events
      {
        event: 'payment.succeeded',
        description: 'Payment completed successfully',
      },
      {
        event: 'payment.failed',
        description: 'Payment failed',
      },
      {
        event: 'payment.pending',
        description: 'Payment is pending',
      },
      {
        event: 'payment.cancelled',
        description: 'Payment was cancelled',
      },
      {
        event: 'payment_session.expired',
        description: 'Payment session expired',
      },
      // Payment Link Events
      {
        event: 'payment_link.paid',
        description: 'Payment link was successfully paid',
      },
      {
        event: 'payment_link.cancelled',
        description: 'Payment link was cancelled',
      },
      {
        event: 'payment_link.expired',
        description: 'Payment link expired without payment',
      },
    ],
    configuration: {
      webhook_url: `${process.env.NEXT_PUBLIC_DOMAIN}/api/zoho/webhooks/payment`,
      signature_verification: !!process.env.ZOHO_WEBHOOK_SECRET,
      content_type: 'application/json',
    },
    setup_instructions: [
      {
        step: 1,
        title: 'Configure Webhook URL in Zoho Payments',
        description: 'Add the webhook URL in your Zoho Payments dashboard',
      },
      {
        step: 2,
        title: 'Set Webhook Secret (Optional)',
        description: 'Set ZOHO_WEBHOOK_SECRET environment variable for signature verification',
      },
      {
        step: 3,
        title: 'Test Webhook',
        description: 'Make a test payment to verify webhook is working',
      },
    ],
  }

  return new Response(JSON.stringify(webhookInfo), { status: 200 })
}
