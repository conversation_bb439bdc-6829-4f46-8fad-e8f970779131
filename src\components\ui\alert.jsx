import React from 'react'

export const Alert = ({ children, className = '', variant = 'default', ...props }) => {
  const baseClasses = 'relative w-full rounded-lg border p-4'
  
  const variants = {
    default: 'bg-white border-gray-200 text-gray-900',
    destructive: 'bg-red-50 border-red-200 text-red-900'
  }

  const variantClasses = variants[variant] || variants.default

  return (
    <div 
      className={`${baseClasses} ${variantClasses} ${className}`}
      {...props}
    >
      {children}
    </div>
  )
}

export const AlertDescription = ({ children, className = '', ...props }) => {
  return (
    <div 
      className={`text-sm ${className}`}
      {...props}
    >
      {children}
    </div>
  )
}
