'use client'

import { useRouter, useSearchParams } from 'next/navigation'
import { Suspense, useEffect, useState } from 'react'

function PaymentSuccessPageContent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [paymentDetails, setPaymentDetails] = useState(null)

  const paymentId = searchParams.get('payment_id')
  const invoiceNumber = searchParams.get('invoice')

  useEffect(() => {
    // Auto-redirect after 10 seconds
    const timer = setTimeout(() => {
      router.push('/aquapartner/billingAndPayments')
    }, 10000)

    setLoading(false)

    return () => clearTimeout(timer)
  }, [router])

  const handleBackToInvoices = () => {
    router.push('/aquapartner/billingAndPayments')
  }

  const handleDownloadReceipt = () => {
    // TODO: Implement receipt download functionality
    console.log('Download receipt for payment:', paymentId)
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <div className="rounded-lg bg-white p-8 shadow-lg">
          {/* Success Icon */}
          <div className="text-center">
            <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
              <svg className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>

            <h2 className="mt-6 text-2xl font-bold text-gray-900">Payment Successful!</h2>
            <p className="mt-2 text-sm text-gray-600">Your payment has been processed successfully.</p>
          </div>

          {/* Payment Details */}
          <div className="mt-8 border-t border-gray-200 pt-6">
            <dl className="space-y-4">
              {paymentId && (
                <div className="flex justify-between">
                  <dt className="text-sm font-medium text-gray-500">Payment ID</dt>
                  <dd className="font-mono text-sm text-gray-900">{paymentId}</dd>
                </div>
              )}

              {invoiceNumber && (
                <div className="flex justify-between">
                  <dt className="text-sm font-medium text-gray-500">Invoice Number</dt>
                  <dd className="text-sm text-gray-900">{invoiceNumber}</dd>
                </div>
              )}

              <div className="flex justify-between">
                <dt className="text-sm font-medium text-gray-500">Payment Date</dt>
                <dd className="text-sm text-gray-900">{new Date().toLocaleDateString()}</dd>
              </div>

              <div className="flex justify-between">
                <dt className="text-sm font-medium text-gray-500">Status</dt>
                <dd className="text-sm">
                  <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                    Completed
                  </span>
                </dd>
              </div>
            </dl>
          </div>

          {/* Action Buttons */}
          <div className="mt-8 space-y-3">
            <button
              onClick={handleDownloadReceipt}
              className="flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Download Receipt
            </button>

            <button
              onClick={handleBackToInvoices}
              className="flex w-full justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Back to Invoices
            </button>
          </div>

          {/* Auto-redirect Notice */}
          <div className="mt-6 text-center">
            <p className="text-xs text-gray-500">You will be automatically redirected to invoices in 10 seconds</p>
          </div>
        </div>

        {/* Additional Information */}
        <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">What&apos;s Next?</h3>
              <div className="mt-2 text-sm text-blue-700">
                <ul className="list-inside list-disc space-y-1">
                  <li>Your invoice status will be updated shortly</li>
                  <li>A payment confirmation email will be sent to you</li>
                  <li>You can view your payment history in the billing section</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function PaymentSuccessPage() {
  return (
    <Suspense
      fallback={
        <div className="flex min-h-screen items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="mx-auto h-12 w-12 animate-spin rounded-full border-b-2 border-green-600"></div>
            <p className="mt-4 text-gray-600">Loading...</p>
          </div>
        </div>
      }
    >
      <PaymentSuccessPageContent />
    </Suspense>
  )
}
