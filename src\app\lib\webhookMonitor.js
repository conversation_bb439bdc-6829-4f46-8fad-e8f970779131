/**
 * Webhook Traffic Monitoring and Logging Utility
 * Provides comprehensive monitoring for Zoho Payment webhook traffic
 */

import connectedDB from '@/app/config/database'
import WebhookEvent from '@/app/models/WebhookEvent'

class WebhookMonitor {
  constructor() {
    this.requestStartTime = null
    this.requestId = null
  }

  /**
   * Generate unique request ID for tracking
   */
  generateRequestId() {
    return `webhook_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Extract client IP address from request
   */
  getClientIP(request) {
    const forwarded = request.headers.get('x-forwarded-for')
    const realIP = request.headers.get('x-real-ip')
    const remoteAddr = request.headers.get('x-remote-addr')
    
    if (forwarded) {
      return forwarded.split(',')[0].trim()
    }
    if (realIP) {
      return realIP
    }
    if (remoteAddr) {
      return remoteAddr
    }
    return 'unknown'
  }

  /**
   * Log incoming webhook request details
   */
  logIncomingRequest(request, body, signature) {
    this.requestId = this.generateRequestId()
    this.requestStartTime = Date.now()
    
    const clientIP = this.getClientIP(request)
    const timestamp = new Date().toISOString()
    
    // Extract relevant headers
    const headers = {
      'content-type': request.headers.get('content-type'),
      'content-length': request.headers.get('content-length'),
      'user-agent': request.headers.get('user-agent'),
      'x-zoho-webhook-signature': signature ? '***PRESENT***' : 'MISSING',
      'x-forwarded-for': request.headers.get('x-forwarded-for'),
      'x-real-ip': request.headers.get('x-real-ip'),
    }

    console.log('\n🔔 ===== WEBHOOK REQUEST RECEIVED =====')
    console.log(`📋 Request ID: ${this.requestId}`)
    console.log(`⏰ Timestamp: ${timestamp}`)
    console.log(`🌐 Client IP: ${clientIP}`)
    console.log(`📊 Content Length: ${body.length} bytes`)
    console.log(`🔐 Signature: ${signature ? 'Present' : 'Missing'}`)
    console.log(`📋 Headers:`, JSON.stringify(headers, null, 2))
    console.log(`📦 Body Preview: ${body.substring(0, 200)}${body.length > 200 ? '...' : ''}`)
    console.log('=====================================\n')

    return {
      requestId: this.requestId,
      timestamp,
      clientIP,
      headers,
      bodyLength: body.length,
      signaturePresent: !!signature
    }
  }

  /**
   * Log webhook signature verification result
   */
  logSignatureVerification(isValid, signature, hasSecret) {
    const status = isValid ? '✅ VALID' : '❌ INVALID'
    const secretStatus = hasSecret ? 'Configured' : 'Not Configured'
    
    console.log(`🔐 SIGNATURE VERIFICATION: ${status}`)
    console.log(`🔑 Webhook Secret: ${secretStatus}`)
    if (signature && !isValid) {
      console.log(`⚠️  Signature Mismatch - Request may be unauthorized`)
    }
    console.log('')
  }

  /**
   * Log parsed webhook data
   */
  logWebhookData(webhookData) {
    const eventType = webhookData.event_type || 'UNKNOWN'
    const paymentId = webhookData.payment_id || 'N/A'
    const sessionId = webhookData.payment_session_id || 'N/A'
    const linkId = webhookData.payment_link_id || 'N/A'
    const amount = webhookData.amount || 'N/A'
    const status = webhookData.status || 'N/A'

    console.log('📋 ===== WEBHOOK DATA ANALYSIS =====')
    console.log(`🎯 Event Type: ${eventType}`)
    console.log(`💰 Amount: ${amount}`)
    console.log(`📊 Status: ${status}`)
    console.log(`🔗 Payment ID: ${paymentId}`)
    console.log(`🔗 Session ID: ${sessionId}`)
    console.log(`🔗 Link ID: ${linkId}`)
    console.log(`📦 Full Data:`, JSON.stringify(webhookData, null, 2))
    console.log('===================================\n')
  }

  /**
   * Log transaction lookup result
   */
  logTransactionLookup(transaction, identifier, isPaymentLink) {
    const type = isPaymentLink ? 'Payment Link' : 'Payment Session'
    
    if (transaction) {
      console.log(`✅ TRANSACTION FOUND`)
      console.log(`🔍 Type: ${type}`)
      console.log(`🆔 Identifier: ${identifier}`)
      console.log(`💾 Transaction ID: ${transaction._id}`)
      console.log(`👤 Customer: ${transaction.customer_email}`)
      console.log(`💰 Amount: ${transaction.amount} ${transaction.currency}`)
      console.log(`📊 Current Status: ${transaction.status}`)
    } else {
      console.log(`❌ TRANSACTION NOT FOUND`)
      console.log(`🔍 Type: ${type}`)
      console.log(`🆔 Identifier: ${identifier}`)
      console.log(`⚠️  This may indicate a webhook for a transaction not in our database`)
    }
    console.log('')
  }

  /**
   * Log status update operation
   */
  logStatusUpdate(eventType, statusData, success) {
    const updateStatus = success ? '✅ SUCCESS' : '❌ FAILED'
    
    console.log(`🔄 STATUS UPDATE: ${updateStatus}`)
    console.log(`🎯 Event: ${eventType}`)
    console.log(`📊 New Status: ${statusData.status || 'N/A'}`)
    console.log(`💳 Payment ID: ${statusData.payment_id || 'N/A'}`)
    console.log(`💰 Payment Method: ${statusData.payment_method || 'N/A'}`)
    if (statusData.error_code || statusData.error_message) {
      console.log(`⚠️  Error Code: ${statusData.error_code || 'N/A'}`)
      console.log(`⚠️  Error Message: ${statusData.error_message || 'N/A'}`)
    }
    console.log('')
  }

  /**
   * Log final response details
   */
  logResponse(statusCode, responseData, error = null) {
    const processingTime = this.requestStartTime ? Date.now() - this.requestStartTime : 0
    const statusEmoji = statusCode >= 200 && statusCode < 300 ? '✅' : '❌'
    
    console.log(`📤 ===== WEBHOOK RESPONSE =====`)
    console.log(`📋 Request ID: ${this.requestId}`)
    console.log(`${statusEmoji} Status Code: ${statusCode}`)
    console.log(`⏱️  Processing Time: ${processingTime}ms`)
    
    if (error) {
      console.log(`❌ Error: ${error.message}`)
      console.log(`📋 Error Stack:`, error.stack)
    }
    
    if (responseData) {
      console.log(`📦 Response Data:`, JSON.stringify(responseData, null, 2))
    }
    
    console.log(`⏰ Completed At: ${new Date().toISOString()}`)
    console.log('==============================\n')

    return {
      requestId: this.requestId,
      statusCode,
      processingTime,
      completedAt: new Date().toISOString(),
      error: error ? error.message : null
    }
  }

  /**
   * Save webhook traffic log to database for historical tracking
   */
  async saveWebhookLog(requestData, webhookData, responseData, error = null) {
    try {
      await connectedDB()
      
      const webhookLog = new WebhookEvent({
        event_id: requestData.requestId,
        event_type: webhookData?.event_type || 'unknown',
        payment_session_id: webhookData?.payment_session_id,
        payment_link_id: webhookData?.payment_link_id,
        payment_id: webhookData?.payment_id,
        amount: parseFloat(webhookData?.amount) || 0,
        currency: webhookData?.currency || 'INR',
        customer_id: webhookData?.customer_id || 'unknown',
        customer_email: webhookData?.customer_email || 'unknown',
        transaction_id: responseData?.transaction_id || null,
        invoice_number: webhookData?.invoice_number || null,
        reference_id: webhookData?.reference_id || null,
        raw_data: webhookData || {},
        signature_verified: requestData.signaturePresent,
        processed: !error,
        processed_at: new Date(),
        processing_time_ms: responseData?.processingTime || 0,
        response_status: responseData?.statusCode || 500,
        client_ip: requestData.clientIP,
        error_message: error ? error.message : null
      })

      await webhookLog.save()
      console.log(`💾 Webhook log saved to database: ${requestData.requestId}`)
      
    } catch (dbError) {
      console.error(`❌ Failed to save webhook log to database:`, dbError.message)
      // Don't throw error to avoid affecting webhook processing
    }
  }

  /**
   * Log webhook processing summary
   */
  logProcessingSummary(eventType, success, processingTime) {
    const summaryEmoji = success ? '🎉' : '💥'
    const statusText = success ? 'COMPLETED SUCCESSFULLY' : 'FAILED'
    
    console.log(`${summaryEmoji} ===== WEBHOOK PROCESSING SUMMARY =====`)
    console.log(`📋 Request ID: ${this.requestId}`)
    console.log(`🎯 Event Type: ${eventType}`)
    console.log(`📊 Status: ${statusText}`)
    console.log(`⏱️  Total Processing Time: ${processingTime}ms`)
    console.log(`⏰ Timestamp: ${new Date().toISOString()}`)
    console.log('=========================================\n')
  }
}

export default WebhookMonitor
