import zohoPaymentService from '@/app/lib/zohoPaymentService'
import connectedDB from '@/app/config/database'

// Force dynamic rendering to prevent static generation
export const dynamic = 'force-dynamic'

/**
 * POST /api/zoho/payments/create-link
 * Create a new payment link
 * Based on Zoho Payment Links API: https://www.zoho.com/in/payments/api/v1/payment-links/#create-payment-link
 */
export async function POST(request) {
  try {
    // Connect to the database
    await connectedDB()

    const body = await request.json()

    const {
      amount,
      currency = 'INR',
      description,
      invoice_number,
      customer_id,
      customer_name,
      customer_email,
      customer_phone,
      redirect_url,
      reference_id,
      meta_data = [],
      expires_at,
      send_sms = false,
      send_email = false,
      partial_payments = false,
      minimum_partial_amount,
    } = body

    // Validate required fields
    if (!amount || !description || !customer_email) {
      return new Response(
        JSON.stringify({
          error: 'Missing required fields',
          message: 'amount, description, and customer_email are required',
          required_fields: ['amount', 'description', 'customer_email'],
        }),
        { status: 400 }
      )
    }

    // Validate amount
    const numericAmount = parseFloat(amount)
    if (isNaN(numericAmount) || numericAmount <= 0) {
      return new Response(
        JSON.stringify({
          error: 'Invalid amount',
          message: 'Amount must be a positive number',
        }),
        { status: 400 }
      )
    }

    // Validate currency
    if (currency !== 'INR') {
      return new Response(
        JSON.stringify({
          error: 'Invalid currency',
          message: 'Only INR currency is supported',
        }),
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(customer_email)) {
      return new Response(
        JSON.stringify({
          error: 'Invalid email',
          message: 'Please provide a valid email address',
        }),
        { status: 400 }
      )
    }

    // Validate partial payment settings
    if (partial_payments && minimum_partial_amount) {
      const minAmount = parseFloat(minimum_partial_amount)
      if (isNaN(minAmount) || minAmount <= 0 || minAmount >= numericAmount) {
        return new Response(
          JSON.stringify({
            error: 'Invalid minimum partial amount',
            message: 'Minimum partial amount must be a positive number less than the total amount',
          }),
          { status: 400 }
        )
      }
    }

    // Validate expires_at if provided
    if (expires_at) {
      const expiryDate = new Date(expires_at * 1000)
      const now = new Date()
      if (expiryDate <= now) {
        return new Response(
          JSON.stringify({
            error: 'Invalid expiry date',
            message: 'Expiry date must be in the future',
          }),
          { status: 400 }
        )
      }
    }

    const paymentLinkData = {
      amount: numericAmount,
      currency,
      description,
      invoice_number,
      customer_id,
      customer_name,
      customer_email,
      customer_phone,
      redirect_url,
      reference_id,
      meta_data,
      expires_at,
      send_sms,
      send_email,
      partial_payments,
      minimum_partial_amount,
    }

    const result = await zohoPaymentService.createPaymentLink(paymentLinkData)

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Payment link created successfully',
        data: {
          payment_link_id: result.payment_link.payment_link_id,
          payment_link_url: result.payment_link.payment_link_url,
          amount: result.payment_link.amount,
          currency: result.payment_link.currency,
          description: result.payment_link.description,
          customer_email: result.payment_link.customer_email,
          status: result.payment_link.status,
          created_time: result.payment_link.created_time,
          expires_at: result.payment_link.expires_at,
          transaction_id: result.transaction_id,
          send_sms: result.payment_link.send_sms,
          send_email: result.payment_link.send_email,
          partial_payments: result.payment_link.partial_payments,
        },
        payment_link: result.payment_link,
      }),
      { status: 201 }
    )
  } catch (error) {
    console.error('Error creating payment link:', error.message)

    // Handle specific Zoho API errors
    if (error.message.includes('Zoho API Error')) {
      return new Response(
        JSON.stringify({
          error: 'Zoho Payment API Error',
          message: error.message,
          details: 'Please check your payment link data and try again',
        }),
        { status: 400 }
      )
    }

    // Handle token errors
    if (error.message.includes('token')) {
      return new Response(
        JSON.stringify({
          error: 'Authentication Error',
          message: error.message,
          details: 'Please check your Zoho Payment configuration',
        }),
        { status: 401 }
      )
    }

    // Handle validation errors
    if (error.message.includes('Missing required fields') || error.message.includes('Invalid')) {
      return new Response(
        JSON.stringify({
          error: 'Validation Error',
          message: error.message,
          details: 'Please check your input data and try again',
        }),
        { status: 400 }
      )
    }

    return new Response(
      JSON.stringify({
        error: 'Payment link creation failed',
        message: error.message,
        details: 'An unexpected error occurred while creating the payment link',
      }),
      { status: 500 }
    )
  }
}

/**
 * GET /api/zoho/payments/create-link
 * Get payment link creation requirements and documentation
 */
export async function GET() {
  const requirements = {
    message: 'Payment Link Creation Requirements',
    description: 'Create payment links that can be shared with customers via email, SMS, or other channels',
    api_reference: 'https://www.zoho.com/in/payments/api/v1/payment-links/#create-payment-link',
    required_fields: [
      {
        field: 'amount',
        type: 'number',
        description: 'Payment amount (must be positive)',
        example: 100.5,
      },
      {
        field: 'description',
        type: 'string',
        description: 'Payment description (max 500 characters)',
        example: 'Payment for Order #12345',
      },
      {
        field: 'customer_email',
        type: 'string',
        description: 'Customer email address (required for payment link)',
        example: '<EMAIL>',
      },
    ],
    optional_fields: [
      {
        field: 'currency',
        type: 'string',
        description: 'Payment currency (default: INR)',
        example: 'INR',
      },
      {
        field: 'invoice_number',
        type: 'string',
        description: 'Invoice number (max 50 characters)',
        example: 'INV-12345',
      },
      {
        field: 'customer_id',
        type: 'string',
        description: 'Customer identifier',
        example: 'CUST-001',
      },
      {
        field: 'customer_name',
        type: 'string',
        description: 'Customer name',
        example: 'John Doe',
      },
      {
        field: 'customer_phone',
        type: 'string',
        description: 'Customer phone number',
        example: '+919876543210',
      },
      {
        field: 'redirect_url',
        type: 'string',
        description: 'URL to redirect after payment',
        example: 'https://yourapp.com/payment/success',
      },
      {
        field: 'reference_id',
        type: 'string',
        description: 'Your internal reference ID',
        example: 'REF-12345',
      },
      {
        field: 'expires_at',
        type: 'number',
        description: 'Unix timestamp when the link expires (default: 7 days)',
        example: 1640995200,
      },
      {
        field: 'send_sms',
        type: 'boolean',
        description: 'Send payment link via SMS (default: false)',
        example: false,
      },
      {
        field: 'send_email',
        type: 'boolean',
        description: 'Send payment link via email (default: false)',
        example: true,
      },
      {
        field: 'partial_payments',
        type: 'boolean',
        description: 'Allow partial payments (default: false)',
        example: false,
      },
      {
        field: 'minimum_partial_amount',
        type: 'number',
        description: 'Minimum amount for partial payments (required if partial_payments is true)',
        example: 50.0,
      },
      {
        field: 'meta_data',
        type: 'array',
        description: 'Additional metadata (max 5 items)',
        example: [{ key: 'order_id', value: 'ORD-123' }],
      },
    ],
    example_payload: {
      amount: 100.5,
      currency: 'INR',
      description: 'Payment for Order #12345',
      customer_email: '<EMAIL>',
      invoice_number: 'INV-12345',
      customer_id: 'CUST-001',
      customer_name: 'John Doe',
      customer_phone: '+919876543210',
      redirect_url: 'https://yourapp.com/payment/success',
      reference_id: 'REF-12345',
      send_email: true,
      send_sms: false,
      partial_payments: false,
      meta_data: [
        { key: 'order_id', value: 'ORD-123' },
        { key: 'product_type', value: 'aquaculture' },
      ],
    },
    response_example: {
      success: true,
      message: 'Payment link created successfully',
      data: {
        payment_link_id: 'pl_123456789',
        payment_link_url: 'https://payments.zoho.in/checkout/pl_123456789',
        amount: 100.5,
        currency: 'INR',
        description: 'Payment for Order #12345',
        customer_email: '<EMAIL>',
        status: 'active',
        created_time: 1640995200,
        expires_at: 1641600000,
        send_sms: false,
        send_email: true,
        partial_payments: false,
      },
    },
    features: [
      'Create shareable payment links',
      'Email and SMS notifications',
      'Partial payment support',
      'Custom expiry dates',
      'Redirect URL support',
      'Metadata tracking',
      'Integration with existing payment workflow',
    ],
  }

  return new Response(JSON.stringify(requirements), { status: 200 })
}
