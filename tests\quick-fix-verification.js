/**
 * Quick Fix Verification Script
 *
 * Tests specific issues found in production readiness analysis
 */

const fetch = globalThis.fetch || require('node-fetch')

const PRODUCTION_URL = 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net'
const LOCAL_URL = 'http://localhost:3000'

async function testPaymentCreationRouting() {
  console.log('🔧 Testing Payment Creation Routing Fix')
  console.log('======================================')

  const testPayload = {
    amount: 100.0,
    currency: 'INR',
    description: 'Fix Verification Test',
    invoice_number: `FIX_TEST_${Date.now()}`,
    customer_id: 'fix_test_customer',
    customer_name: 'Fix Test Customer',
    customer_email: '<EMAIL>',
    customer_phone: '+91-**********',
  }

  for (const [env, baseUrl] of [
    ['Local', LOCAL_URL],
    ['Production', PRODUCTION_URL],
  ]) {
    console.log(`\n--- ${env} Environment ---`)

    try {
      const response = await fetch(`${baseUrl}/api/zoho/payments/create-session`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testPayload),
      })

      console.log(`Status: ${response.status}`)

      const data = await response.json()

      if (data.success && data.data?.payment_session_id) {
        console.log(`✅ Payment creation working - Session ID: ${data.data.payment_session_id}`)
        return data.data.payment_session_id
      } else if (data.message?.includes('Requirements')) {
        console.log(`❌ Still returning documentation instead of creating payment`)
      } else {
        console.log(`❌ Unexpected response:`, data)
      }
    } catch (error) {
      console.log(`❌ Error: ${error.message}`)
    }
  }
}

async function testInputValidation() {
  console.log('\n🔧 Testing Input Validation Fix')
  console.log('==============================')

  const invalidPayload = { amount: -100, currency: 'INVALID' }

  for (const [env, baseUrl] of [
    ['Local', LOCAL_URL],
    ['Production', PRODUCTION_URL],
  ]) {
    console.log(`\n--- ${env} Environment ---`)

    try {
      const response = await fetch(`${baseUrl}/api/zoho/payments/create-session`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invalidPayload),
      })

      console.log(`Status: ${response.status}`)

      if (response.status === 400) {
        console.log(`✅ Input validation working - correctly rejected invalid data`)
      } else {
        console.log(`❌ Input validation not working - should return 400, got ${response.status}`)
      }
    } catch (error) {
      console.log(`❌ Error: ${error.message}`)
    }
  }
}

async function testPaymentStatusEndpoint() {
  console.log('\n🔧 Testing Payment Status Endpoint Fix')
  console.log('=====================================')

  // First try to create a payment to get a valid session ID
  const sessionId = await testPaymentCreationRouting()

  if (sessionId) {
    console.log(`\nTesting status retrieval for session: ${sessionId}`)

    for (const [env, baseUrl] of [
      ['Local', LOCAL_URL],
      ['Production', PRODUCTION_URL],
    ]) {
      console.log(`\n--- ${env} Environment ---`)

      try {
        const response = await fetch(`${baseUrl}/api/zoho/payments/status/${sessionId}`)
        console.log(`Status: ${response.status}`)

        if (response.ok) {
          const data = await response.json()
          if (data.success && data.payment_session) {
            console.log(`✅ Payment status endpoint working`)
          } else {
            console.log(`❌ Invalid response format:`, data)
          }
        } else {
          console.log(`❌ Status endpoint error: ${response.status}`)
        }
      } catch (error) {
        console.log(`❌ Error: ${error.message}`)
      }
    }
  } else {
    console.log('❌ Cannot test status endpoint - no valid session ID available')
  }
}

async function testEndToEndFlow() {
  console.log('\n🔧 Testing End-to-End Payment Flow')
  console.log('=================================')

  const testPayload = {
    amount: 50.0,
    currency: 'INR',
    description: 'End-to-End Test Payment',
    invoice_number: `E2E_${Date.now()}`,
    customer_id: 'e2e_test_customer',
    customer_name: 'E2E Test Customer',
    customer_email: '<EMAIL>',
    customer_phone: '+91-**********',
  }

  for (const [env, baseUrl] of [
    ['Local', LOCAL_URL],
    ['Production', PRODUCTION_URL],
  ]) {
    console.log(`\n--- ${env} Environment ---`)

    try {
      // Step 1: Create payment session
      console.log('Step 1: Creating payment session...')
      const createResponse = await fetch(`${baseUrl}/api/zoho/payments/create-session`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testPayload),
      })

      if (!createResponse.ok) {
        console.log(`❌ Step 1 failed: ${createResponse.status}`)
        continue
      }

      const createData = await createResponse.json()
      if (!createData.success || !createData.data?.payment_session_id) {
        console.log(`❌ Step 1 failed: Invalid response`)
        continue
      }

      const sessionId = createData.data.payment_session_id
      console.log(`✅ Step 1 passed: Session created ${sessionId}`)

      // Step 2: Check payment status
      console.log('Step 2: Checking payment status...')
      const statusResponse = await fetch(`${baseUrl}/api/zoho/payments/status/${sessionId}`)

      if (!statusResponse.ok) {
        console.log(`❌ Step 2 failed: ${statusResponse.status}`)
        continue
      }

      const statusData = await statusResponse.json()
      if (!statusData.success) {
        console.log(`❌ Step 2 failed: Invalid status response`)
        continue
      }

      console.log(`✅ Step 2 passed: Status retrieved`)

      // Step 3: List transactions
      console.log('Step 3: Listing transactions...')
      const listResponse = await fetch(
        `${baseUrl}/api/zoho/payments/list?customer_id=${testPayload.customer_id}&limit=1`
      )

      if (!listResponse.ok) {
        console.log(`❌ Step 3 failed: ${listResponse.status}`)
        continue
      }

      const listData = await listResponse.json()
      if (!listData.success || !Array.isArray(listData.transactions)) {
        console.log(`❌ Step 3 failed: Invalid list response`)
        continue
      }

      console.log(`✅ Step 3 passed: Transactions listed (${listData.transactions.length} found)`)
      console.log(`🎉 End-to-end flow completed successfully in ${env}!`)
    } catch (error) {
      console.log(`❌ End-to-end flow failed: ${error.message}`)
    }
  }
}

async function runQuickFixVerification() {
  console.log('🚀 Quick Fix Verification Suite')
  console.log('===============================')
  console.log('Testing critical fixes for production readiness\n')

  await testPaymentCreationRouting()
  await testInputValidation()
  await testPaymentStatusEndpoint()
  await testEndToEndFlow()

  console.log('\n📋 Fix Verification Complete')
  console.log('============================')
  console.log('Check the results above to verify if the critical issues have been resolved.')
  console.log('All tests should show ✅ for production readiness.')
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runQuickFixVerification,
    testPaymentCreationRouting,
    testInputValidation,
    testPaymentStatusEndpoint,
    testEndToEndFlow,
  }
}

// Run verification if this file is executed directly
if (typeof require !== 'undefined' && require.main === module) {
  runQuickFixVerification().catch(console.error)
}
