// Zoho Payment Integration Utility

export interface ZohoPaymentSession {
  session_id: string; // This will be mapped from payments_session_id
  session_url: string;
  payment_session_id: string;
  currency?: string;
  amount?: string;
  created_time?: number;
  meta_data?: Array<{
    key: string;
    value: string;
  }>;
}

export interface CreatePaymentSessionRequest {
  amount: number;
  currency_code: string;
  customer_id: string;
  invoice_number: string;
  description?: string;
}

export async function createZohoPaymentSession(
  paymentData: CreatePaymentSessionRequest
): Promise<ZohoPaymentSession> {
  console.log("📡 API REQUEST: Creating payment session with data:", paymentData);

  // Fix: Use the correct API endpoint path
  const response = await fetch('/api/zoho/payments/create-session', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(paymentData),
  });

  console.log("📡 API RESPONSE: Status:", response.status, response.statusText);

  if (!response.ok) {
    const errorText = await response.text();
    console.error("❌ API ERROR: Response not ok:", errorText);
    throw new Error(`Failed to create payment session: ${response.statusText}`);
  }

  const sessionData = await response.json();
  console.log("✅ API SUCCESS: Payment session data:", sessionData);

  // Fix: Map the response structure to match expected format
  return {
    session_id: sessionData.data?.payment_session_id || sessionData.payment_session?.payments_session_id,
    session_url: `https://payments.zoho.in/checkout/${sessionData.data?.payment_session_id || sessionData.payment_session?.payments_session_id}`,
    payment_session_id: sessionData.data?.payment_session_id || sessionData.payment_session?.payments_session_id,
    currency: sessionData.data?.currency || sessionData.payment_session?.currency,
    amount: sessionData.data?.amount || sessionData.payment_session?.amount,
    created_time: sessionData.data?.created_time || sessionData.payment_session?.created_time,
    meta_data: sessionData.payment_session?.meta_data || []
  };
}

export function initializeZohoWidget(sessionId: string, amount: number, invoiceNumber: string) {
  console.log('🚀 WIDGET INIT: Starting Zoho widget initialization (POC-style)');
  console.log('🔑 SESSION ID:', sessionId);
  console.log('💰 AMOUNT:', amount);
  console.log('📄 INVOICE:', invoiceNumber);

  // Validate session ID format
  if (!sessionId || sessionId.trim() === '' || !/^\d+$/.test(sessionId)) {
    console.error('❌ WIDGET ERROR: Invalid session ID format:', sessionId);
    alert('Invalid payment session ID. Please try again.');
    return;
  }

  // Check if ZPayments is available (should be loaded via layout script tag)
  if (!(window as any).ZPayments) {
    console.error('❌ WIDGET ERROR: ZPayments not available. Trying fallback...');
    // Give it a moment for the script to load, then try fallback
    setTimeout(() => {
      if (!(window as any).ZPayments) {
        console.error('❌ WIDGET ERROR: ZPayments still not available after wait. Using fallback URL.');
        fallbackToDirectURL(sessionId);
      } else {
        initializeWidgetDirectly(sessionId, amount, invoiceNumber);
      }
    }, 1000);
    return;
  }

  // Direct initialization like the working POC
  initializeWidgetDirectly(sessionId, amount, invoiceNumber);
}

function loadZohoScriptAndInitialize(sessionId: string, amount: number, invoiceNumber: string) {
  console.log('📥 SCRIPT: Loading ZPayments script...');
  
  // Load script and then initialize
  loadZohoScript()
    .then(() => {
      console.log('✅ SCRIPT: Loaded successfully, initializing widget');
      initializeWidgetDirectly(sessionId, amount, invoiceNumber);
    })
    .catch((error) => {
      console.error('❌ SCRIPT: Failed to load, using fallback', error);
      fallbackToDirectURL(sessionId);
    });
}

function initializeWidgetDirectly(sessionId: string, amount: number, invoiceNumber: string) {
  console.log('🏗️ WIDGET: Direct initialization (POC approach)');

  try {
    // Configuration exactly like the working POC - use hardcoded values that work
    const config = {
      account_id: "***********", // Use the exact values from POC
      domain: "IN",
      otherOptions: {
        api_key: "1003.f35b9411653295bb03db1e8490dc6cdd.0f625d54ec97f4eba7bedd0dc6fc23b8" // Use the exact values from POC
      }
    };

    console.log('🔧 CONFIG:', JSON.stringify(config, null, 2));

    // Create instance exactly like POC
    const instance = new (window as any).ZPayments(config);
    console.log('✅ INSTANCE: Created successfully');

    // Payment options exactly like POC - match the structure exactly
    const options = {
      amount: amount.toString(),
      currency_code: "INR",
      payments_session_id: sessionId,
      currency_symbol: "₹",
      business: "AquaTest", // Use exact business name from POC
      description: `Purchase test product`, // Use exact description from POC
      address: {
        name: "Venkat", // Use exact name from POC
        email: "<EMAIL>" // Use exact email from POC
      }
    };

    console.log('🔧 OPTIONS:', JSON.stringify(options, null, 2));

    // Call requestPaymentMethod exactly like POC with proper error handling
    instance.requestPaymentMethod(options)
      .then((data: any) => {
        console.log('✅ PAYMENT SUCCESS:', JSON.stringify(data));
        alert(`Payment Successful!\nTransaction: ${data.payment_id || 'N/A'}`);
      })
      .catch((error: any) => {
        console.log('❌ PAYMENT ERROR:', JSON.stringify(error));
        // Fix: Use exact error handling from POC
        if (error.code !== "widget_closed") {
          console.error("Widget Error:", error.message || 'Unknown error');
          alert(`Payment Failed: ${error.message || 'Unknown error'}`);
        } else {
          console.error("Widget Closed");
        }
      })
      .finally(async () => {
        // Close instance like POC
        try {
          await instance.close();
          console.log('✅ INSTANCE: Closed successfully');
        } catch (closeError) {
          console.log('⚠️ INSTANCE: Close error (non-critical):', closeError);
        }
      });

  } catch (error) {
    console.error('❌ WIDGET: Direct initialization failed:', error);
    console.log('🔄 FALLBACK: Using direct URL');
    fallbackToDirectURL(sessionId);
  }
}

function loadZohoScript(): Promise<void> {
  return new Promise((resolve, reject) => {
    // Check if already loaded
    if ((window as any).ZPayments) {
      console.log('✅ SCRIPT: ZPayments already available');
      resolve();
      return;
    }

    console.log('📥 SCRIPT: Loading official Zoho Payments script');
    const script = document.createElement('script');
    script.src = 'https://static.zohocdn.com/zpay/zpay-js/v1/zpayments.js';
    script.async = true;
    
    script.onload = () => {
      console.log('✅ SCRIPT: ZPayments loaded successfully');
      resolve();
    };
    
    script.onerror = () => {
      console.error('❌ SCRIPT: Failed to load ZPayments');
      reject(new Error('Failed to load ZPayments script'));
    };
    
    document.head.appendChild(script);
  });
}

function fallbackToDirectURL(sessionId: string) {
  console.log('🔄 FALLBACK: Using direct URL strategy');
  const checkoutUrl = `https://payments.zoho.in/checkout/${sessionId}`;
  console.log('🌐 FALLBACK URL:', checkoutUrl);

  try {
    const newWindow = window.open(checkoutUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
    if (newWindow) {
      console.log('✅ FALLBACK: New window opened successfully');
      newWindow.focus();
    } else {
      console.error('❌ FALLBACK: Popup blocked');
      // Try same window navigation as last resort
      window.location.href = checkoutUrl;
    }
  } catch (error) {
    console.error('❌ FALLBACK ERROR:', error);
    alert('Unable to open payment page. Please check popup blockers.');
  }
}

// Debug function for testing
export function debugZohoIntegration() {
  console.log('🔧 ZOHO DEBUG: Integration status');
  console.log('  - ZPayments available:', !!(window as any).ZPayments);
  console.log('  - Environment variables:');
  console.log('    - NEXT_PUBLIC_ZOHO_ACCOUNT_ID:', process.env.NEXT_PUBLIC_ZOHO_ACCOUNT_ID);
  console.log('    - NEXT_PUBLIC_ZOHO_API_KEY:', process.env.NEXT_PUBLIC_ZOHO_API_KEY ? 'Set' : 'Not set');
}

// Extend Window interface for TypeScript
declare global {
  interface Window {
    ZPayments: any;
  }
}
