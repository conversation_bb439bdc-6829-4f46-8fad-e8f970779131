# Flutter Zoho Payment Integration API Documentation (Official SDK)

## ⚠️ **Migration Notice**

**This documentation has been updated for the official Zoho Payments SDK.** The backend endpoints now use the official SDK for enhanced security and compatibility.

## 📱 Overview

This documentation provides comprehensive guidance for integrating the Zoho Payment API with Flutter mobile applications using the **official Zoho SDK backend**. The API is production-ready and fully tested.

### 🔗 Base Configuration

```dart
class PaymentConfig {
  static const String baseUrl = 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net';
  static const String apiPath = '/api';

  // ⚠️ IMPORTANT: All endpoints require trailing slashes
  static String get healthEndpoint => '$baseUrl$apiPath/zoho/health/';
  static String get createSessionEndpoint => '$baseUrl$apiPath/zoho/payments/create-session/';
  static String get paymentListEndpoint => '$baseUrl$apiPath/zoho/payments/list/';
  static String get webhookEndpoint => '$baseUrl$apiPath/zoho/webhooks/payment/';
  static String get refundEndpoint => '$baseUrl$apiPath/zoho/refunds/create/';

  static String paymentStatusEndpoint(String sessionId) =>
    '$baseUrl$apiPath/zoho/payments/status/$sessionId/';
}
```

### 🔐 Authentication & Headers

No authentication headers are required for client applications. Authentication is handled server-side with Zoho OAuth tokens.

**Required Headers:**

```dart
Map<String, String> get defaultHeaders => {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
};
```

---

## 📋 API Endpoints

### 1. Health Check

**Endpoint:** `GET /api/zoho/health/`

**Purpose:** Check API health and configuration status

**Flutter Implementation:**

```dart
Future<HealthCheckResponse> checkHealth() async {
  final response = await http.get(
    Uri.parse(PaymentConfig.healthEndpoint),
    headers: PaymentConfig.defaultHeaders,
  );

  if (response.statusCode == 200) {
    return HealthCheckResponse.fromJson(jsonDecode(response.body));
  } else {
    throw PaymentException('Health check failed: ${response.statusCode}');
  }
}
```

**Response Schema:**

```dart
class HealthCheckResponse {
  final String timestamp;
  final String service;
  final String version;
  final String status;
  final Map<String, dynamic> checks;
  final HealthConfiguration configuration;

  HealthCheckResponse({
    required this.timestamp,
    required this.service,
    required this.version,
    required this.status,
    required this.checks,
    required this.configuration,
  });

  factory HealthCheckResponse.fromJson(Map<String, dynamic> json) {
    return HealthCheckResponse(
      timestamp: json['timestamp'],
      service: json['service'],
      version: json['version'],
      status: json['status'],
      checks: json['checks'],
      configuration: HealthConfiguration.fromJson(json['configuration']),
    );
  }
}

class HealthConfiguration {
  final String accountId;
  final String webhookSecret;
  final String domain;

  HealthConfiguration({
    required this.accountId,
    required this.webhookSecret,
    required this.domain,
  });

  factory HealthConfiguration.fromJson(Map<String, dynamic> json) {
    return HealthConfiguration(
      accountId: json['account_id'],
      webhookSecret: json['webhook_secret'],
      domain: json['domain'],
    );
  }
}
```

### 2. Create Payment Session

**Endpoint:** `POST /api/zoho/payments/create-session/`

**Purpose:** Create a new payment session for processing

**Request Schema:**

```dart
class PaymentRequest {
  final double amount;
  final String currency;
  final String description;
  final String invoiceNumber;
  final String customerId;
  final String? customerName;
  final String? customerEmail;
  final String? customerPhone;
  final String? redirectUrl;
  final String? referenceId;
  final List<MetaData>? metaData;

  PaymentRequest({
    required this.amount,
    this.currency = 'INR',
    required this.description,
    required this.invoiceNumber,
    required this.customerId,
    this.customerName,
    this.customerEmail,
    this.customerPhone,
    this.redirectUrl,
    this.referenceId,
    this.metaData,
  });

  Map<String, dynamic> toJson() {
    return {
      'amount': amount,
      'currency': currency,
      'description': description,
      'invoice_number': invoiceNumber,
      'customer_id': customerId,
      if (customerName != null) 'customer_name': customerName,
      if (customerEmail != null) 'customer_email': customerEmail,
      if (customerPhone != null) 'customer_phone': customerPhone,
      if (redirectUrl != null) 'redirect_url': redirectUrl,
      if (referenceId != null) 'reference_id': referenceId,
      if (metaData != null) 'meta_data': metaData!.map((e) => e.toJson()).toList(),
    };
  }
}

class MetaData {
  final String key;
  final String value;

  MetaData({required this.key, required this.value});

  Map<String, dynamic> toJson() => {'key': key, 'value': value};
}
```

**Flutter Implementation:**

```dart
Future<PaymentSessionResponse> createPaymentSession(PaymentRequest request) async {
  final response = await http.post(
    Uri.parse(PaymentConfig.createSessionEndpoint),
    headers: PaymentConfig.defaultHeaders,
    body: jsonEncode(request.toJson()),
  );

  if (response.statusCode == 201) {
    return PaymentSessionResponse.fromJson(jsonDecode(response.body));
  } else {
    final error = jsonDecode(response.body);
    throw PaymentException(error['message'] ?? 'Payment session creation failed');
  }
}
```

**Response Schema:**

```dart
class PaymentSessionResponse {
  final bool success;
  final String message;
  final PaymentSessionData data;
  final PaymentSession paymentSession;

  PaymentSessionResponse({
    required this.success,
    required this.message,
    required this.data,
    required this.paymentSession,
  });

  factory PaymentSessionResponse.fromJson(Map<String, dynamic> json) {
    return PaymentSessionResponse(
      success: json['success'],
      message: json['message'],
      data: PaymentSessionData.fromJson(json['data']),
      paymentSession: PaymentSession.fromJson(json['payment_session']),
    );
  }
}

class PaymentSessionData {
  final String paymentSessionId;
  final String amount;
  final String currency;
  final String description;
  final String invoiceNumber;
  final int createdTime;
  final String transactionId;
  final String expiresIn;

  PaymentSessionData({
    required this.paymentSessionId,
    required this.amount,
    required this.currency,
    required this.description,
    required this.invoiceNumber,
    required this.createdTime,
    required this.transactionId,
    required this.expiresIn,
  });

  factory PaymentSessionData.fromJson(Map<String, dynamic> json) {
    return PaymentSessionData(
      paymentSessionId: json['payment_session_id'],
      amount: json['amount'],
      currency: json['currency'],
      description: json['description'],
      invoiceNumber: json['invoice_number'],
      createdTime: json['created_time'],
      transactionId: json['transaction_id'],
      expiresIn: json['expires_in'],
    );
  }
}

class PaymentSession {
  final String paymentsSessionId;
  final String currency;
  final String amount;
  final String description;
  final String invoiceNumber;
  final int createdTime;
  final List<MetaData> metaData;

  PaymentSession({
    required this.paymentsSessionId,
    required this.currency,
    required this.amount,
    required this.description,
    required this.invoiceNumber,
    required this.createdTime,
    required this.metaData,
  });

  factory PaymentSession.fromJson(Map<String, dynamic> json) {
    return PaymentSession(
      paymentsSessionId: json['payments_session_id'],
      currency: json['currency'],
      amount: json['amount'],
      description: json['description'],
      invoiceNumber: json['invoice_number'],
      createdTime: json['created_time'],
      metaData: (json['meta_data'] as List)
          .map((e) => MetaData(key: e['key'], value: e['value']))
          .toList(),
    );
  }
}
```

### 3. Check Payment Status

**Endpoint:** `GET /api/zoho/payments/status/{sessionId}/`

**Purpose:** Retrieve current status of a payment session

**Flutter Implementation:**

```dart
Future<PaymentStatusResponse> getPaymentStatus(String sessionId) async {
  final response = await http.get(
    Uri.parse(PaymentConfig.paymentStatusEndpoint(sessionId)),
    headers: PaymentConfig.defaultHeaders,
  );

  if (response.statusCode == 200) {
    return PaymentStatusResponse.fromJson(jsonDecode(response.body));
  } else {
    final error = jsonDecode(response.body);
    throw PaymentException(error['message'] ?? 'Failed to get payment status');
  }
}
```

**Response Schema:**

```dart
class PaymentStatusResponse {
  final bool success;
  final String message;
  final PaymentStatusData data;

  PaymentStatusResponse({
    required this.success,
    required this.message,
    required this.data,
  });

  factory PaymentStatusResponse.fromJson(Map<String, dynamic> json) {
    return PaymentStatusResponse(
      success: json['success'],
      message: json['message'],
      data: PaymentStatusData.fromJson(json['data']),
    );
  }
}

class PaymentStatusData {
  final String transactionId;
  final String paymentSessionId;
  final String status;
  final double amount;
  final String currency;
  final String description;
  final String invoiceNumber;
  final String customerId;
  final String? customerName;
  final String? customerEmail;
  final String? paymentId;
  final String? paymentMethod;
  final DateTime? sessionCreatedTime;
  final DateTime? paymentCompletedTime;
  final DateTime? sessionExpiresAt;
  final String? errorCode;
  final String? errorMessage;

  PaymentStatusData({
    required this.transactionId,
    required this.paymentSessionId,
    required this.status,
    required this.amount,
    required this.currency,
    required this.description,
    required this.invoiceNumber,
    required this.customerId,
    this.customerName,
    this.customerEmail,
    this.paymentId,
    this.paymentMethod,
    this.sessionCreatedTime,
    this.paymentCompletedTime,
    this.sessionExpiresAt,
    this.errorCode,
    this.errorMessage,
  });

  factory PaymentStatusData.fromJson(Map<String, dynamic> json) {
    return PaymentStatusData(
      transactionId: json['transaction_id'],
      paymentSessionId: json['payment_session_id'],
      status: json['status'],
      amount: json['amount'].toDouble(),
      currency: json['currency'],
      description: json['description'],
      invoiceNumber: json['invoice_number'],
      customerId: json['customer_id'],
      customerName: json['customer_name'],
      customerEmail: json['customer_email'],
      paymentId: json['payment_id'],
      paymentMethod: json['payment_method'],
      sessionCreatedTime: json['session_created_time'] != null
          ? DateTime.parse(json['session_created_time']) : null,
      paymentCompletedTime: json['payment_completed_time'] != null
          ? DateTime.parse(json['payment_completed_time']) : null,
      sessionExpiresAt: json['session_expires_at'] != null
          ? DateTime.parse(json['session_expires_at']) : null,
      errorCode: json['error_code'],
      errorMessage: json['error_message'],
    );
  }
}
```

### 4. List Customer Payments

**Endpoint:** `GET /api/zoho/payments/list/`

**Purpose:** Retrieve list of payments for a customer with pagination

**Query Parameters:**

- `customer_id` (required): Customer identifier
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10, max: 100)
- `status` (optional): Filter by payment status
- `from_date` (optional): Start date (YYYY-MM-DD)
- `to_date` (optional): End date (YYYY-MM-DD)

**Flutter Implementation:**

```dart
Future<PaymentListResponse> getCustomerPayments({
  required String customerId,
  int page = 1,
  int limit = 10,
  String? status,
  String? fromDate,
  String? toDate,
}) async {
  final queryParams = <String, String>{
    'customer_id': customerId,
    'page': page.toString(),
    'limit': limit.toString(),
  };

  if (status != null) queryParams['status'] = status;
  if (fromDate != null) queryParams['from_date'] = fromDate;
  if (toDate != null) queryParams['to_date'] = toDate;

  final uri = Uri.parse(PaymentConfig.paymentListEndpoint).replace(
    queryParameters: queryParams,
  );

  final response = await http.get(uri, headers: PaymentConfig.defaultHeaders);

  if (response.statusCode == 200) {
    return PaymentListResponse.fromJson(jsonDecode(response.body));
  } else {
    final error = jsonDecode(response.body);
    throw PaymentException(error['message'] ?? 'Failed to get payment list');
  }
}
```

**Response Schema:**

```dart
class PaymentListResponse {
  final bool success;
  final String message;
  final PaymentListData data;

  PaymentListResponse({
    required this.success,
    required this.message,
    required this.data,
  });

  factory PaymentListResponse.fromJson(Map<String, dynamic> json) {
    return PaymentListResponse(
      success: json['success'],
      message: json['message'],
      data: PaymentListData.fromJson(json['data']),
    );
  }
}

class PaymentListData {
  final List<PaymentTransaction> transactions;
  final PaginationInfo pagination;

  PaymentListData({
    required this.transactions,
    required this.pagination,
  });

  factory PaymentListData.fromJson(Map<String, dynamic> json) {
    return PaymentListData(
      transactions: (json['transactions'] as List)
          .map((e) => PaymentTransaction.fromJson(e))
          .toList(),
      pagination: PaginationInfo.fromJson(json['pagination']),
    );
  }
}

class PaymentTransaction {
  final String id;
  final String paymentSessionId;
  final String? paymentId;
  final double amount;
  final String currency;
  final String description;
  final String invoiceNumber;
  final String customerId;
  final String status;
  final DateTime createdAt;
  final DateTime updatedAt;

  PaymentTransaction({
    required this.id,
    required this.paymentSessionId,
    this.paymentId,
    required this.amount,
    required this.currency,
    required this.description,
    required this.invoiceNumber,
    required this.customerId,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  factory PaymentTransaction.fromJson(Map<String, dynamic> json) {
    return PaymentTransaction(
      id: json['_id'],
      paymentSessionId: json['payments_session_id'],
      paymentId: json['payment_id'],
      amount: json['amount'].toDouble(),
      currency: json['currency'],
      description: json['description'],
      invoiceNumber: json['invoice_number'],
      customerId: json['customer_id'],
      status: json['status'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }
}

class PaginationInfo {
  final int currentPage;
  final int totalPages;
  final int totalItems;
  final int itemsPerPage;
  final bool hasNextPage;
  final bool hasPrevPage;

  PaginationInfo({
    required this.currentPage,
    required this.totalPages,
    required this.totalItems,
    required this.itemsPerPage,
    required this.hasNextPage,
    required this.hasPrevPage,
  });

  factory PaginationInfo.fromJson(Map<String, dynamic> json) {
    return PaginationInfo(
      currentPage: json['current_page'],
      totalPages: json['total_pages'],
      totalItems: json['total_items'],
      itemsPerPage: json['items_per_page'],
      hasNextPage: json['has_next_page'],
      hasPrevPage: json['has_prev_page'],
    );
  }
}
```
