/**
 * Simple Zoho Payment API Test (Official SDK)
 *
 * A lightweight test script for official Zoho Payments SDK integration
 */

const BASE_URL = 'http://localhost:3000';

// Test data for official SDK
const testPaymentData = {
  amount: 1000.50,
  invoiceNo: 'TEST-INV-001',
  customerId: 'TEST-CUST-001',
  customerName: 'Test Customer',
  customerEmail: '<EMAIL>',
  customerPhone: '+919876543210',
  description: 'Test payment for official SDK integration',
  currency: 'INR',
  businessName: 'AquaPartner Test Business',
  referenceNumber: 'TEST-REF-001'
};

/**
 * Make HTTP request
 */
async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, options);
    const data = await response.json();
    
    return {
      success: response.ok,
      status: response.status,
      data: data
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Test Health Check
 */
async function testHealthCheck() {
  console.log('\n=== Testing Health Check ===');
  
  const result = await makeRequest(`${BASE_URL}/api/zoho/health`);
  
  if (result.success) {
    console.log('✅ Health check passed');
    console.log('Response:', JSON.stringify(result.data, null, 2));
    return true;
  } else {
    console.log('❌ Health check failed');
    console.log('Error:', result.error || result.data);
    return false;
  }
}

/**
 * Test Flutter Payment Initiation API (Official SDK)
 */
async function testFlutterPaymentInitiation() {
  console.log('\n=== Testing Flutter Payment Initiation API (Official SDK) ===');

  const result = await makeRequest(`${BASE_URL}/api/flutter/payment/initiate`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(testPaymentData)
  });

  console.log('Status:', result.status);
  console.log('Response:', JSON.stringify(result.data || result.error, null, 2));

  if (result.success && result.data.success && result.data.webview_url) {
    console.log('✅ Flutter payment initiation API works');
    console.log('WebView URL:', result.data.webview_url);
    return true;
  } else {
    console.log('❌ Flutter payment initiation API failed');
    return false;
  }
}

/**
 * Test Invalid Data Handling (Official SDK)
 */
async function testInvalidDataHandling() {
  console.log('\n=== Testing Invalid Data Handling (Official SDK) ===');

  const invalidData = { invoiceNo: 'TEST-INV-002' }; // Missing amount and customerId

  const result = await makeRequest(`${BASE_URL}/api/flutter/payment/initiate`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(invalidData)
  });

  console.log('Status:', result.status);
  console.log('Response:', JSON.stringify(result.data || result.error, null, 2));

  if (result.status === 400 && result.data.error) {
    console.log('✅ Invalid data handling works correctly');
    return true;
  } else {
    console.log('❌ Invalid data handling failed');
    return false;
  }
}

/**
 * Test Payment Page HTML (Official SDK)
 */
async function testPaymentPageHTML() {
  console.log('\n=== Testing Payment Page HTML (Official SDK) ===');

  const sessionId = 'TEST_SESSION_ID_123';
  const queryParams = new URLSearchParams({
    sessionId: sessionId,
    amount: testPaymentData.amount.toString(),
    currency: testPaymentData.currency,
    description: testPaymentData.description,
    customerName: testPaymentData.customerName,
    businessName: testPaymentData.businessName
  });

  try {
    const response = await fetch(`${BASE_URL}/api/payment-page?${queryParams}`);

    console.log('Status:', response.status);

    if (response.ok) {
      const htmlContent = await response.text();

      // Check for official SDK elements
      const checks = [
        { name: 'Official SDK Script', test: htmlContent.includes('zpayments.js') },
        { name: 'ZPayments Class', test: htmlContent.includes('window.ZPayments') },
        { name: 'requestPaymentMethod', test: htmlContent.includes('requestPaymentMethod') },
        { name: 'Session ID', test: htmlContent.includes(sessionId) },
        { name: 'Flutter Callback', test: htmlContent.includes('sendToFlutterApp') }
      ];

      console.log('Payment Page HTML Checks:');
      checks.forEach(check => {
        const icon = check.test ? '✅' : '❌';
        console.log(`  ${icon} ${check.name}`);
      });

      const allPassed = checks.every(check => check.test);
      if (allPassed) {
        console.log('✅ Payment page HTML test passed');
        return true;
      } else {
        console.log('❌ Payment page HTML test failed');
        return false;
      }
    } else {
      console.log('❌ Failed to load payment page HTML');
      return false;
    }
  } catch (error) {
    console.log('❌ Payment page HTML test failed:', error.message);
    return false;
  }
}

/**
 * Test Payment Status API (Official SDK)
 */
async function testPaymentStatusAPI() {
  console.log('\n=== Testing Payment Status API (Official SDK) ===');

  const sessionId = 'TEST_SESSION_ID_123';

  try {
    const response = await fetch(`${BASE_URL}/api/flutter/payment/status/${sessionId}`);

    console.log('Status:', response.status);

    if (response.status === 404 || response.status === 200) {
      const data = await response.json();
      console.log('Response:', JSON.stringify(data, null, 2));

      if (response.status === 404 || (data.success === false)) {
        console.log('✅ Payment status API correctly handles non-existent session');
        return true;
      } else if (data.success) {
        console.log('✅ Payment status API works correctly');
        return true;
      }
    }

    console.log('❌ Payment status API failed unexpectedly');
    return false;
  } catch (error) {
    console.log('❌ Payment status API failed:', error.message);
    return false;
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting Simple Zoho Payment API Tests (Official SDK)');
  console.log('========================================================');

  const tests = [
    { name: 'Health Check', fn: testHealthCheck },
    { name: 'Flutter Payment Initiation', fn: testFlutterPaymentInitiation },
    { name: 'Invalid Data Handling', fn: testInvalidDataHandling },
    { name: 'Payment Page HTML', fn: testPaymentPageHTML },
    { name: 'Payment Status API', fn: testPaymentStatusAPI }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.log(`❌ ${test.name} threw an error:`, error.message);
      failed++;
    }
  }
  
  console.log('\n==========================================');
  console.log('📊 TEST SUMMARY');
  console.log('==========================================');
  console.log(`Total Tests: ${tests.length}`);
  console.log(`Passed: ${passed}`);
  console.log(`Failed: ${failed}`);
  console.log(`Success Rate: ${((passed / tests.length) * 100).toFixed(1)}%`);
  
  if (passed === tests.length) {
    console.log('\n🎉 All tests passed!');
  } else {
    console.log('\n⚠️  Some tests failed. Check the output above for details.');
  }
  
  return { passed, failed, total: tests.length };
}

// Check if running directly
if (typeof require !== 'undefined' && require.main === module) {
  runAllTests().catch(console.error);
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runAllTests,
    testHealthCheck,
    testFlutterPaymentInitiation,
    testInvalidDataHandling,
    testPaymentPageHTML,
    testPaymentStatusAPI
  };
}
