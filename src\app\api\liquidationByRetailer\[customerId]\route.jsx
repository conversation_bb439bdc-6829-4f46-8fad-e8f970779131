import connectedDB from '@/app/config/database'
import LiquidationByRetailer from '@/app/models/LiquidationByRetailer'

// Force dynamic rendering to prevent static generation
export const dynamic = 'force-dynamic'

// Remove generateStaticParams since we're using dynamic rendering
// export async function generateStaticParams() {
//   return [
//     { customerId: 'customer1' },
//     { customerId: 'customer2' },
//   ]
// }

// GET /api/liquidationByRetailer/:id
export const GET = async (request, { params }) => {
  try {
    await connectedDB()
    const liquidation = await LiquidationByRetailer.findOne({ partnerId: params.customerId })

    if (!liquidation) return new Response(JSON.stringify({ results: [] }), { status: 200 })
    return new Response(JSON.stringify({ results: liquidation }), { status: 200 })
  } catch (error) {
    return new Response(error, { status: 500 })
  }
}
