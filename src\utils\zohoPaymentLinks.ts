/**
 * Zoho Payment Links Integration Utility
 * Provides a unified interface for creating payments with automatic method selection
 */

export interface CreatePaymentRequest {
  amount: number
  currency?: string
  description: string
  invoice_number?: string
  customer_id: string
  customer_name?: string
  customer_email: string
  customer_phone?: string
  redirect_url?: string
  reference_id?: string
  expires_at?: string
  notify_user?: boolean
  meta_data?: Array<{ key: string; value: string }>
}

export interface PaymentLinkResponse {
  payment_link_id: string
  payment_link_url: string
  amount: string
  currency: string
  description: string
  reference_id: string
  status: string
  expires_at?: string
  created_time: number
}

export interface PaymentSessionResponse {
  payment_session_id: string
  amount: number
  currency: string
  description: string
  invoice_number: string
  created_time: number
}

export interface CreatePaymentResponse {
  success: boolean
  message: string
  payment_method: 'payment_link' | 'payment_session'
  fallback_used: boolean
  original_method?: string
  final_method?: string
  data: PaymentLinkResponse | PaymentSessionResponse
  payment_link?: any
  payment_session?: any
  transaction_id: string
}

/**
 * Create payment using automatic method selection
 * Chooses between payment links and payment sessions based on environment and configuration
 */
export async function createZohoPayment(paymentData: CreatePaymentRequest): Promise<CreatePaymentResponse> {
  console.log('📡 PAYMENT API: Creating payment with automatic method selection:', paymentData)

  const response = await fetch('/api/zoho/payments/create-payment', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(paymentData),
  })

  console.log('📡 PAYMENT API: Response status:', response.status, response.statusText)

  if (!response.ok) {
    const errorText = await response.text()
    console.error('❌ PAYMENT API ERROR: Response not ok:', errorText)
    throw new Error(`Failed to create payment: ${response.statusText}`)
  }

  const responseData = await response.json()
  console.log('✅ PAYMENT API: Success response:', responseData)

  return responseData
}

/**
 * Create payment link specifically (bypasses automatic selection)
 */
export async function createZohoPaymentLink(paymentData: CreatePaymentRequest): Promise<CreatePaymentResponse> {
  console.log('🔗 PAYMENT LINK API: Creating payment link:', paymentData)

  // Set environment variable temporarily to force payment links
  const originalMethod = process.env.ZOHO_PAYMENT_METHOD

  const response = await fetch('/api/zoho/payments/create-payment', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Force-Payment-Method': 'links', // Custom header to force payment links
    },
    body: JSON.stringify(paymentData),
  })

  console.log('🔗 PAYMENT LINK API: Response status:', response.status, response.statusText)

  if (!response.ok) {
    const errorText = await response.text()
    console.error('❌ PAYMENT LINK API ERROR: Response not ok:', errorText)
    throw new Error(`Failed to create payment link: ${response.statusText}`)
  }

  const responseData = await response.json()
  console.log('✅ PAYMENT LINK API: Success response:', responseData)

  return responseData
}

/**
 * Initialize payment based on response type
 * Handles both payment links (redirect) and payment sessions (widget)
 */
export function initializeZohoPaymentResponse(paymentResponse: CreatePaymentResponse, invoiceNumber: string) {
  console.log('🚀 PAYMENT INIT: Initializing payment response:', paymentResponse)

  if (paymentResponse.payment_method === 'payment_link') {
    // Payment link - direct redirect
    console.log('🔗 PAYMENT INIT: Using payment link redirect')
    const paymentUrl =
      (paymentResponse.data as PaymentLinkResponse).payment_link_url || paymentResponse.payment_link?.url

    if (paymentUrl) {
      console.log('🔗 PAYMENT INIT: Redirecting to:', paymentUrl)
      window.location.href = paymentUrl
    } else {
      console.error('❌ PAYMENT INIT: No payment URL found in response')
      throw new Error('Payment link URL not found')
    }
  } else {
    // Payment session - use existing widget initialization
    console.log('🎯 PAYMENT INIT: Using payment session widget')
    const sessionId =
      (paymentResponse.data as PaymentSessionResponse).payment_session_id ||
      paymentResponse.payment_session?.payments_session_id
    const amount = (paymentResponse.data as PaymentSessionResponse).amount || paymentResponse.payment_session?.amount

    if (sessionId && amount) {
      // Import and use the existing widget initialization
      import('./zohoPayment')
        .then(({ initializeZohoWidget }) => {
          initializeZohoWidget(sessionId, amount, invoiceNumber)
        })
        .catch((error) => {
          console.error('❌ PAYMENT INIT: Failed to load zohoPayment module:', error)
          throw new Error('Failed to initialize payment widget')
        })
    } else {
      console.error('❌ PAYMENT INIT: Missing session ID or amount')
      throw new Error('Payment session data incomplete')
    }
  }
}

/**
 * Get payment method configuration
 */
export function getPaymentMethodConfig(): string {
  // Check for client-side configuration or default to auto
  return (typeof window !== 'undefined' && (window as any).ZOHO_PAYMENT_METHOD) || 'auto'
}

/**
 * Detect if current environment should use payment links
 */
export function shouldUsePaymentLinks(): boolean {
  if (typeof navigator === 'undefined') return false

  const userAgent = navigator.userAgent
  const isWebView = /wv|WebView/i.test(userAgent)
  const isMobile = /Mobile|Android|iPhone|iPad/i.test(userAgent)

  const config = getPaymentMethodConfig()

  switch (config) {
    case 'links':
      return true
    case 'widget':
      return false
    case 'auto':
    default:
      return isWebView || isMobile
  }
}
