#!/usr/bin/env node

/**
 * Test Script: Zoho Payment Integration
 *
 * This script tests the complete payment integration with official SDK
 */

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const https = require('https');
const http = require('http');

console.log('🧪 Zoho Payment Integration Test Suite');
console.log('======================================\n');

// Configuration
const BASE_URL = 'http://localhost:3001';
const TEST_DATA = {
  amount: 1.00,
  invoiceNo: `TEST-${Date.now()}`,
  customerId: 'TEST-CUSTOMER-001',
  customerName: 'Test Customer',
  customerEmail: '<EMAIL>',
  customerPhone: '9876543210',
  description: 'Test Payment for SDK Validation',
  businessName: 'Test Business',
  currency: 'INR'
};

// Helper function to make HTTP requests
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (error) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', reject);
    
    if (options.body) {
      req.write(JSON.stringify(options.body));
    }
    
    req.end();
  });
}

// Test 1: Environment Variables
function testEnvironmentVariables() {
  console.log('1. 🔍 Testing Environment Variables...');
  
  const requiredVars = [
    'ZOHO_PAY_ACCOUNT_ID',
    'ZOHO_PAY_API_KEY',
    'MONGODB_URI'
  ];

  const results = {};
  requiredVars.forEach(varName => {
    results[varName] = !!process.env[varName];
  });

  const allSet = Object.values(results).every(Boolean);
  
  if (allSet) {
    console.log('   ✅ All environment variables are set\n');
  } else {
    console.log('   ❌ Missing environment variables:');
    Object.entries(results).forEach(([key, value]) => {
      if (!value) console.log(`      - ${key}`);
    });
    console.log();
  }
  
  return allSet;
}

// Test 2: Payment Initiation API
async function testPaymentInitiation() {
  console.log('2. 🚀 Testing Payment Initiation API...');
  
  try {
    const response = await makeRequest(`${BASE_URL}/api/flutter/payment/initiate`, {
      method: 'POST',
      body: TEST_DATA
    });

    if (response.status === 200 && response.data.success) {
      console.log('   ✅ Payment initiation successful');
      console.log(`   📝 Session ID: ${response.data.payment_session.id}`);
      console.log(`   🔗 WebView URL: ${response.data.webview_url}`);
      console.log();
      return { success: true, data: response.data };
    } else {
      console.log(`   ❌ Payment initiation failed (${response.status})`);
      console.log(`   Error: ${JSON.stringify(response.data, null, 2)}`);
      console.log();
      return { success: false, error: response.data };
    }
  } catch (error) {
    console.log(`   ❌ Request failed: ${error.message}`);
    console.log();
    return { success: false, error: error.message };
  }
}

// Test 3: Payment Page HTML
async function testPaymentPage(sessionId) {
  console.log('3. 📄 Testing Payment Page HTML...');
  
  try {
    const url = `${BASE_URL}/api/payment-page?sessionId=${sessionId}&amount=${TEST_DATA.amount}&currency=${TEST_DATA.currency}&description=${encodeURIComponent(TEST_DATA.description)}`;
    
    const response = await makeRequest(url);

    if (response.status === 200) {
      const htmlContent = response.data;
      
      // Check for official SDK elements
      const checks = [
        { name: 'Official SDK Script', test: htmlContent.includes('zpayments.js') },
        { name: 'ZPayments Class', test: htmlContent.includes('window.ZPayments') },
        { name: 'requestPaymentMethod', test: htmlContent.includes('requestPaymentMethod') },
        { name: 'Session ID Placeholder', test: htmlContent.includes(sessionId) },
        { name: 'Flutter Callback', test: htmlContent.includes('sendToFlutterApp') }
      ];

      const allPassed = checks.every(check => check.test);
      
      if (allPassed) {
        console.log('   ✅ Payment page HTML is correctly configured');
      } else {
        console.log('   ⚠️  Payment page HTML issues:');
        checks.forEach(check => {
          const icon = check.test ? '✅' : '❌';
          console.log(`      ${icon} ${check.name}`);
        });
      }
      console.log();
      return allPassed;
    } else {
      console.log(`   ❌ Failed to load payment page (${response.status})`);
      console.log();
      return false;
    }
  } catch (error) {
    console.log(`   ❌ Request failed: ${error.message}`);
    console.log();
    return false;
  }
}

// Test 4: Payment Status API
async function testPaymentStatus(sessionId) {
  console.log('4. 📊 Testing Payment Status API...');
  
  try {
    const response = await makeRequest(`${BASE_URL}/api/flutter/payment/status/${sessionId}`);

    if (response.status === 200 && response.data.success) {
      console.log('   ✅ Payment status API working');
      console.log(`   📝 Status: ${response.data.payment_session.status}`);
      console.log(`   ⏰ Expires: ${response.data.payment_session.expires_at}`);
      console.log();
      return true;
    } else {
      console.log(`   ❌ Payment status check failed (${response.status})`);
      console.log(`   Error: ${JSON.stringify(response.data, null, 2)}`);
      console.log();
      return false;
    }
  } catch (error) {
    console.log(`   ❌ Request failed: ${error.message}`);
    console.log();
    return false;
  }
}

// Test 5: Database Connection
async function testDatabaseConnection() {
  console.log('5. 🗄️  Testing Database Connection...');
  
  try {
    // This is a simple test - in a real scenario, you might want to test actual DB operations
    const response = await makeRequest(`${BASE_URL}/api/flutter/payment/initiate`, {
      method: 'POST',
      body: { ...TEST_DATA, amount: 0.01 } // Minimal test amount
    });

    if (response.status === 200) {
      console.log('   ✅ Database connection working (payment session created)');
      console.log();
      return true;
    } else {
      console.log('   ❌ Database connection issues detected');
      console.log();
      return false;
    }
  } catch (error) {
    console.log(`   ❌ Database test failed: ${error.message}`);
    console.log();
    return false;
  }
}

// Generate Test Report
function generateTestReport(results) {
  console.log('📊 Test Results Summary');
  console.log('======================');
  
  const tests = [
    { name: 'Environment Variables', status: results.env },
    { name: 'Payment Initiation API', status: results.initiation },
    { name: 'Payment Page HTML', status: results.html },
    { name: 'Payment Status API', status: results.status },
    { name: 'Database Connection', status: results.database }
  ];

  tests.forEach(test => {
    const icon = test.status ? '✅' : '❌';
    console.log(`${icon} ${test.name}`);
  });

  const allPassed = tests.every(test => test.status);
  const passedCount = tests.filter(test => test.status).length;
  
  console.log('\n' + '='.repeat(40));
  console.log(`📈 Score: ${passedCount}/${tests.length} tests passed`);
  
  if (allPassed) {
    console.log('🎉 All tests passed! Your integration is ready.');
    console.log('\n✨ Next Steps:');
    console.log('1. Test with your Flutter app');
    console.log('2. Verify payment callbacks');
    console.log('3. Test with small real amounts');
  } else {
    console.log('⚠️  Some tests failed. Please address the issues above.');
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Check environment variables');
    console.log('2. Ensure server is running');
    console.log('3. Verify Zoho Payment credentials');
  }
}

// Main test runner
async function runTests() {
  const results = {};
  
  // Run tests
  results.env = testEnvironmentVariables();
  
  const initiationResult = await testPaymentInitiation();
  results.initiation = initiationResult.success;
  
  if (initiationResult.success) {
    const sessionId = initiationResult.data.payment_session.id;
    results.html = await testPaymentPage(sessionId);
    results.status = await testPaymentStatus(sessionId);
  } else {
    results.html = false;
    results.status = false;
  }
  
  results.database = await testDatabaseConnection();
  
  generateTestReport(results);
}

// Run the tests
runTests().catch(error => {
  console.error('❌ Test suite failed:', error);
  process.exit(1);
});
