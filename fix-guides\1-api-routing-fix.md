# Fix Guide: Production API Routing Issue

## Problem

Production POST requests to `/api/zoho/payments/create-session` return documentation instead of processing payment creation.

## Root Cause Analysis

### Step 1: Verify API Route File Structure

```bash
# Check if the route file exists and has correct structure
ls -la src/app/api/zoho/payments/create-session/
cat src/app/api/zoho/payments/create-session/route.js
```

### Step 2: Verify Export Functions

The route file must export both GET and POST functions:

```javascript
// Check that both functions are exported
export async function GET(request) { ... }
export async function POST(request) { ... }
```

### Step 3: Test Local Production Build

```bash
# Build production version locally
npm run build

# Start production server locally
npm start

# Test the endpoint
curl -X POST http://localhost:3000/api/zoho/payments/create-session \
  -H "Content-Type: application/json" \
  -d '{"amount": 100, "currency": "INR", "description": "test"}'
```

## Solution Steps

### Step 1: Fix API Route File Structure

Check the current route file:

```bash
# Navigate to the route file
cd src/app/api/zoho/payments/create-session/
```

Ensure the file is named exactly `route.js` (not `route.jsx` or other variants).

### Step 2: Verify Route Handler Exports

The route file should have this structure:

```javascript
// src/app/api/zoho/payments/create-session/route.js

import zohoPaymentService from '@/app/lib/zohoPaymentService'

/**
 * GET /api/zoho/payments/create-session
 * Returns payment session creation requirements
 */
export async function GET() {
  // Documentation response
  return new Response(
    JSON.stringify({
      message: 'Payment Session Creation Requirements',
      // ... documentation
    }),
    { status: 200 }
  )
}

/**
 * POST /api/zoho/payments/create-session
 * Create a new payment session
 */
export async function POST(request) {
  try {
    const body = await request.json()
    // Payment creation logic
    const result = await zohoPaymentService.createPaymentSession(body)
    return new Response(
      JSON.stringify({
        success: true,
        data: result,
      }),
      { status: 201 }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({
        error: error.message,
      }),
      { status: 400 }
    )
  }
}
```

### Step 3: Check Next.js Configuration

Verify `next.config.js` doesn't exclude API routes:

```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  // Ensure API routes are included
  experimental: {
    appDir: true,
  },
  // Don't exclude API directory
}

module.exports = nextConfig
```

### Step 4: Fix Azure Static Web Apps Configuration

Check `.github/workflows/azure-static-web-apps-yellow-sky-08e56d200.yml`:

```yaml
# Ensure API location is correctly set
api_location: '/api' # Should point to API directory
app_location: '/' # App source code path
output_location: '' # Built app content directory
```

### Step 5: Add Azure SWA Routes Configuration

Create `staticwebapp.config.json` in project root:

```json
{
  "routes": [
    {
      "route": "/api/*",
      "allowedRoles": ["anonymous"]
    }
  ],
  "navigationFallback": {
    "rewrite": "/index.html",
    "exclude": ["/api/*"]
  }
}
```

## Testing Procedures

### Test 1: Local Production Build

```bash
# Clean build
rm -rf .next
npm run build
npm start

# Test POST request
curl -X POST http://localhost:3000/api/zoho/payments/create-session \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 100,
    "currency": "INR",
    "description": "Test Payment",
    "invoice_number": "TEST_001",
    "customer_id": "test_customer"
  }'
```

Expected: Status 201 with payment session data

### Test 2: Production Deployment

```bash
# Commit and push changes
git add .
git commit -m "Fix API routing for production"
git push origin main

# Wait for deployment (2-3 minutes)
# Test production endpoint
curl -X POST https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api/zoho/payments/create-session \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 100,
    "currency": "INR",
    "description": "Production Test",
    "invoice_number": "PROD_001",
    "customer_id": "prod_customer"
  }'
```

Expected: Status 201 with payment session data

### Test 3: Verification Script

```bash
node tests/quick-fix-verification.js
```

## Rollback Procedure

If the fix doesn't work:

### Step 1: Revert Changes

```bash
git revert HEAD
git push origin main
```

### Step 2: Alternative Approach - Check File Naming

```bash
# Ensure exact file naming
mv route.jsx route.js  # If file has wrong extension
```

### Step 3: Debug with Logs

Add logging to the route handler:

```javascript
export async function POST(request) {
  console.log('POST handler called')
  // ... rest of code
}
```

## Common Issues and Solutions

### Issue: Case Sensitivity

- Ensure file is named `route.js` not `Route.js`
- Check folder names are lowercase

### Issue: Export Syntax

- Use `export async function POST` not `export const POST`
- Ensure functions are async

### Issue: Azure SWA API Detection

- Verify `api_location: '/api'` in workflow
- Check that API folder is included in deployment

## Success Criteria

- POST requests return status 201 with payment data
- GET requests return status 200 with documentation
- Production and local behavior match
- Validation middleware becomes active
