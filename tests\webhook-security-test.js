/**
 * Webhook Security Testing Suite
 * Comprehensive testing of webhook security features
 */

const axios = require('axios')
const crypto = require('crypto')
require('dotenv').config({ path: '.env.local' })

// Test configuration
const BASE_URL = process.env.NEXT_PUBLIC_DOMAIN || 'http://localhost:3000'
const WEBHOOK_URL = `${BASE_URL}/api/zoho/webhooks/payment`
const WEBHOOK_SECRET = process.env.ZOHO_WEBHOOK_SECRET
const TEST_TIMEOUT = 15000

console.log('🔒 WEBHOOK SECURITY TESTING SUITE')
console.log('==================================')
console.log(`Webhook URL: ${WEBHOOK_URL}`)
console.log(`Secret Configured: ${WEBHOOK_SECRET ? '✅ Yes' : '❌ No'}`)
console.log('')

/**
 * Test HMAC signature validation
 */
async function testHMACValidation() {
  console.log('🔐 Testing HMAC Signature Validation')
  console.log('------------------------------------')

  const tests = []

  try {
    const basePayload = {
      event_type: 'payment.succeeded',
      payment_id: 'test_hmac_001',
      status: 'success',
      amount: 100,
      currency: 'INR',
      created_time: Math.floor(Date.now() / 1000)
    }

    // Test 1: Valid signature
    console.log('📝 Test 1: Valid HMAC signature...')
    const validResult = await sendWebhookWithSignature(basePayload, true)
    tests.push({
      name: 'Valid HMAC signature',
      passed: validResult.status === 200,
      expected: 200,
      actual: validResult.status,
      details: validResult.data
    })
    console.log(`   ${validResult.status === 200 ? '✅' : '❌'} Status: ${validResult.status}`)

    // Test 2: Invalid signature
    console.log('📝 Test 2: Invalid HMAC signature...')
    const invalidResult = await sendWebhookWithSignature(basePayload, false, 'invalid_signature')
    tests.push({
      name: 'Invalid HMAC signature rejection',
      passed: invalidResult.status === 401,
      expected: 401,
      actual: invalidResult.status,
      details: invalidResult.data
    })
    console.log(`   ${invalidResult.status === 401 ? '✅' : '❌'} Status: ${invalidResult.status}`)

    // Test 3: Missing signature
    console.log('📝 Test 3: Missing HMAC signature...')
    const missingResult = await sendWebhookWithSignature(basePayload, false, null)
    tests.push({
      name: 'Missing HMAC signature rejection',
      passed: missingResult.status === 401,
      expected: 401,
      actual: missingResult.status,
      details: missingResult.data
    })
    console.log(`   ${missingResult.status === 401 ? '✅' : '❌'} Status: ${missingResult.status}`)

    // Test 4: Malformed signature
    console.log('📝 Test 4: Malformed HMAC signature...')
    const malformedResult = await sendWebhookWithSignature(basePayload, false, 'not_a_valid_hex_signature!')
    tests.push({
      name: 'Malformed HMAC signature rejection',
      passed: malformedResult.status === 401,
      expected: 401,
      actual: malformedResult.status,
      details: malformedResult.data
    })
    console.log(`   ${malformedResult.status === 401 ? '✅' : '❌'} Status: ${malformedResult.status}`)

    return { success: true, tests }

  } catch (error) {
    console.log(`   ❌ HMAC testing failed: ${error.message}`)
    return { success: false, error: error.message, tests }
  }
}

/**
 * Test timestamp validation (replay attack prevention)
 */
async function testTimestampValidation() {
  console.log('⏰ Testing Timestamp Validation')
  console.log('-------------------------------')

  const tests = []

  try {
    const basePayload = {
      event_type: 'payment.succeeded',
      payment_id: 'test_timestamp_001',
      status: 'success',
      amount: 100,
      currency: 'INR'
    }

    // Test 1: Current timestamp (valid)
    console.log('📝 Test 1: Current timestamp...')
    const currentPayload = { ...basePayload, created_time: Math.floor(Date.now() / 1000) }
    const currentResult = await sendWebhookWithSignature(currentPayload, true)
    tests.push({
      name: 'Current timestamp acceptance',
      passed: currentResult.status === 200,
      expected: 200,
      actual: currentResult.status,
      details: currentResult.data
    })
    console.log(`   ${currentResult.status === 200 ? '✅' : '❌'} Status: ${currentResult.status}`)

    // Test 2: Old timestamp (replay attack)
    console.log('📝 Test 2: Old timestamp (10 minutes ago)...')
    const oldPayload = { ...basePayload, created_time: Math.floor((Date.now() - 10 * 60 * 1000) / 1000) }
    const oldResult = await sendWebhookWithSignature(oldPayload, true)
    tests.push({
      name: 'Old timestamp rejection',
      passed: oldResult.status === 401,
      expected: 401,
      actual: oldResult.status,
      details: oldResult.data
    })
    console.log(`   ${oldResult.status === 401 ? '✅' : '❌'} Status: ${oldResult.status}`)

    // Test 3: Future timestamp
    console.log('📝 Test 3: Future timestamp...')
    const futurePayload = { ...basePayload, created_time: Math.floor((Date.now() + 10 * 60 * 1000) / 1000) }
    const futureResult = await sendWebhookWithSignature(futurePayload, true)
    tests.push({
      name: 'Future timestamp handling',
      passed: futureResult.status === 200 || futureResult.status === 401, // Either is acceptable
      expected: '200 or 401',
      actual: futureResult.status,
      details: futureResult.data
    })
    console.log(`   ${futureResult.status === 200 || futureResult.status === 401 ? '✅' : '❌'} Status: ${futureResult.status}`)

    // Test 4: Missing timestamp
    console.log('📝 Test 4: Missing timestamp...')
    const noTimestampPayload = { ...basePayload }
    delete noTimestampPayload.created_time
    const noTimestampResult = await sendWebhookWithSignature(noTimestampPayload, true)
    tests.push({
      name: 'Missing timestamp handling',
      passed: noTimestampResult.status === 400 || noTimestampResult.status === 401,
      expected: '400 or 401',
      actual: noTimestampResult.status,
      details: noTimestampResult.data
    })
    console.log(`   ${noTimestampResult.status === 400 || noTimestampResult.status === 401 ? '✅' : '❌'} Status: ${noTimestampResult.status}`)

    return { success: true, tests }

  } catch (error) {
    console.log(`   ❌ Timestamp testing failed: ${error.message}`)
    return { success: false, error: error.message, tests }
  }
}

/**
 * Test idempotency protection
 */
async function testIdempotencyProtection() {
  console.log('🔄 Testing Idempotency Protection')
  console.log('---------------------------------')

  const tests = []

  try {
    const uniquePayload = {
      event_type: 'payment.succeeded',
      payment_id: `test_idempotent_${Date.now()}`,
      status: 'success',
      amount: 100,
      currency: 'INR',
      created_time: Math.floor(Date.now() / 1000)
    }

    // Test 1: First request (should succeed)
    console.log('📝 Test 1: First webhook request...')
    const firstResult = await sendWebhookWithSignature(uniquePayload, true)
    tests.push({
      name: 'First request processing',
      passed: firstResult.status === 200,
      expected: 200,
      actual: firstResult.status,
      details: firstResult.data
    })
    console.log(`   ${firstResult.status === 200 ? '✅' : '❌'} Status: ${firstResult.status}`)

    // Wait a moment for processing
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Test 2: Duplicate request (should be detected)
    console.log('📝 Test 2: Duplicate webhook request...')
    const duplicateResult = await sendWebhookWithSignature(uniquePayload, true)
    tests.push({
      name: 'Duplicate request detection',
      passed: duplicateResult.status === 200 && duplicateResult.data?.message?.includes('already processed'),
      expected: '200 with duplicate message',
      actual: `${duplicateResult.status} - ${duplicateResult.data?.message}`,
      details: duplicateResult.data
    })
    console.log(`   ${duplicateResult.data?.message?.includes('already processed') ? '✅' : '❌'} Status: ${duplicateResult.status}`)

    // Test 3: Similar but different request (should succeed)
    console.log('📝 Test 3: Similar but different request...')
    const differentPayload = { ...uniquePayload, payment_id: `${uniquePayload.payment_id}_different` }
    const differentResult = await sendWebhookWithSignature(differentPayload, true)
    tests.push({
      name: 'Different request processing',
      passed: differentResult.status === 200 && !differentResult.data?.message?.includes('already processed'),
      expected: '200 without duplicate message',
      actual: `${differentResult.status} - ${differentResult.data?.message || 'new processing'}`,
      details: differentResult.data
    })
    console.log(`   ${differentResult.status === 200 && !differentResult.data?.message?.includes('already processed') ? '✅' : '❌'} Status: ${differentResult.status}`)

    return { success: true, tests }

  } catch (error) {
    console.log(`   ❌ Idempotency testing failed: ${error.message}`)
    return { success: false, error: error.message, tests }
  }
}

/**
 * Test rate limiting
 */
async function testRateLimiting() {
  console.log('🚦 Testing Rate Limiting')
  console.log('------------------------')

  const tests = []

  try {
    const basePayload = {
      event_type: 'payment.succeeded',
      status: 'success',
      amount: 100,
      currency: 'INR',
      created_time: Math.floor(Date.now() / 1000)
    }

    // Test 1: Normal request rate (should succeed)
    console.log('📝 Test 1: Normal request rate...')
    const normalRequests = []
    for (let i = 0; i < 5; i++) {
      const payload = { ...basePayload, payment_id: `test_rate_${Date.now()}_${i}` }
      normalRequests.push(sendWebhookWithSignature(payload, true))
      await new Promise(resolve => setTimeout(resolve, 100)) // Small delay
    }

    const normalResults = await Promise.all(normalRequests)
    const normalSuccess = normalResults.every(result => result.status === 200 || result.status === 429)
    
    tests.push({
      name: 'Normal request rate handling',
      passed: normalSuccess,
      expected: 'All requests handled (200 or 429)',
      actual: normalResults.map(r => r.status).join(', '),
      details: normalResults
    })
    console.log(`   ${normalSuccess ? '✅' : '❌'} Results: ${normalResults.map(r => r.status).join(', ')}`)

    // Test 2: Rapid burst (may trigger rate limiting)
    console.log('📝 Test 2: Rapid burst requests...')
    const burstRequests = []
    for (let i = 0; i < 10; i++) {
      const payload = { ...basePayload, payment_id: `test_burst_${Date.now()}_${i}` }
      burstRequests.push(sendWebhookWithSignature(payload, true))
    }

    const burstResults = await Promise.all(burstRequests)
    const hasRateLimit = burstResults.some(result => result.status === 429)
    
    tests.push({
      name: 'Rate limiting activation',
      passed: true, // Rate limiting may or may not trigger depending on configuration
      expected: 'Rate limiting may activate',
      actual: `${burstResults.filter(r => r.status === 429).length} rate limited out of ${burstResults.length}`,
      details: burstResults
    })
    console.log(`   ✅ Rate limiting: ${hasRateLimit ? 'Activated' : 'Not triggered'} (${burstResults.filter(r => r.status === 429).length}/${burstResults.length})`)

    return { success: true, tests }

  } catch (error) {
    console.log(`   ❌ Rate limiting testing failed: ${error.message}`)
    return { success: false, error: error.message, tests }
  }
}

/**
 * Send webhook with signature
 */
async function sendWebhookWithSignature(payload, useValidSignature = true, customSignature = null) {
  try {
    const payloadString = JSON.stringify(payload)
    let signature = customSignature

    if (useValidSignature && !customSignature && WEBHOOK_SECRET) {
      const timestamp = payload.created_time || Math.floor(Date.now() / 1000)
      const signaturePayload = `${timestamp}.${payloadString}`
      signature = crypto.createHmac('sha256', WEBHOOK_SECRET).update(signaturePayload).digest('hex')
    }

    const headers = { 'Content-Type': 'application/json' }
    if (signature !== null) {
      headers['x-zoho-webhook-signature'] = signature
    }

    const response = await axios.post(WEBHOOK_URL, payload, {
      timeout: TEST_TIMEOUT,
      headers,
      validateStatus: () => true // Don't throw on non-2xx status
    })

    return {
      status: response.status,
      data: response.data,
      headers: response.headers
    }

  } catch (error) {
    return {
      status: error.response?.status || 500,
      data: error.response?.data || { error: error.message },
      error: error.message
    }
  }
}

/**
 * Run all security tests
 */
async function runAllSecurityTests() {
  console.log('🚀 Starting Webhook Security Tests')
  console.log('===================================\n')

  const results = {
    hmac: null,
    timestamp: null,
    idempotency: null,
    rateLimiting: null,
    overall: false
  }

  try {
    // Test HMAC validation
    results.hmac = await testHMACValidation()
    console.log('')

    // Test timestamp validation
    results.timestamp = await testTimestampValidation()
    console.log('')

    // Test idempotency protection
    results.idempotency = await testIdempotencyProtection()
    console.log('')

    // Test rate limiting
    results.rateLimiting = await testRateLimiting()
    console.log('')

    // Calculate overall success
    results.overall = results.hmac.success && 
                     results.timestamp.success && 
                     results.idempotency.success && 
                     results.rateLimiting.success

    // Print summary
    console.log('📊 SECURITY TEST RESULTS SUMMARY')
    console.log('=================================')
    console.log(`HMAC Validation: ${results.hmac.success ? '✅ PASS' : '❌ FAIL'}`)
    console.log(`Timestamp Validation: ${results.timestamp.success ? '✅ PASS' : '❌ FAIL'}`)
    console.log(`Idempotency Protection: ${results.idempotency.success ? '✅ PASS' : '❌ FAIL'}`)
    console.log(`Rate Limiting: ${results.rateLimiting.success ? '✅ PASS' : '❌ FAIL'}`)
    console.log(`Overall Security: ${results.overall ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`)

    // Print detailed test results
    console.log('\n📋 Detailed Test Results:')
    const allTests = [
      ...results.hmac.tests,
      ...results.timestamp.tests,
      ...results.idempotency.tests,
      ...results.rateLimiting.tests
    ]

    allTests.forEach(test => {
      console.log(`   ${test.passed ? '✅' : '❌'} ${test.name}: Expected ${test.expected}, Got ${test.actual}`)
    })

    return results

  } catch (error) {
    console.error('❌ Security test suite failed:', error.message)
    return { ...results, error: error.message }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllSecurityTests()
    .then((results) => {
      process.exit(results.overall ? 0 : 1)
    })
    .catch((error) => {
      console.error('❌ Security test execution failed:', error)
      process.exit(1)
    })
}

module.exports = { 
  runAllSecurityTests,
  testHMACValidation,
  testTimestampValidation,
  testIdempotencyProtection,
  testRateLimiting,
  sendWebhookWithSignature
}
