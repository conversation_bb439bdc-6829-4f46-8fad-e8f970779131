import { NextResponse } from 'next/server'
import { withRateLimit } from '@/app/lib/rateLimiting'
import { Logger } from '@/app/lib/monitoring'
import { 
  checkWebhookHealth, 
  sendAlert, 
  getAlertConfiguration, 
  updateAlertThresholds,
  runPeriodicHealthCheck 
} from '@/app/lib/webhookAlerting'

/**
 * GET /api/zoho/webhooks/alerts
 * Get current webhook health status and recent alerts
 */
async function handleAlertsGET(request) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action') || 'status'

    Logger.info('Webhook alerts request', {
      method: 'handleAlertsGET',
      action
    })

    switch (action) {
      case 'status':
        const healthResult = await checkWebhookHealth()
        return NextResponse.json({
          success: true,
          health: healthResult,
          timestamp: new Date().toISOString()
        })

      case 'config':
        const config = getAlertConfiguration()
        return NextResponse.json({
          success: true,
          configuration: config,
          timestamp: new Date().toISOString()
        })

      case 'check':
        const checkResult = await runPeriodicHealthCheck()
        return NextResponse.json({
          success: true,
          healthCheck: checkResult,
          timestamp: new Date().toISOString()
        })

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action',
          validActions: ['status', 'config', 'check'],
          timestamp: new Date().toISOString()
        }, { status: 400 })
    }

  } catch (error) {
    Logger.error('Webhook alerts GET failed', {
      method: 'handleAlertsGET',
      error: error.message,
      stack: error.stack
    })

    return NextResponse.json({
      success: false,
      error: 'Failed to process alerts request',
      message: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * POST /api/zoho/webhooks/alerts
 * Trigger manual health check or update alert configuration
 */
async function handleAlertsPOST(request) {
  try {
    const body = await request.json()
    const { action, data } = body

    Logger.info('Webhook alerts action requested', {
      method: 'handleAlertsPOST',
      action
    })

    switch (action) {
      case 'manual_check':
        const healthResult = await runPeriodicHealthCheck()
        return NextResponse.json({
          success: true,
          action: 'manual_check',
          result: healthResult,
          timestamp: new Date().toISOString()
        })

      case 'test_alert':
        const testAlert = {
          type: 'test_alert',
          severity: 'info',
          message: 'Test alert triggered manually',
          details: {
            triggeredBy: 'manual_test',
            timestamp: new Date().toISOString()
          },
          timestamp: new Date().toISOString()
        }

        const alertResult = await sendAlert(testAlert)
        return NextResponse.json({
          success: true,
          action: 'test_alert',
          alert: testAlert,
          result: alertResult,
          timestamp: new Date().toISOString()
        })

      case 'update_thresholds':
        if (!data || !data.thresholds) {
          return NextResponse.json({
            success: false,
            error: 'Missing thresholds data',
            timestamp: new Date().toISOString()
          }, { status: 400 })
        }

        const updatedThresholds = updateAlertThresholds(data.thresholds)
        return NextResponse.json({
          success: true,
          action: 'update_thresholds',
          thresholds: updatedThresholds,
          timestamp: new Date().toISOString()
        })

      case 'send_custom_alert':
        if (!data || !data.alert) {
          return NextResponse.json({
            success: false,
            error: 'Missing alert data',
            timestamp: new Date().toISOString()
          }, { status: 400 })
        }

        const customAlert = {
          ...data.alert,
          timestamp: new Date().toISOString()
        }

        const customAlertResult = await sendAlert(customAlert)
        return NextResponse.json({
          success: true,
          action: 'send_custom_alert',
          alert: customAlert,
          result: customAlertResult,
          timestamp: new Date().toISOString()
        })

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action',
          validActions: ['manual_check', 'test_alert', 'update_thresholds', 'send_custom_alert'],
          timestamp: new Date().toISOString()
        }, { status: 400 })
    }

  } catch (error) {
    Logger.error('Webhook alerts POST failed', {
      method: 'handleAlertsPOST',
      error: error.message,
      stack: error.stack
    })

    return NextResponse.json({
      success: false,
      error: 'Failed to process alerts action',
      message: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

// Apply rate limiting
export const GET = withRateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  maxRequests: 30, // Allow frequent health checks
  message: 'Too many alert requests. Please wait before trying again.',
})(handleAlertsGET)

export const POST = withRateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  maxRequests: 10, // Limit alert actions
  message: 'Too many alert actions. Please wait before trying again.',
})(handleAlertsPOST)
