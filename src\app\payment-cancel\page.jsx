'use client'

import { useRouter, useSearchParams } from 'next/navigation'
import { Suspense, useEffect } from 'react'

function PaymentCancelPageContent() {
  const searchParams = useSearchParams()
  const router = useRouter()

  const invoiceNumber = searchParams.get('invoice')

  useEffect(() => {
    // Auto-redirect after 5 seconds
    const timer = setTimeout(() => {
      router.push('/aquapartner/billingAndPayments')
    }, 5000)

    return () => clearTimeout(timer)
  }, [router])

  const handleRetryPayment = () => {
    router.push('/aquapartner/billingAndPayments')
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <div className="rounded-lg bg-white p-8 shadow-lg">
          {/* Cancel Icon */}
          <div className="text-center">
            <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-yellow-100">
              <svg className="h-8 w-8 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>

            <h2 className="mt-6 text-2xl font-bold text-gray-900">Payment Cancelled</h2>
            <p className="mt-2 text-sm text-gray-600">You have cancelled the payment process.</p>
          </div>

          {/* Payment Details */}
          <div className="mt-8 border-t border-gray-200 pt-6">
            <dl className="space-y-4">
              {invoiceNumber && (
                <div className="flex justify-between">
                  <dt className="text-sm font-medium text-gray-500">Invoice Number</dt>
                  <dd className="text-sm text-gray-900">{invoiceNumber}</dd>
                </div>
              )}

              <div className="flex justify-between">
                <dt className="text-sm font-medium text-gray-500">Cancellation Date</dt>
                <dd className="text-sm text-gray-900">{new Date().toLocaleDateString()}</dd>
              </div>

              <div className="flex justify-between">
                <dt className="text-sm font-medium text-gray-500">Status</dt>
                <dd className="text-sm">
                  <span className="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800">
                    Cancelled
                  </span>
                </dd>
              </div>
            </dl>
          </div>

          {/* Action Buttons */}
          <div className="mt-8 space-y-3">
            <button
              onClick={handleRetryPayment}
              className="flex w-full justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Try Payment Again
            </button>
          </div>

          {/* Auto-redirect Notice */}
          <div className="mt-6 text-center">
            <p className="text-xs text-gray-500">You will be automatically redirected to invoices in 5 seconds</p>
          </div>
        </div>

        {/* Information */}
        <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">No Charges Applied</h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>
                  Since you cancelled the payment, no charges have been applied to your account. Your invoice remains
                  unpaid and you can retry the payment anytime.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function PaymentCancelPage() {
  return (
    <Suspense
      fallback={
        <div className="flex min-h-screen items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="mx-auto h-12 w-12 animate-spin rounded-full border-b-2 border-yellow-600"></div>
            <p className="mt-4 text-gray-600">Loading...</p>
          </div>
        </div>
      }
    >
      <PaymentCancelPageContent />
    </Suspense>
  )
}
