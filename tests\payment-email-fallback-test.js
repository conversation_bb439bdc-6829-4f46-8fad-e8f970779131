/**
 * Test script to validate the payment email fallback fixes
 * This script tests the customer email handling and payment links strategy
 */

const axios = require('axios')

// Configuration
const BASE_URL = process.env.BASE_URL || 'http://localhost:3000'
const TEST_CUSTOMER_ID = '401088000053137251'
const TEST_INVOICE_NUMBER = 'AP-RI-2526-00142'
const TEST_AMOUNT = 600.10

console.log('🧪 PAYMENT EMAIL FALLBACK TEST')
console.log('================================')
console.log(`Base URL: ${BASE_URL}`)
console.log(`Customer ID: ${TEST_CUSTOMER_ID}`)
console.log(`Invoice: ${TEST_INVOICE_NUMBER}`)
console.log(`Amount: ₹${TEST_AMOUNT}`)
console.log('')

async function testPaymentEmailFallback() {
  try {
    console.log('📧 TEST 1: Payment creation with missing customer email')
    console.log('------------------------------------------------------')
    
    // Test payment creation with missing customer_email
    const paymentData = {
      amount: TEST_AMOUNT,
      currency: 'INR',
      customer_id: TEST_CUSTOMER_ID,
      customer_name: 'SRI DEVI AQUA FEEDS & NEEDS',
      customer_email: null, // Intentionally null to test fallback
      customer_phone: '+919999999999',
      invoice_number: TEST_INVOICE_NUMBER,
      description: `Payment for AquaPartner Invoice ${TEST_INVOICE_NUMBER}`,
      redirect_url: `${BASE_URL}/payment-success?invoice=${TEST_INVOICE_NUMBER}`,
      reference_id: `${TEST_CUSTOMER_ID}_${TEST_INVOICE_NUMBER}_${Date.now()}`,
    }

    console.log('📡 Sending payment request with null email...')
    console.log('Request data:', JSON.stringify(paymentData, null, 2))

    const response = await axios.post(`${BASE_URL}/api/zoho/payments/create-payment`, paymentData, {
      headers: {
        'Content-Type': 'application/json',
        'X-Force-Payment-Method': 'links', // Force payment links strategy
      },
    })

    console.log('✅ Response Status:', response.status)
    console.log('✅ Response Data:', JSON.stringify(response.data, null, 2))

    // Validate response
    if (response.status === 201) {
      console.log('✅ SUCCESS: Payment created successfully with 201 status')
      
      if (response.data.payment_method === 'payment_link') {
        console.log('✅ SUCCESS: Payment links strategy was used')
      } else {
        console.log('⚠️  WARNING: Expected payment_link method, got:', response.data.payment_method)
      }

      if (response.data.data && response.data.data.payment_link_url) {
        console.log('✅ SUCCESS: Payment link URL generated:', response.data.data.payment_link_url)
      } else {
        console.log('❌ ERROR: No payment link URL in response')
      }
    } else {
      console.log('❌ ERROR: Expected 201 status, got:', response.status)
    }

  } catch (error) {
    console.error('❌ TEST FAILED:', error.message)
    if (error.response) {
      console.error('Response Status:', error.response.status)
      console.error('Response Data:', error.response.data)
    }
  }
}

async function testPaymentEmailWithValue() {
  try {
    console.log('')
    console.log('📧 TEST 2: Payment creation with valid customer email')
    console.log('----------------------------------------------------')
    
    // Test payment creation with valid customer_email
    const paymentData = {
      amount: TEST_AMOUNT,
      currency: 'INR',
      customer_id: TEST_CUSTOMER_ID,
      customer_name: 'SRI DEVI AQUA FEEDS & NEEDS',
      customer_email: '<EMAIL>', // Valid email
      customer_phone: '+919999999999',
      invoice_number: TEST_INVOICE_NUMBER + '_2',
      description: `Payment for AquaPartner Invoice ${TEST_INVOICE_NUMBER}_2`,
      redirect_url: `${BASE_URL}/payment-success?invoice=${TEST_INVOICE_NUMBER}_2`,
      reference_id: `${TEST_CUSTOMER_ID}_${TEST_INVOICE_NUMBER}_2_${Date.now()}`,
    }

    console.log('📡 Sending payment request with valid email...')

    const response = await axios.post(`${BASE_URL}/api/zoho/payments/create-payment`, paymentData, {
      headers: {
        'Content-Type': 'application/json',
        'X-Force-Payment-Method': 'links', // Force payment links strategy
      },
    })

    console.log('✅ Response Status:', response.status)
    console.log('✅ Response Data:', JSON.stringify(response.data, null, 2))

    // Validate response
    if (response.status === 201) {
      console.log('✅ SUCCESS: Payment created successfully with 201 status')
    } else {
      console.log('❌ ERROR: Expected 201 status, got:', response.status)
    }

  } catch (error) {
    console.error('❌ TEST FAILED:', error.message)
    if (error.response) {
      console.error('Response Status:', error.response.status)
      console.error('Response Data:', error.response.data)
    }
  }
}

async function runTests() {
  console.log('🚀 Starting payment email fallback tests...')
  console.log('')
  
  await testPaymentEmailFallback()
  await testPaymentEmailWithValue()
  
  console.log('')
  console.log('🏁 Test execution completed!')
  console.log('')
  console.log('📋 SUMMARY:')
  console.log('- Test 1: Validates email fallback logic when customer_email is null')
  console.log('- Test 2: Validates normal flow with valid customer_email')
  console.log('- Both tests force payment links strategy for consistency')
  console.log('')
  console.log('✅ If both tests pass with 201 status, the fixes are working correctly!')
}

// Run the tests
runTests().catch(console.error)
