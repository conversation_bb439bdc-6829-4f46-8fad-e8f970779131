'use client'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/table'
import AddFarmerPopup from '@/customComponents/AddFarmerPopup'

import { retailerAtom } from '@/customComponents/providers'
import { useFetchFarmersVisitsByCustomerId } from '@/utils/requests'
import { useAtomValue } from 'jotai'
import moment from 'moment'
import { useState } from 'react'
import { FarmerDetailView } from './components/farmerDetailsView'

/**
 * Utility functions for safe data access and formatting
 * Implements defensive programming practices for production environments
 */

/**
 * Safely gets the last visit from farmer visits array
 * @param {Array|undefined|null} visits - Array of farmer visits
 * @returns {Object|null} Last visit object or null if not available
 */
const getLastVisit = (visits) => {
  if (!Array.isArray(visits) || visits.length === 0) {
    return null
  }
  return visits[visits.length - 1] || null
}

/**
 * Safely gets the first visit from farmer visits array
 * @param {Array|undefined|null} visits - Array of farmer visits
 * @returns {Object|null} First visit object or null if not available
 */
const getFirstVisit = (visits) => {
  if (!Array.isArray(visits) || visits.length === 0) {
    return null
  }
  return visits[0] || null
}

/**
 * Safely extracts and formats address from visit data
 * @param {Object|null} visit - Visit object
 * @returns {string} Formatted address or fallback text
 */
const getFormattedAddress = (visit) => {
  if (!visit || typeof visit !== 'object') {
    return 'Address not available'
  }

  const { shortAddress } = visit

  if (typeof shortAddress === 'string' && shortAddress.trim()) {
    return shortAddress.trim()
  }

  // Fallback to other address fields if shortAddress is not available
  if (typeof visit.address === 'string' && visit.address.trim()) {
    return visit.address.trim()
  }

  if (typeof visit.location === 'string' && visit.location.trim()) {
    return visit.location.trim()
  }

  return 'Address not available'
}

/**
 * Safely formats visit date with fallback handling
 * @param {Object|null} visit - Visit object
 * @param {string} format - Moment.js format string
 * @returns {string} Formatted date or fallback text
 */
const getFormattedVisitDate = (visit, format = 'DD-MM-YYYY') => {
  if (!visit || typeof visit !== 'object') {
    return 'Date not available'
  }

  const { visitedDate } = visit

  if (!visitedDate) {
    return 'Date not available'
  }

  try {
    const momentDate = moment(visitedDate)
    if (momentDate.isValid()) {
      return momentDate.format(format)
    }
  } catch (error) {
    console.warn('Error formatting visit date:', error)
  }

  return 'Invalid date'
}

/**
 * Validates farmer data structure
 * @param {Object} farmer - Farmer object
 * @returns {boolean} True if farmer has minimum required data
 */
const isValidFarmer = (farmer) => {
  return farmer && typeof farmer === 'object' && typeof farmer.name === 'string' && farmer.name.trim().length > 0
}

export default function MyFarmers() {
  const customer = useAtomValue(retailerAtom)
  const farmersVisits = useFetchFarmersVisitsByCustomerId(customer.customerId)

  const [selectedFarmer, setSelectedFarmer] = useState(null)
  const [isPopupOpen, setIsPopupOpen] = useState(false)
  const [showFarmerDetails, setShowFarmerDetails] = useState(false)

  /**
   * Safely handles farmer selection with validation
   * @param {string} farmerName - Name of the farmer to select
   */
  const farmerClicked = (farmerName) => {
    try {
      if (!farmerName || typeof farmerName !== 'string') {
        console.warn('Invalid farmer name provided:', farmerName)
        return
      }

      // Safely access farmers data with validation
      const farmersData = farmersVisits?.results
      if (!Array.isArray(farmersData)) {
        console.warn('Farmers data is not available or invalid')
        return
      }

      const farmer = farmersData.find((farmer) => isValidFarmer(farmer) && farmer.name === farmerName)

      if (farmer) {
        setSelectedFarmer(farmer)
        setShowFarmerDetails(true)
      } else {
        console.warn('Farmer not found or invalid:', farmerName)
      }
    } catch (error) {
      console.error('Error selecting farmer:', error)
    }
  }

  /**
   * Safely gets the list of valid farmers with error handling
   * @returns {Array} Array of valid farmer objects
   */
  const getValidFarmers = () => {
    try {
      const farmersData = farmersVisits?.results
      if (!Array.isArray(farmersData)) {
        return []
      }

      return farmersData.filter(isValidFarmer)
    } catch (error) {
      console.error('Error processing farmers data:', error)
      return []
    }
  }

  const handleOpenPopup = () => setIsPopupOpen(true)
  const handleClosePopup = () => setIsPopupOpen(false)
  const handleEmailSubmit = (email) => {
    console.log('User email:', email)
    setIsPopupOpen(false)
  }

  /**
   * Safely checks if data is loading
   * @returns {boolean} True if data is still loading
   */
  const isLoading = () => {
    return !farmersVisits || farmersVisits.loading === true
  }

  /**
   * Safely checks if there's an error in data loading
   * @returns {boolean} True if there's an error
   */
  const hasError = () => {
    return farmersVisits?.error || false
  }

  /**
   * Gets error message safely
   * @returns {string} Error message or default message
   */
  const getErrorMessage = () => {
    if (typeof farmersVisits?.error === 'string') {
      return farmersVisits.error
    }
    if (farmersVisits?.error?.message) {
      return farmersVisits.error.message
    }
    return 'Failed to load farmers data. Please try again.'
  }

  // Show loading state
  if (isLoading()) {
    return (
      <div className="flex min-h-screen flex-row overflow-y-auto scroll-smooth">
        <div className="flex w-screen flex-col">
          <div className="flex flex-wrap items-end justify-between gap-4">
            <div className="mt-2 pb-6 align-baseline sm:flex sm:justify-between">
              <div className="flex flex-col sm:flex-row">
                <p className="text-2xl font-bold">My Farmers</p>
              </div>
            </div>
          </div>
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="mx-auto h-12 w-12 animate-spin rounded-full border-b-2 border-indigo-600"></div>
              <p className="mt-4 text-zinc-500">Loading farmers data...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Show error state
  if (hasError()) {
    return (
      <div className="flex min-h-screen flex-row overflow-y-auto scroll-smooth">
        <div className="flex w-screen flex-col">
          <div className="flex flex-wrap items-end justify-between gap-4">
            <div className="mt-2 pb-6 align-baseline sm:flex sm:justify-between">
              <div className="flex flex-col sm:flex-row">
                <p className="text-2xl font-bold">My Farmers</p>
              </div>
            </div>
          </div>
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="mb-4 text-6xl text-red-500">⚠️</div>
              <h3 className="mb-2 text-lg font-semibold text-zinc-900">Error Loading Data</h3>
              <p className="mb-4 text-zinc-500">{getErrorMessage()}</p>
              <button
                onClick={() => window.location.reload()}
                className="rounded-md bg-indigo-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-row overflow-y-auto scroll-smooth">
      <div className="flex w-screen flex-col">
        <div className="flex flex-wrap items-end justify-between gap-4">
          <div className="mt-2 pb-6 align-baseline sm:flex sm:justify-between">
            <div className="flex flex-col sm:flex-row">
              <p className="text-2xl font-bold">My Farmers</p>
            </div>
          </div>
          {/* <button
            type="button"
            onClick={handleOpenPopup}
            className="rounded-md bg-indigo-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          >
            Add Farmer
          </button> */}
        </div>

        {/* <ul name="farmersList" className="mt-0 flex-1">
          {farmersVisits.results &&
            farmersVisits.results.map((farmer, index) => {
              // Safely get the correct farmer image
              const firstVisit = getFirstVisit(farmer.visits)
              const lastVisit = getLastVisit(farmer.visits)
              const farmerImage =
                firstVisit?.image ||
                lastVisit?.image ||
                `https://placehold.co/400x600/png?font=roboto&text=${farmer.name[0]}`

              return (
                <li key={farmer.name}>
                  {index > 0 && <Divider soft={index > 0} />}
                  <div className="flex items-center justify-between">
                    <div className="flex gap-6 py-6">
                      <div className="w-32 shrink-0">
                        <img
                          className="rotate-270 scale-59 h-32 w-32 rounded-full shadow"
                          src={farmerImage}
                          alt={`${farmer.name}'s farm visit image`}
                        />
                     
                      </div>
                      <div className="space-y-1.5">
                        <div className="text-base/6 font-semibold">
                          <button className="text-start uppercase" name={farmer.name} onClick={farmerClicked}>
                            {farmer.name}
                          </button>
                        </div>
                        <div className="text-xs/6 text-zinc-500">
                          {getFormattedAddress(getLastVisit(farmer.visits))}
                        </div>
                        <div className="text-xs/6 text-zinc-600">
                          {'Last visited on '}
                          {getFormattedVisitDate(getFirstVisit(farmer.visits))}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <Badge className="max-sm:hidden" color={farmer.farmType === 'Own' ? 'lime' : 'zinc'}>
                        {farmer.farmType}
                      </Badge>
                    </div>
                  </div>
                </li>
              )
            })}
        </ul> */}

        {/* Popup for adding a farmer */}
        <div className="flex justify-center">
          {isPopupOpen && (
            <AddFarmerPopup
              open={isPopupOpen}
              setOpen={setIsPopupOpen}
              onClose={handleClosePopup}
              onSubmit={handleEmailSubmit}
            />
          )}

          {/* Farmer detail view */}
          {showFarmerDetails && (
            <FarmerDetailView open={showFarmerDetails} setOpen={setShowFarmerDetails} farmer={selectedFarmer} />
          )}
        </div>
        <div className="overflow-x-auto">
          <Table className="w-full [--gutter:theme(spacing.6)] lg:[--gutter:theme(spacing.10)]">
            <TableHead>
              <TableRow>
                <TableHeader>Farmer</TableHeader>
                <TableHeader>Address</TableHeader>
                <TableHeader>Last Visit Date</TableHeader>
              </TableRow>
            </TableHead>
            <TableBody>
              {getValidFarmers().map((farmer, index) => {
                // Safely get farmer image with fallbacks
                const firstVisit = getFirstVisit(farmer.visits)
                const lastVisit = getLastVisit(farmer.visits)
                const farmerImage =
                  firstVisit?.image ||
                  lastVisit?.image ||
                  `https://placehold.co/400x600/png?font=roboto&text=${farmer.name[0]}`

                // Safely get address and visit date
                const lastVisitAddress = getFormattedAddress(lastVisit)
                const firstVisitDate = getFormattedVisitDate(firstVisit)

                return (
                  <TableRow
                    key={`farmer-${farmer.name}-${index}`}
                    href={'#'}
                    title={`farmer #${farmer.name}`}
                    name={farmer.name}
                    onClick={() => farmerClicked(farmer.name)}
                  >
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <img
                          src={farmerImage}
                          alt={`${farmer.name} profile image`}
                          className="h-6 w-6 rounded-full object-cover"
                          onError={(e) => {
                            // Fallback image on load error
                            e.target.src = `https://placehold.co/400x600/png?font=roboto&text=${farmer.name[0]}`
                          }}
                        />
                        <span>{farmer.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>{lastVisitAddress}</TableCell>
                    <TableCell className="text-zinc-500">{firstVisitDate}</TableCell>
                  </TableRow>
                )
              })}
              {getValidFarmers().length === 0 && (
                <TableRow>
                  <TableCell colSpan={3} className="py-8 text-center text-zinc-500">
                    <div>
                      <p>No farmers data available</p>
                      <p className="mt-2 text-sm">Please check your connection and try again</p>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  )
}
