import connectedDB from '@/app/config/database'
import PaymentAccessToken from '@/app/models/PaymentAccessToken'
import PaymentTransaction from '@/app/models/PaymentTransaction'
import axios from 'axios'

/**
 * Enhanced axios instance with timeout and retry configuration
 */
const createAxiosInstance = () => {
  const instance = axios.create({
    timeout: parseInt(process.env.API_TIMEOUT_MS) || 30000, // 30 seconds default
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'AquaPartner-Payment-Service/1.0',
    },
  })

  // Request interceptor for logging
  instance.interceptors.request.use(
    (config) => {
      console.log(`🌐 API REQUEST: ${config.method?.toUpperCase()} ${config.url}`, {
        method: 'apiRequest',
        url: config.url,
        timeout: config.timeout,
      })
      return config
    },
    (error) => {
      console.error('Request interceptor error:', error)
      return Promise.reject(error)
    }
  )

  // Response interceptor for logging and error handling
  instance.interceptors.response.use(
    (response) => {
      console.log(`✅ API RESPONSE: ${response.status} ${response.config.url}`, {
        method: 'apiResponse',
        status: response.status,
        url: response.config.url,
        duration: response.config.metadata?.duration || 'unknown',
      })
      return response
    },
    (error) => {
      const config = error.config
      const status = error.response?.status
      const message = error.response?.data?.message || error.message

      console.error(`❌ API ERROR: ${status || 'NETWORK'} ${config?.url}`, {
        method: 'apiError',
        status,
        url: config?.url,
        message,
        code: error.code,
        isTimeout: error.code === 'ECONNABORTED',
      })

      return Promise.reject(error)
    }
  )

  return instance
}

/**
 * Retry mechanism with exponential backoff
 */
async function withRetry(operation, maxRetries = 3, baseDelay = 1000) {
  let lastError

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const startTime = Date.now()
      const result = await operation()
      const duration = Date.now() - startTime

      console.log(`✅ Operation succeeded on attempt ${attempt}/${maxRetries} (${duration}ms)`)
      return result
    } catch (error) {
      lastError = error

      // Don't retry on certain errors
      if (error.response?.status === 401 || error.response?.status === 403) {
        console.error(`🚫 Non-retryable error (${error.response.status}), not retrying`)
        throw error
      }

      if (attempt === maxRetries) {
        console.error(`❌ Operation failed after ${maxRetries} attempts`)
        break
      }

      // Calculate delay with exponential backoff
      const delay = baseDelay * Math.pow(2, attempt - 1)
      console.warn(`⏳ Attempt ${attempt}/${maxRetries} failed, retrying in ${delay}ms`, {
        method: 'retryAttempt',
        attempt,
        maxRetries,
        delay,
        error: error.message,
        isTimeout: error.code === 'ECONNABORTED',
      })

      await new Promise((resolve) => setTimeout(resolve, delay))
    }
  }

  throw lastError
}

class ZohoPaymentService {
  constructor() {
    this.baseURL = 'https://payments.zoho.in/api/v1'
    this.authURL = 'https://accounts.zoho.in/oauth/v2'
    this.accountId = process.env.ZOHO_PAY_ACCOUNT_ID
    this.axiosInstance = createAxiosInstance()
    this.maxRetries = parseInt(process.env.MAX_RETRY_ATTEMPTS) || 3
  }

  /**
   * Get valid access token using direct OAuth refresh (POC approach)
   * Falls back to database token if OAuth refresh fails
   */
  async getValidAccessToken() {
    try {
      // Try direct OAuth token refresh first (POC approach)
      const accessToken = await this.refreshAccessTokenDirect()
      if (accessToken) {
        return accessToken
      }
    } catch (error) {
      console.warn('Direct OAuth refresh failed, falling back to database token:', error.message)
    }

    // Fallback to database-stored token
    await connectedDB()
    const result = await PaymentAccessToken.findOne({})

    if (!result || !result.access_token) {
      throw new Error('No Zoho Payment token found. Please check token management service or OAuth configuration.')
    }

    return result.access_token
  }

  /**
   * Direct OAuth token refresh (POC approach) with retry logic
   */
  async refreshAccessTokenDirect() {
    const {
      ZOHO_OAUTH_GENERATE_ACCESS_TOKEN_URL,
      ZOHO_OAUTH_REFRESH_TOKEN,
      ZOHO_OAUTH_CLIENT_ID,
      ZOHO_OAUTH_CLIENT_SECRET,
      ZOHO_OAUTH_REDIRECT_URI,
    } = process.env

    if (
      !ZOHO_OAUTH_GENERATE_ACCESS_TOKEN_URL ||
      !ZOHO_OAUTH_REFRESH_TOKEN ||
      !ZOHO_OAUTH_CLIENT_ID ||
      !ZOHO_OAUTH_CLIENT_SECRET ||
      !ZOHO_OAUTH_REDIRECT_URI
    ) {
      throw new Error('Missing OAuth environment variables for direct token refresh')
    }

    return await withRetry(async () => {
      const getTokenUrl = `${ZOHO_OAUTH_GENERATE_ACCESS_TOKEN_URL}?refresh_token=${ZOHO_OAUTH_REFRESH_TOKEN}&client_id=${ZOHO_OAUTH_CLIENT_ID}&client_secret=${ZOHO_OAUTH_CLIENT_SECRET}&redirect_uri=${ZOHO_OAUTH_REDIRECT_URI}&grant_type=refresh_token`

      const response = await this.axiosInstance.post(getTokenUrl)

      if (response.status === 200 && response?.data?.access_token) {
        console.log('Direct OAuth token refresh successful')
        return response.data.access_token
      } else {
        throw new Error(`Invalid OAuth response: ${JSON.stringify(response.data)}`)
      }
    }, this.maxRetries)
  }

  /**
   * Create payment session
   */
  async createPaymentSession(paymentData) {
    try {
      const accessToken = await this.getValidAccessToken()

      const {
        amount,
        currency = 'INR',
        description,
        invoice_number,
        customer_id,
        customer_name,
        customer_email,
        customer_phone,
        redirect_url,
        reference_id,
        meta_data = [],
      } = paymentData

      // Validate required fields
      if (!amount || !description || !invoice_number || !customer_id) {
        throw new Error('Missing required fields: amount, description, invoice_number, customer_id')
      }

      // Construct webhook URL for payment notifications
      const webhookUrl = `${process.env.NEXT_PUBLIC_DOMAIN}/api/zoho/webhooks/payment`

      const sessionPayload = {
        amount: parseFloat(amount),
        currency,
        description,
        invoice_number,
        webhook_url: webhookUrl,
        meta_data: [
          { key: 'customer_id', value: customer_id },
          { key: 'invoice_number', value: invoice_number },
          ...meta_data,
        ],
      }

      // Use POC approach with configurable payment session URL and retry logic
      const paymentSessionUrl = process.env.ZOHO_PAYMENT_SESSION_URL || `${this.baseURL}/paymentsessions`

      const response = await withRetry(async () => {
        return await this.axiosInstance.post(`${paymentSessionUrl}?account_id=${this.accountId}`, sessionPayload, {
          headers: {
            Authorization: `Zoho-oauthtoken ${accessToken}`,
            'Content-Type': 'application/json',
          },
        })
      }, this.maxRetries)

      if (response.data.code !== 0) {
        throw new Error(`Zoho API Error: ${response.data.message}`)
      }

      const paymentSession = response.data.payments_session

      // Save transaction to database
      await connectedDB()
      const transaction = new PaymentTransaction({
        payments_session_id: paymentSession.payments_session_id,
        amount: parseFloat(amount),
        currency,
        description,
        invoice_number,
        customer_id,
        customer_name,
        customer_email,
        customer_phone,
        status: 'created',
        meta_data: sessionPayload.meta_data,
        redirect_url,
        reference_id,
        session_created_time: new Date(paymentSession.created_time * 1000),
        session_expires_at: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes
      })

      await transaction.save()

      return {
        success: true,
        payment_session: paymentSession,
        transaction_id: transaction._id,
      }
    } catch (error) {
      console.error('Error creating payment session:', error.response?.data || error.message)
      throw error
    }
  }

  /**
   * Create payment link using Zoho Payment Links API
   * Alternative to payment sessions for direct redirect flow
   */
  async createPaymentLink(paymentData) {
    try {
      const accessToken = await this.getValidAccessToken()

      const {
        amount,
        currency = 'INR',
        description,
        invoice_number,
        customer_id,
        customer_name,
        customer_email,
        customer_phone,
        redirect_url,
        reference_id,
        expires_at,
        notify_user = false,
        meta_data = [],
      } = paymentData

      // Enhanced email handling with fallback logic
      const finalCustomerEmail = customer_email || `${customer_id}@aquapartner.com`

      console.log('📧 PAYMENT LINK EMAIL HANDLING:', {
        original: customer_email,
        final: finalCustomerEmail,
        customer_id,
      })

      // Validate required fields for Payment Links API (using final email)
      if (!amount || !description || !finalCustomerEmail) {
        throw new Error(
          'Missing required fields for Payment Links: amount, description, customer_email (after fallback)'
        )
      }

      // Construct webhook URL for payment notifications
      const webhookUrl = `${process.env.NEXT_PUBLIC_DOMAIN}/api/zoho/webhooks/payment`

      // Map our payment data to Zoho Payment Links API format
      const paymentLinkPayload = {
        amount: parseFloat(amount),
        currency,
        email: finalCustomerEmail, // Use the final email with fallback
        description,
        reference_id: reference_id || invoice_number || `${customer_id}_${Date.now()}`,
        notify_user,
        webhook_url: webhookUrl,
      }

      // Add optional fields if provided
      if (customer_phone) {
        paymentLinkPayload.phone = customer_phone
      }

      if (redirect_url) {
        paymentLinkPayload.return_url = redirect_url
      }

      if (expires_at) {
        paymentLinkPayload.expires_at = expires_at
      }

      console.log('🔗 PAYMENT LINK: Creating payment link with payload:', paymentLinkPayload)

      // Use Zoho Payment Links API endpoint with retry logic
      const paymentLinksUrl = `${this.baseURL}/paymentlinks`

      const response = await withRetry(async () => {
        return await this.axiosInstance.post(`${paymentLinksUrl}?account_id=${this.accountId}`, paymentLinkPayload, {
          headers: {
            Authorization: `Zoho-oauthtoken ${accessToken}`,
            'Content-Type': 'application/json',
          },
        })
      }, this.maxRetries)

      console.log('🔗 PAYMENT LINK: API Response:', response.data)

      if (response.data.code !== 0) {
        throw new Error(`Zoho Payment Links API Error: ${response.data.message}`)
      }

      const paymentLink = response.data.payment_links

      // Save payment link transaction to database
      await connectedDB()
      const transaction = new PaymentTransaction({
        payment_link_id: paymentLink.payment_link_id,
        payment_link_url: paymentLink.url,
        amount: parseFloat(amount),
        currency,
        description,
        invoice_number,
        customer_id,
        customer_name,
        customer_email: finalCustomerEmail, // Use the final email with fallback
        customer_phone,
        status: 'created',
        payment_method: 'payment_link',
        meta_data: [
          { key: 'customer_id', value: customer_id },
          { key: 'invoice_number', value: invoice_number },
          { key: 'payment_link_id', value: paymentLink.payment_link_id },
          ...meta_data,
        ],
        redirect_url,
        reference_id: paymentLink.reference_id,
        session_created_time: new Date(paymentLink.created_time * 1000),
        session_expires_at: paymentLink.expires_at
          ? new Date(paymentLink.expires_at)
          : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days default
        // Use a unique identifier for payment links to avoid index conflicts
        payments_session_id: `payment_link_${paymentLink.payment_link_id}`,
      })

      await transaction.save()

      console.log('🔗 PAYMENT LINK: Transaction saved:', transaction._id)

      return {
        success: true,
        payment_link: paymentLink,
        payment_link_url: paymentLink.url,
        payment_link_id: paymentLink.payment_link_id,
        transaction_id: transaction._id,
      }
    } catch (error) {
      console.error('❌ PAYMENT LINK ERROR: Failed to create payment link:', error.response?.data || error.message)
      throw error
    }
  }

  /**
   * Retrieve payment link details
   */
  async getPaymentLink(paymentLinkId) {
    try {
      const accessToken = await this.getValidAccessToken()

      const response = await withRetry(async () => {
        return await this.axiosInstance.get(
          `${this.baseURL}/paymentlinks/${paymentLinkId}?account_id=${this.accountId}`,
          {
            headers: {
              Authorization: `Zoho-oauthtoken ${accessToken}`,
            },
          }
        )
      }, this.maxRetries)

      if (response.data.code !== 0) {
        throw new Error(`Zoho Payment Links API Error: ${response.data.message}`)
      }

      return response.data.payment_links
    } catch (error) {
      console.error('❌ PAYMENT LINK ERROR: Failed to retrieve payment link:', error.response?.data || error.message)
      throw error
    }
  }

  /**
   * Determine payment method based on configuration and environment
   */
  getPaymentMethod(userAgent = '') {
    const configMethod = process.env.ZOHO_PAYMENT_METHOD || 'auto'
    const isWebView = /wv|WebView/i.test(userAgent)
    const isMobile = /Mobile|Android|iPhone|iPad/i.test(userAgent)

    console.log('🔧 PAYMENT METHOD: Configuration:', configMethod)
    console.log('🔧 PAYMENT METHOD: User Agent:', userAgent)
    console.log('🔧 PAYMENT METHOD: Is WebView:', isWebView)
    console.log('🔧 PAYMENT METHOD: Is Mobile:', isMobile)

    switch (configMethod) {
      case 'widget':
        return 'widget'
      case 'links':
        return 'links'
      case 'auto':
      default:
        // Auto-detection logic:
        // - Mobile WebView: Always use payment links (simpler redirect flow)
        // - Desktop/Web: Use widget for better UX
        // - Mobile browsers: Use payment links for better mobile experience
        if (isWebView || isMobile) {
          console.log('🔧 PAYMENT METHOD: Auto-selected payment links for mobile/WebView')
          return 'links'
        } else {
          console.log('🔧 PAYMENT METHOD: Auto-selected widget for desktop')
          return 'widget'
        }
    }
  }

  /**
   * Create payment with automatic method selection and fallback
   */
  async createPayment(paymentData, userAgent = '') {
    const paymentMethod = this.getPaymentMethod(userAgent)

    console.log('💳 PAYMENT: Using method:', paymentMethod)

    try {
      if (paymentMethod === 'links') {
        return await this.createPaymentLink(paymentData)
      } else {
        return await this.createPaymentSession(paymentData)
      }
    } catch (error) {
      console.error(`❌ PAYMENT: ${paymentMethod} method failed:`, error.message)

      // Fallback logic: if widget fails, try payment links
      if (paymentMethod === 'widget') {
        console.log('🔄 PAYMENT: Falling back to payment links...')
        try {
          const fallbackResult = await this.createPaymentLink(paymentData)
          console.log('✅ PAYMENT: Fallback to payment links successful')
          return {
            ...fallbackResult,
            fallback_used: true,
            original_method: 'widget',
            final_method: 'links',
          }
        } catch (fallbackError) {
          console.error('❌ PAYMENT: Fallback also failed:', fallbackError.message)
          throw new Error(`Both payment methods failed. Widget: ${error.message}, Links: ${fallbackError.message}`)
        }
      } else {
        // If payment links fail, don't fallback to widget as it requires different integration
        throw error
      }
    }
  }

  /**
   * Retrieve payment session details
   */
  async getPaymentSession(paymentSessionId) {
    try {
      const accessToken = await this.getValidAccessToken()

      const response = await withRetry(async () => {
        return await this.axiosInstance.get(
          `${this.baseURL}/paymentsessions/${paymentSessionId}?account_id=${this.accountId}`,
          {
            headers: {
              Authorization: `Zoho-oauthtoken ${accessToken}`,
            },
          }
        )
      }, this.maxRetries)

      if (response.data.code !== 0) {
        throw new Error(`Zoho API Error: ${response.data.message}`)
      }

      return response.data.payments_session
    } catch (error) {
      console.error('Error retrieving payment session:', error.response?.data || error.message)
      throw error
    }
  }

  /**
   * Get payment details
   */
  async getPayment(paymentId) {
    try {
      const accessToken = await this.getValidAccessToken()

      const response = await withRetry(async () => {
        return await this.axiosInstance.get(`${this.baseURL}/payments/${paymentId}?account_id=${this.accountId}`, {
          headers: {
            Authorization: `Zoho-oauthtoken ${accessToken}`,
          },
        })
      }, this.maxRetries)

      if (response.data.code !== 0) {
        throw new Error(`Zoho API Error: ${response.data.message}`)
      }

      return response.data.payment
    } catch (error) {
      console.error('Error retrieving payment:', error.response?.data || error.message)
      throw error
    }
  }

  /**
   * Create refund
   */
  async createRefund(paymentId, refundData) {
    try {
      const accessToken = await this.getValidAccessToken()

      const { amount, reason = 'Customer request' } = refundData

      const refundPayload = {
        amount: parseFloat(amount),
        reason,
      }

      const response = await axios.post(
        `${this.baseURL}/payments/${paymentId}/refunds?account_id=${this.accountId}`,
        refundPayload,
        {
          headers: {
            Authorization: `Zoho-oauthtoken ${accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      )

      if (response.data.code !== 0) {
        throw new Error(`Zoho API Error: ${response.data.message}`)
      }

      return response.data.refund
    } catch (error) {
      console.error('Error creating refund:', error.response?.data || error.message)
      throw error
    }
  }

  /**
   * Update transaction status from webhook or manual check
   */
  async updateTransactionStatus(paymentSessionId, statusData) {
    try {
      await connectedDB()

      const transaction = await PaymentTransaction.findOne({ payments_session_id: paymentSessionId })

      if (!transaction) {
        throw new Error('Transaction not found')
      }

      await transaction.updateStatus(statusData.status, statusData)

      return transaction
    } catch (error) {
      console.error('Error updating transaction status:', error.message)
      throw error
    }
  }

  /**
   * Get transaction by payment session ID or payment link ID
   * Enhanced to support both payment sessions and payment links
   */
  async getTransaction(paymentSessionId) {
    try {
      await connectedDB()

      console.log('🔍 TRANSACTION LOOKUP: Searching for transaction with ID:', paymentSessionId)

      // First try direct lookup by payments_session_id
      let transaction = await PaymentTransaction.findOne({ payments_session_id: paymentSessionId })

      if (transaction) {
        console.log('✅ TRANSACTION FOUND: By payments_session_id')
        return transaction
      }

      // If not found and ID looks like a payment link session ID, try payment_link_id
      if (paymentSessionId.startsWith('payment_link_')) {
        const paymentLinkId = paymentSessionId.replace('payment_link_', '')
        console.log('🔗 PAYMENT LINK LOOKUP: Trying payment_link_id:', paymentLinkId)

        transaction = await PaymentTransaction.findOne({ payment_link_id: paymentLinkId })

        if (transaction) {
          console.log('✅ TRANSACTION FOUND: By payment_link_id')
          return transaction
        }
      }

      // If still not found, try searching by payment_link_id directly (in case webhook sends link ID)
      transaction = await PaymentTransaction.findOne({ payment_link_id: paymentSessionId })

      if (transaction) {
        console.log('✅ TRANSACTION FOUND: By direct payment_link_id match')
        return transaction
      }

      console.log('❌ TRANSACTION NOT FOUND: No match for ID:', paymentSessionId)
      return null
    } catch (error) {
      console.error('Error retrieving transaction:', error.message)
      throw error
    }
  }

  /**
   * Get transaction by payment link ID
   */
  async getTransactionByPaymentLinkId(paymentLinkId) {
    try {
      await connectedDB()

      const transaction = await PaymentTransaction.findOne({
        'meta_data.key': 'payment_link_id',
        'meta_data.value': paymentLinkId,
      })

      return transaction
    } catch (error) {
      console.error('Error getting transaction by payment link ID:', error.message)
      throw error
    }
  }

  /**
   * Update transaction status by payment link ID
   */
  async updateTransactionStatusByPaymentLinkId(paymentLinkId, statusData) {
    try {
      await connectedDB()

      const transaction = await PaymentTransaction.findOne({
        'meta_data.key': 'payment_link_id',
        'meta_data.value': paymentLinkId,
      })

      if (!transaction) {
        throw new Error(`Transaction not found for payment link: ${paymentLinkId}`)
      }

      // Update transaction using the existing method
      await transaction.updateStatus(statusData.status, statusData)

      return transaction
    } catch (error) {
      console.error('Error updating transaction status by payment link ID:', error.message)
      throw error
    }
  }

  /**
   * List transactions for a customer
   */
  async getCustomerTransactions(customerId, options = {}) {
    try {
      await connectedDB()

      const { page = 1, limit = 10, status, from_date, to_date } = options
      const skip = (page - 1) * limit

      const query = { customer_id: customerId }

      if (status) {
        query.status = status
      }

      // Add date range filtering
      if (from_date || to_date) {
        query.createdAt = {}
        if (from_date) {
          query.createdAt.$gte = new Date(from_date)
        }
        if (to_date) {
          query.createdAt.$lte = new Date(to_date + 'T23:59:59.999Z')
        }
      }

      const transactions = await PaymentTransaction.find(query).sort({ createdAt: -1 }).skip(skip).limit(limit)

      const total = await PaymentTransaction.countDocuments(query)

      return {
        transactions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      }
    } catch (error) {
      console.error('Error retrieving customer transactions:', error.message)
      throw error
    }
  }

  /**
   * Advanced search for transactions with complex filters
   */
  async advancedSearchTransactions(searchOptions = {}) {
    try {
      await connectedDB()

      const {
        customer_ids = [],
        statuses = [],
        amount_range = {},
        date_range = {},
        invoice_numbers = [],
        page = 1,
        limit = 10,
        sort_by = 'createdAt',
        sort_order = 'desc',
      } = searchOptions

      const skip = (page - 1) * limit
      const query = {}

      // Customer IDs filter
      if (customer_ids.length > 0) {
        query.customer_id = { $in: customer_ids }
      }

      // Status filter
      if (statuses.length > 0) {
        query.status = { $in: statuses }
      }

      // Amount range filter
      if (amount_range.min !== undefined || amount_range.max !== undefined) {
        query.amount = {}
        if (amount_range.min !== undefined) {
          query.amount.$gte = amount_range.min
        }
        if (amount_range.max !== undefined) {
          query.amount.$lte = amount_range.max
        }
      }

      // Date range filter
      if (date_range.from || date_range.to) {
        query.createdAt = {}
        if (date_range.from) {
          query.createdAt.$gte = new Date(date_range.from)
        }
        if (date_range.to) {
          query.createdAt.$lte = new Date(date_range.to + 'T23:59:59.999Z')
        }
      }

      // Invoice numbers filter
      if (invoice_numbers.length > 0) {
        query.invoice_number = { $in: invoice_numbers }
      }

      // Build sort object
      const sortObj = {}
      sortObj[sort_by] = sort_order === 'desc' ? -1 : 1

      const transactions = await PaymentTransaction.find(query).sort(sortObj).skip(skip).limit(limit)

      const total = await PaymentTransaction.countDocuments(query)

      return {
        transactions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
        search_criteria: searchOptions,
      }
    } catch (error) {
      console.error('Error in advanced search:', error.message)
      throw error
    }
  }

  /**
   * Create payment link using Zoho Payment Links API
   */
  async createPaymentLink(paymentLinkData) {
    try {
      const accessToken = await this.getValidAccessToken()

      const {
        amount,
        currency = 'INR',
        description,
        invoice_number,
        customer_id,
        customer_name,
        customer_email,
        customer_phone,
        redirect_url,
        reference_id,
        meta_data = [],
        expires_at,
        send_sms = false,
        send_email = false,
        partial_payments = false,
        minimum_partial_amount,
      } = paymentLinkData

      // Validate required fields per Zoho API documentation
      if (!amount || !description || !customer_email) {
        throw new Error('Missing required fields: amount, description, customer_email')
      }

      // Construct webhook URL for payment notifications
      const webhookUrl = `${process.env.NEXT_PUBLIC_DOMAIN}/api/zoho/webhooks/payment`

      // Prepare request payload according to Zoho API documentation
      const linkPayload = {
        amount: parseFloat(amount),
        currency,
        description,
        email: customer_email,
        phone: customer_phone,
        reference_id: reference_id || `LINK-${Date.now()}`,
        return_url: redirect_url,
        notify_user: send_email,
        webhook_url: webhookUrl,
      }

      // Add expires_at in yyyy-MM-dd format if provided
      if (expires_at) {
        const expiryDate = new Date(expires_at * 1000)
        linkPayload.expires_at = expiryDate.toISOString().split('T')[0]
      }

      // Call Zoho Payment Links API
      const response = await axios.post(`${this.baseURL}/paymentlinks?account_id=${this.accountId}`, linkPayload, {
        headers: {
          Authorization: `Zoho-oauthtoken ${accessToken}`,
          'Content-Type': 'application/json',
        },
      })

      if (response.data.code !== 0) {
        throw new Error(`Zoho API Error: ${response.data.message}`)
      }

      const paymentLink = response.data.payment_links

      // Save transaction to database
      await connectedDB()
      const transaction = new PaymentTransaction({
        payments_session_id: `link_${paymentLink.payment_link_id}`,
        amount: parseFloat(amount),
        currency,
        description,
        invoice_number: invoice_number || `LINK-${Date.now()}`,
        customer_id: customer_id || 'link-customer',
        customer_name,
        customer_email,
        customer_phone,
        status: 'created',
        meta_data: [
          { key: 'payment_type', value: 'payment_link' },
          { key: 'payment_link_id', value: paymentLink.payment_link_id },
          { key: 'payment_link_url', value: paymentLink.url },
          { key: 'customer_email', value: customer_email },
          { key: 'send_sms', value: send_sms.toString() },
          { key: 'send_email', value: send_email.toString() },
          { key: 'partial_payments', value: partial_payments.toString() },
          ...meta_data,
        ],
        redirect_url,
        reference_id: linkPayload.reference_id,
        session_expires_at: paymentLink.expires_at
          ? new Date(paymentLink.expires_at)
          : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      })

      await transaction.save()

      return {
        success: true,
        payment_link: paymentLink,
        transaction_id: transaction._id,
      }
    } catch (error) {
      console.error('Error creating payment link:', error.response?.data || error.message)
      throw error
    }
  }
}

const zohoPaymentService = new ZohoPaymentService()
export default zohoPaymentService
