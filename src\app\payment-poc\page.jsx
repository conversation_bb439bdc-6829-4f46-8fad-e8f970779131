'use client'

import { useCallback, useEffect, useState, Suspense } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'

function PaymentPOCPageContent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [paymentSession, setPaymentSession] = useState(null)
  const [paymentInstance, setPaymentInstance] = useState(null)
  
  const sessionId = searchParams.get('session_id')
  const invoiceNumber = searchParams.get('invoice')

  const fetchPaymentSession = useCallback(async () => {
    try {
      const response = await fetch(`/api/zoho/payments/status/${sessionId}/`)

      if (!response.ok) {
        throw new Error('Failed to fetch payment session')
      }

      const data = await response.json()

      if (!data.success) {
        throw new Error(data.message || 'Failed to load payment session')
      }

      setPaymentSession(data.data)

      // Initialize Zoho Payment SDK (POC approach)
      await initializeZohoPaymentPOC(data.data)
    } catch (err) {
      console.error('Payment session fetch error:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }, [sessionId, initializeZohoPaymentPOC, setPaymentSession, setError, setLoading])

  const initializeZohoPaymentPOC = useCallback(async (sessionData) => {
    try {
      // Load ZPayments SDK (POC approach)
      await loadZPaymentsSDK()

      // Check if ZPayments is loaded
      if (typeof window.ZPayments === 'undefined') {
        throw new Error('Zoho Payment SDK not loaded')
      }

      // POC-style configuration
      let config = {
        account_id: process.env.NEXT_PUBLIC_ZOHO_PAY_ACCOUNT_ID || sessionData.account_id,
        domain: 'IN',
        otherOptions: {
          api_key: process.env.NEXT_PUBLIC_ZOHO_PAY_API_KEY,
        },
      }

      // Create instance using POC approach
      const instance = new window.ZPayments(config)
      setPaymentInstance(instance)

      // POC-style payment options
      let options = {
        amount: sessionData.amount?.toString() || '0.0',
        currency_code: sessionData.currency || 'INR',
        payments_session_id: sessionData.payment_session_id,
        currency_symbol: sessionData.currency === 'INR' ? '₹' : sessionData.currency,
        business: 'AquaPartner',
        description: sessionData.description || 'Payment for invoice',
        address: {
          name: sessionData.customer_name || 'Customer',
          email: sessionData.customer_email || '',
        },
      }

      console.log('Initiating payment with POC approach:', options)

      try {
        // Use POC approach with requestPaymentMethod
        let data = await instance.requestPaymentMethod(options)
        console.log('Payment successful:', JSON.stringify(data))

        // Handle successful payment
        router.push(`/payment-success?payment_id=${data.payment_id || 'unknown'}&invoice=${invoiceNumber}`)

      } catch (error) {
        console.error('Payment error:', JSON.stringify(error))

        // POC-style error handling
        if (error.code !== 'widget_closed') {
          console.error('Payment failed:', error)
          router.push(`/payment-failed?error=${encodeURIComponent(error.message || 'Payment failed')}&invoice=${invoiceNumber}`)
        } else {
          console.log('Payment widget closed by user')
          router.push(`/payment-cancel?invoice=${invoiceNumber}`)
        }
      } finally {
        // POC-style cleanup
        if (instance) {
          try {
            await instance.close()
            console.log('Payment instance closed')
          } catch (closeError) {
            console.warn('Error closing payment instance:', closeError)
          }
        }
      }

    } catch (error) {
      console.error('Payment initialization error:', error)
      setError(error.message || 'Failed to initialize payment system')
    }
  }, [router, invoiceNumber, setPaymentInstance, setError])

  const loadZPaymentsSDK = () => {
    return new Promise((resolve, reject) => {
      // Check if already loaded
      if (window.ZPayments) {
        resolve()
        return
      }

      // Create script element to load ZPayments SDK (POC URL)
      const script = document.createElement('script')
      script.src = 'https://static.zohocdn.com/zpay/zpay-js/v1/zpayments.js'
      script.onload = () => {
        console.log('ZPayments SDK loaded successfully')
        resolve()
      }
      script.onerror = () => {
        reject(new Error('Failed to load ZPayments SDK'))
      }

      document.head.appendChild(script)
    })
  }

  useEffect(() => {
    if (!sessionId) {
      setError('Invalid payment session')
      setLoading(false)
      return
    }

    // Fetch payment session details
    fetchPaymentSession()

    // Cleanup on unmount
    return () => {
      if (paymentInstance) {
        paymentInstance.close().catch(console.warn)
      }
    }
  }, [sessionId, fetchPaymentSession, paymentInstance])

  const handleBackToInvoices = () => {
    router.push('/aquapartner/billingAndPayments')
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading payment (POC approach)...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
              <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="mt-4 text-lg font-medium text-gray-900">Payment Error (POC)</h3>
            <p className="mt-2 text-sm text-gray-500">{error}</p>
            <div className="mt-6">
              <button
                onClick={handleBackToInvoices}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Back to Invoices
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow-lg rounded-lg overflow-hidden">
          {/* Header */}
          <div className="bg-blue-600 px-6 py-4">
            <h1 className="text-xl font-semibold text-white">Complete Payment (POC Approach)</h1>
            {invoiceNumber && (
              <p className="text-blue-100 text-sm mt-1">Invoice: {invoiceNumber}</p>
            )}
          </div>
          
          {/* Payment Details */}
          {paymentSession && (
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">Amount:</span>
                  <span className="ml-2 font-medium">₹{paymentSession.amount}</span>
                </div>
                <div>
                  <span className="text-gray-500">Currency:</span>
                  <span className="ml-2 font-medium">{paymentSession.currency}</span>
                </div>
                <div className="col-span-2">
                  <span className="text-gray-500">Description:</span>
                  <span className="ml-2 font-medium">{paymentSession.description}</span>
                </div>
              </div>
            </div>
          )}
          
          {/* Payment Processing Message */}
          <div className="px-6 py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-4 text-gray-600">Processing payment using POC approach...</p>
              <p className="mt-2 text-sm text-gray-500">Using ZPayments SDK with requestPaymentMethod()</p>
            </div>
          </div>
          
          {/* Footer */}
          <div className="bg-gray-50 px-6 py-4">
            <div className="flex justify-between items-center">
              <button
                onClick={handleBackToInvoices}
                className="text-sm text-gray-600 hover:text-gray-800"
              >
                ← Back to Invoices
              </button>
              <div className="text-xs text-gray-500">
                POC approach with ZPayments SDK
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function PaymentPOCPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading payment...</p>
        </div>
      </div>
    }>
      <PaymentPOCPageContent />
    </Suspense>
  )
}
