import { NextRequest, NextResponse } from 'next/server'
import zohoPaymentService from '@/app/lib/zohoPaymentService'

export async function GET(
  request: NextRequest,
  { params }: { params: { sessionId: string } }
) {
  try {
    const { sessionId } = params

    if (!sessionId) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Missing session ID',
          message: 'Payment session ID is required'
        },
        { status: 400 }
      )
    }

    console.log(`[FLUTTER] Checking payment status for session: ${sessionId}`)

    // Get transaction from database
    const transaction = await zohoPaymentService.getTransaction(sessionId)
    
    if (!transaction) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Transaction not found',
          message: 'No transaction found for the provided session ID'
        },
        { status: 404 }
      )
    }

    // Get latest payment session details from Zoho
    let paymentSession = null
    try {
      paymentSession = await zohoPaymentService.getPaymentSession(sessionId)
    } catch (error) {
      console.warn('Could not fetch payment session from Zoho:', error)
      // Continue with database data only
    }

    // Determine current status
    let currentStatus = transaction.status
    let paymentDetails = null

    // If we have payment session data, check for updates
    if (paymentSession) {
      if (paymentSession.payment_id && transaction.status === 'created') {
        // Payment has been made, get payment details
        try {
          paymentDetails = await zohoPaymentService.getPayment(paymentSession.payment_id)
          currentStatus = paymentDetails.status || 'completed'
          
          // Update transaction status in database
          await zohoPaymentService.updateTransactionStatus(sessionId, {
            status: currentStatus,
            payment_id: paymentSession.payment_id,
            payment_details: paymentDetails
          })
        } catch (error) {
          console.warn('Could not fetch payment details:', error)
        }
      }
    }

    // Check if session has expired
    const now = new Date()
    const expiresAt = transaction.session_expires_at || new Date(transaction.createdAt.getTime() + 15 * 60 * 1000)
    const isExpired = now > expiresAt

    return NextResponse.json({
      success: true,
      payment_session: {
        id: sessionId,
        status: currentStatus,
        amount: transaction.amount,
        currency: transaction.currency,
        description: transaction.description,
        invoice_number: transaction.invoice_number,
        customer_id: transaction.customer_id,
        created_at: transaction.createdAt,
        expires_at: expiresAt,
        is_expired: isExpired,
        payment_id: paymentSession?.payment_id || transaction.payment_id,
        last_updated: transaction.updatedAt
      },
      payment_details: paymentDetails,
      transaction_id: transaction._id,
      // Flutter-specific information
      flutter_status: {
        can_retry: isExpired || currentStatus === 'failed',
        should_close_webview: ['completed', 'success', 'failed', 'cancelled'].includes(currentStatus),
        next_action: getNextAction(currentStatus, isExpired)
      }
    })

  } catch (error: any) {
    console.error('Error checking payment status:', error)
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Status check failed',
        message: error.message,
        code: 'STATUS_CHECK_ERROR'
      },
      { status: 500 }
    )
  }
}

function getNextAction(status: string, isExpired: boolean): string {
  if (isExpired) return 'retry_payment'
  
  switch (status) {
    case 'created':
    case 'pending':
      return 'wait_for_completion'
    case 'completed':
    case 'success':
      return 'close_webview_success'
    case 'failed':
      return 'close_webview_failure'
    case 'cancelled':
      return 'close_webview_cancelled'
    default:
      return 'wait_for_completion'
  }
}
