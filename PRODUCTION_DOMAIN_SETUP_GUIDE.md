# Production Domain Configuration Setup Guide

## Issue Identified

The production health check shows `"domain": "not configured"` because the `NEXT_PUBLIC_DOMAIN` environment variable is not set in the Azure Static Web Apps deployment.

## Root Cause

The GitHub workflow file (`.github/workflows/azure-static-web-apps-yellow-sky-08e56d200.yml`) is configured to use `${{ secrets.NEXT_PUBLIC_DOMAIN }}`, but this secret is not set in the GitHub repository.

## Solution: Set GitHub Repository Secret

### Step 1: Access GitHub Repository Settings

1. Go to your GitHub repository: `https://github.com/YOUR_USERNAME/aquapartner-ts`
2. Click on **Settings** tab
3. In the left sidebar, click on **Secrets and variables** → **Actions**

### Step 2: Add the NEXT_PUBLIC_DOMAIN Secret

1. Click **New repository secret**
2. Set the following:
   - **Name**: `NEXT_PUBLIC_DOMAIN`
   - **Secret**: `https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net`
3. Click **Add secret**

### Step 3: Verify Other Required Secrets

Ensure these secrets are also configured (they should already be set based on the test results):

- `NEXT_PUBLIC_API_DOMAIN`: `/api`
- `MONGODB_URI`: Your MongoDB connection string
- `ZOHO_PAYMENT_SESSION_URL`: `https://payments.zoho.in/api/v1/paymentsessions`
- `ZOHO_PAY_ACCOUNT_ID`: Your Zoho account ID
- `ZOHO_PAY_API_KEY`: Your Zoho API key
- `ZOHO_WEBHOOK_SECRET`: Your webhook secret
- `AZURE_STATIC_WEB_APPS_API_TOKEN_YELLOW_SKY_08E56D200`: Azure deployment token

### Step 4: Trigger a New Deployment

After adding the secret, trigger a new deployment by either:

1. **Push a commit** to the main branch, or
2. **Manually trigger** the workflow from GitHub Actions tab

### Step 5: Verify the Fix

After deployment completes (usually 2-3 minutes), test the production health check:

```bash
curl https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api/zoho/health
```

The response should show:

```json
{
  "configuration": {
    "account_id": "configured",
    "webhook_secret": "configured",
    "domain": "https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net"
  }
}
```

## Alternative: Quick Test Command

You can run this command to test the production health check:

```bash
node -e "
fetch('https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api/zoho/health')
  .then(r => r.json())
  .then(d => {
    console.log('Status:', d.status);
    console.log('Domain:', d.configuration?.domain);
    console.log('All checks healthy:', Object.values(d.checks).every(c => c.status === 'healthy'));
  })
  .catch(e => console.log('Error:', e.message))
"
```

## Expected Results After Fix

Once the `NEXT_PUBLIC_DOMAIN` secret is set and deployed:

✅ **Production Health Check**: Should show "healthy" status with domain configured
✅ **Payment Session Creation**: Should work properly
✅ **Payment Status Retrieval**: Should work properly
✅ **Webhook Configuration**: Should show correct webhook URL with domain

## Webhook Configuration for Zoho

After the domain is configured, update your Zoho Payment webhook settings to:

- **Webhook URL**: `https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api/zoho/webhooks/payment`
- **Content Type**: `application/json`
- **Events**: Enable all payment events (success, failed, pending, cancelled, expired)

## Testing After Configuration

Run the production test suite again:

```bash
node tests/production-payment-test.js
```

Expected result: All 10 tests should pass (100% success rate).

## Security Notes

- The `NEXT_PUBLIC_DOMAIN` variable is safe to be public as it's used for client-side configuration
- Keep other secrets (API keys, database URIs, webhook secrets) secure and never commit them to code
- Regularly rotate webhook secrets and API keys for security

## Troubleshooting

If the domain still shows "not configured" after deployment:

1. Check that the secret name is exactly `NEXT_PUBLIC_DOMAIN` (case-sensitive)
2. Verify the secret value is exactly `https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net`
3. Ensure the deployment completed successfully in GitHub Actions
4. Check Azure Static Web Apps logs for any deployment errors
