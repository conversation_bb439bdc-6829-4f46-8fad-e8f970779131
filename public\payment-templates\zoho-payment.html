<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Processing</title>
    <script src="https://static.zohocdn.com/zpay/zpay-js/v1/zpayments.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .payment-container {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 30px;
            width: 90%;
            max-width: 450px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        .payment-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        .loader {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .status-message {
            margin: 20px 0;
            font-size: 16px;
            font-weight: 500;
            color: #555;
        }
        .error-message {
            color: #e74c3c;
            margin: 15px 0;
            padding: 10px;
            background-color: #fdf2f2;
            border-radius: 6px;
            border-left: 4px solid #e74c3c;
        }
        .success-message {
            color: #27ae60;
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f9f4;
            border-radius: 6px;
            border-left: 4px solid #27ae60;
        }
        .payment-details {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border: 1px solid #e9ecef;
        }
        .amount-display {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin: 10px 0;
        }
        .debug-info {
            font-size: 12px;
            color: #666;
            margin-top: 20px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            text-align: left;
        }
        .retry-button {
            background-color: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-top: 15px;
            font-size: 14px;
        }
        .retry-button:hover {
            background-color: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="payment-container">
        <h2>Payment Processing</h2>
        
        <div class="payment-details">
            <div>Amount: <span class="amount-display">{{CURRENCY}} {{AMOUNT}}</span></div>
            <div style="font-size: 14px; color: #666; margin-top: 5px;">{{DESCRIPTION}}</div>
        </div>
        
        <div class="loader" id="loader"></div>
        <div class="status-message" id="statusMessage">Initializing payment...</div>
        <div class="error-message" id="errorMessage" style="display: none;"></div>
        <div class="success-message" id="successMessage" style="display: none;"></div>
        
        <button class="retry-button" id="retryButton" style="display: none;" onclick="retryPayment()">
            Retry Payment
        </button>
        
        <div class="debug-info" id="debugInfo" style="display: none;">
            <strong>Debug Information:</strong><br>
            Session ID: {{SESSION_ID}}<br>
            Callback URL: {{CALLBACK_URL}}<br>
            API Domain: {{API_DOMAIN}}
        </div>
    </div>

    <script>
        // Configuration from template variables
        const CONFIG = {
            sessionId: '{{SESSION_ID}}',
            amount: '{{AMOUNT}}',
            currency: '{{CURRENCY}}',
            description: '{{DESCRIPTION}}',
            callbackUrl: '{{CALLBACK_URL}}',
            apiDomain: '{{API_DOMAIN}}',
            debugMode: false // Set to true for debugging
        };

        // Show debug info if in debug mode
        if (CONFIG.debugMode) {
            document.getElementById('debugInfo').style.display = 'block';
        }

        // Enhanced Flutter communication with error handling
        function sendToFlutterApp(status, data) {
            const message = {
                status: status,
                data: data,
                timestamp: new Date().toISOString(),
                sessionId: CONFIG.sessionId
            };

            console.log('Sending to Flutter:', message);

            try {
                // For flutter_inappwebview (most common)
                if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
                    window.flutter_inappwebview.callHandler('paymentCallback', message);
                    return true;
                }

                // For iOS WKWebView
                if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.flutterPaymentCallback) {
                    window.webkit.messageHandlers.flutterPaymentCallback.postMessage(message);
                    return true;
                }

                // For Android WebView
                if (window.flutterPaymentCallback && typeof window.flutterPaymentCallback.postMessage === 'function') {
                    window.flutterPaymentCallback.postMessage(JSON.stringify(message));
                    return true;
                }

                // Fallback: try postMessage to parent window
                if (window.parent && window.parent !== window) {
                    window.parent.postMessage(message, '*');
                    return true;
                }

                console.warn('No Flutter communication channel found');
                return false;
            } catch (error) {
                console.error('Error sending message to Flutter:', error);
                return false;
            }
        }

        // Enhanced error handling
        function showError(message, details = null) {
            document.getElementById('statusMessage').style.display = 'none';
            document.getElementById('loader').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
            
            const errorElement = document.getElementById('errorMessage');
            errorElement.textContent = message;
            errorElement.style.display = 'block';
            
            document.getElementById('retryButton').style.display = 'inline-block';
            
            if (details) {
                console.error('Payment Error Details:', details);
            }
        }

        function showSuccess(message) {
            document.getElementById('statusMessage').style.display = 'none';
            document.getElementById('loader').style.display = 'none';
            document.getElementById('errorMessage').style.display = 'none';
            
            const successElement = document.getElementById('successMessage');
            successElement.textContent = message;
            successElement.style.display = 'block';
        }

        function updateStatus(message) {
            document.getElementById('statusMessage').textContent = message;
        }

        // Retry payment function
        function retryPayment() {
            document.getElementById('retryButton').style.display = 'none';
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('loader').style.display = 'block';
            document.getElementById('statusMessage').style.display = 'block';

            updateStatus('Retrying payment...');
            initializePayment();
        }

        // Global instance variable to manage SDK instance
        let paymentInstance = null;

        // Main payment initialization using OFFICIAL Zoho Payments SDK
        async function initializePayment() {
            if (!CONFIG.sessionId) {
                showError('Missing payment session ID');
                sendToFlutterApp('error', {
                    message: 'Missing payment session ID',
                    code: 'MISSING_SESSION_ID'
                });
                return;
            }

            updateStatus(`Processing payment of ${CONFIG.currency} ${CONFIG.amount}...`);

            try {
                // Check if ZPayments is loaded (Official SDK)
                if (typeof window.ZPayments === 'undefined') {
                    throw new Error('Zoho Payment SDK not loaded');
                }

                // Create instance using official SDK
                let config = {
                    account_id: "{{ACCOUNT_ID}}", // Will be replaced by template
                    domain: "IN",
                    otherOptions: {
                        api_key: "{{API_KEY}}" // Will be replaced by template
                    }
                };

                // Store instance globally for cleanup
                paymentInstance = new window.ZPayments(config);

                // Prepare payment options
                let options = {
                    amount: CONFIG.amount,
                    currency_code: CONFIG.currency,
                    payments_session_id: CONFIG.sessionId,
                    currency_symbol: CONFIG.currency === 'INR' ? '₹' : CONFIG.currency,
                    business: "{{BUSINESS_NAME}}", // Will be replaced by template
                    description: CONFIG.description,
                    invoice_number: "{{INVOICE_NUMBER}}", // Will be replaced by template
                    reference_number: "{{REFERENCE_NUMBER}}", // Will be replaced by template
                    address: {
                        name: "{{CUSTOMER_NAME}}", // Will be replaced by template
                        email: "{{CUSTOMER_EMAIL}}", // Will be replaced by template
                        phone: "{{CUSTOMER_PHONE}}" // Will be replaced by template
                    }
                };

                // Initiate payment using official SDK
                let data = await paymentInstance.requestPaymentMethod(options);

                // Handle success
                console.log('Payment Success:', data);
                showSuccess('Payment completed successfully!');

                sendToFlutterApp('success', {
                    payment_id: data.payment_id,
                    message: data.message || 'Payment successful!',
                    amount: CONFIG.amount,
                    currency: CONFIG.currency,
                    sessionId: CONFIG.sessionId
                });

                // Close the instance
                if (paymentInstance) {
                    await paymentInstance.close();
                    paymentInstance = null;
                }

            } catch (error) {
                console.error('Payment error:', error);

                // Handle different error types
                if (error.code === 'widget_closed') {
                    showError('Payment was cancelled');
                    sendToFlutterApp('cancelled', {
                        message: 'Payment cancelled by user',
                        code: error.code,
                        sessionId: CONFIG.sessionId
                    });
                } else {
                    showError('Payment failed: ' + (error.message || 'Unknown error'));
                    sendToFlutterApp('failure', {
                        message: error.message || 'Payment failed',
                        code: error.code || 'PAYMENT_ERROR',
                        sessionId: CONFIG.sessionId
                    });
                }
            }
        }

        // Initialize payment when page loads
        window.onload = function() {
            // Add a small delay to ensure everything is loaded
            setTimeout(initializePayment, 500);
        };

        // Handle page visibility changes (useful for mobile)
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                console.log('Page became visible');
            }
        });

        // Cleanup function for page unload
        window.addEventListener('beforeunload', function() {
            if (paymentInstance) {
                try {
                    paymentInstance.close();
                } catch (error) {
                    console.warn('Error closing payment instance:', error);
                }
                paymentInstance = null;
            }
        });

        // Global error handler
        window.onerror = function(msg, url, lineNo, columnNo, error) {
            console.error('Global error:', { msg, url, lineNo, columnNo, error });
            sendToFlutterApp('error', {
                message: 'Unexpected error occurred',
                code: 'GLOBAL_ERROR',
                details: msg
            });
        };
    </script>
</body>
</html>
