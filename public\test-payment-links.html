<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Payment Links API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .result { margin-top: 20px; padding: 10px; border: 1px solid #ccc; background: #f9f9f9; }
        .error { border-color: #f00; background: #ffe6e6; }
        .success { border-color: #0a0; background: #e6ffe6; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Zoho Payment Links API</h1>
        
        <h2>Test Payment Creation</h2>
        <button onclick="testPaymentLinks()">Test Payment Links</button>
        <button onclick="testPaymentSession()">Test Payment Session</button>
        <button onclick="testAutoPayment()">Test Auto Payment</button>
        
        <div id="result" class="result" style="display: none;">
            <h3>Result:</h3>
            <pre id="resultText"></pre>
        </div>
    </div>

    <script>
        async function testPaymentLinks() {
            showResult('Testing Payment Links...', 'info');
            
            try {
                const response = await fetch('/api/zoho/payments/create-payment', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Force-Payment-Method': 'links'
                    },
                    body: JSON.stringify({
                        amount: 100,
                        currency: 'INR',
                        description: 'Test Payment Link',
                        customer_email: '<EMAIL>',
                        customer_id: 'test123',
                        customer_name: 'Test Customer',
                        invoice_number: 'TEST-001',
                        reference_id: 'test_' + Date.now()
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    showResult('Payment Link Created Successfully:\n' + JSON.stringify(data, null, 2), 'success');
                    
                    if (data.data && data.data.payment_link_url) {
                        const openLink = confirm('Payment link created! Do you want to open it?');
                        if (openLink) {
                            window.open(data.data.payment_link_url, '_blank');
                        }
                    }
                } else {
                    showResult('Error: ' + JSON.stringify(data, null, 2), 'error');
                }
            } catch (error) {
                showResult('Network Error: ' + error.message, 'error');
            }
        }

        async function testPaymentSession() {
            showResult('Testing Payment Session...', 'info');
            
            try {
                const response = await fetch('/api/zoho/payments/create-payment', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Force-Payment-Method': 'widget'
                    },
                    body: JSON.stringify({
                        amount: 100,
                        currency: 'INR',
                        description: 'Test Payment Session',
                        customer_email: '<EMAIL>',
                        customer_id: 'test123',
                        customer_name: 'Test Customer',
                        invoice_number: 'TEST-002',
                        reference_id: 'test_' + Date.now()
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    showResult('Payment Session Created Successfully:\n' + JSON.stringify(data, null, 2), 'success');
                } else {
                    showResult('Error: ' + JSON.stringify(data, null, 2), 'error');
                }
            } catch (error) {
                showResult('Network Error: ' + error.message, 'error');
            }
        }

        async function testAutoPayment() {
            showResult('Testing Auto Payment Selection...', 'info');
            
            try {
                const response = await fetch('/api/zoho/payments/create-payment', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        amount: 100,
                        currency: 'INR',
                        description: 'Test Auto Payment',
                        customer_email: '<EMAIL>',
                        customer_id: 'test123',
                        customer_name: 'Test Customer',
                        invoice_number: 'TEST-003',
                        reference_id: 'test_' + Date.now()
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    showResult('Auto Payment Created Successfully:\n' + JSON.stringify(data, null, 2), 'success');
                    
                    if (data.payment_method === 'payment_link' && data.data && data.data.payment_link_url) {
                        const openLink = confirm('Payment link created! Do you want to open it?');
                        if (openLink) {
                            window.open(data.data.payment_link_url, '_blank');
                        }
                    }
                } else {
                    showResult('Error: ' + JSON.stringify(data, null, 2), 'error');
                }
            } catch (error) {
                showResult('Network Error: ' + error.message, 'error');
            }
        }

        function showResult(text, type) {
            const resultDiv = document.getElementById('result');
            const resultText = document.getElementById('resultText');
            
            resultText.textContent = text;
            resultDiv.className = 'result ' + type;
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>
