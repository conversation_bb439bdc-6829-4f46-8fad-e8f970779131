#!/usr/bin/env node

/**
 * Azure Deployment Verification Script
 *
 * This script verifies that your Azure Static Web Apps deployment
 * has all the required environment variables configured correctly.
 */

const https = require('https')

// Configuration
const AZURE_DOMAIN = 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net'
const HEALTH_ENDPOINT = '/api/zoho/health'

// Required environment variables that should be present
const REQUIRED_ENV_VARS = ['ZOHO_PAY_ACCOUNT_ID', 'MONGODB_URI']

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
}

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`)
}

function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const request = https.get(url, (response) => {
      let data = ''

      response.on('data', (chunk) => {
        data += chunk
      })

      response.on('end', () => {
        try {
          const jsonData = JSON.parse(data)
          resolve({
            statusCode: response.statusCode,
            data: jsonData,
          })
        } catch (error) {
          reject(new Error(`Failed to parse JSON response: ${error.message}`))
        }
      })
    })

    request.on('error', (error) => {
      reject(error)
    })

    request.setTimeout(10000, () => {
      request.destroy()
      reject(new Error('Request timeout'))
    })
  })
}

async function verifyHealthEndpoint() {
  log(`${colors.bold}🔍 Verifying Azure Deployment Health...${colors.reset}\n`)

  const healthUrl = `${AZURE_DOMAIN}${HEALTH_ENDPOINT}`
  log(`Testing endpoint: ${healthUrl}`, colors.blue)

  try {
    const response = await makeRequest(healthUrl)

    log(`\n📊 Response Status: ${response.statusCode}`, response.statusCode === 200 ? colors.green : colors.red)

    const healthData = response.data

    // Check overall status
    log(`\n🏥 Overall Health Status: ${healthData.status}`, healthData.status === 'healthy' ? colors.green : colors.red)

    // Check environment variables
    if (healthData.checks && healthData.checks.environment) {
      const envCheck = healthData.checks.environment
      log(`\n🔧 Environment Variables Check:`, colors.bold)
      log(`   Status: ${envCheck.status}`, envCheck.status === 'healthy' ? colors.green : colors.red)
      log(`   Message: ${envCheck.message}`)

      if (envCheck.missing_variables && envCheck.missing_variables.length > 0) {
        log(`   ❌ Missing Variables: ${envCheck.missing_variables.join(', ')}`, colors.red)
      } else {
        log(`   ✅ All required variables present`, colors.green)
      }
    }

    // Check database connectivity
    if (healthData.checks && healthData.checks.database) {
      const dbCheck = healthData.checks.database
      log(`\n💾 Database Check:`, colors.bold)
      log(`   Status: ${dbCheck.status}`, dbCheck.status === 'healthy' ? colors.green : colors.red)
      log(`   Message: ${dbCheck.message}`)
    }

    // Check Zoho authentication
    if (healthData.checks && healthData.checks.zoho_auth) {
      const zohoCheck = healthData.checks.zoho_auth
      log(`\n🔐 Zoho Authentication Check:`, colors.bold)
      log(`   Status: ${zohoCheck.status}`, zohoCheck.status === 'healthy' ? colors.green : colors.red)
      log(`   Message: ${zohoCheck.message}`)
    }

    // Check configuration
    if (healthData.configuration) {
      const config = healthData.configuration
      log(`\n⚙️  Configuration:`, colors.bold)
      log(`   Account ID: ${config.account_id}`, config.account_id === 'configured' ? colors.green : colors.red)
      log(`   Webhook Secret: ${config.webhook_secret}`)
      log(`   Domain: ${config.domain}`)
    }

    // Summary
    log(`\n📋 Summary:`, colors.bold)
    if (healthData.status === 'healthy') {
      log(`✅ Deployment is healthy and ready for use!`, colors.green)
    } else {
      log(`❌ Deployment has issues that need to be resolved.`, colors.red)
      log(`\n🔧 Next Steps:`, colors.yellow)
      log(`1. Check GitHub Secrets configuration`)
      log(`2. Verify all required secrets are added`)
      log(`3. Redeploy if necessary`)
      log(`4. Check GitHub Actions logs for errors`)
    }
  } catch (error) {
    log(`\n❌ Error testing health endpoint:`, colors.red)
    log(`   ${error.message}`)

    log(`\n🔧 Troubleshooting Steps:`, colors.yellow)
    log(`1. Verify the Azure Static Web App is deployed`)
    log(`2. Check if the API routes are properly configured`)
    log(`3. Ensure the domain URL is correct`)
    log(`4. Check GitHub Actions deployment logs`)
  }
}

async function verifyEnvironmentVariables() {
  log(`\n🔍 Checking specific environment variables...`, colors.bold)

  try {
    const response = await makeRequest(`${AZURE_DOMAIN}${HEALTH_ENDPOINT}`)
    const healthData = response.data

    if (healthData.checks && healthData.checks.environment) {
      const envCheck = healthData.checks.environment

      REQUIRED_ENV_VARS.forEach((varName) => {
        const isPresent = !envCheck.missing_variables || !envCheck.missing_variables.includes(varName)
        log(`   ${varName}: ${isPresent ? '✅ Present' : '❌ Missing'}`, isPresent ? colors.green : colors.red)
      })
    }
  } catch (error) {
    log(`❌ Could not verify environment variables: ${error.message}`, colors.red)
  }
}

// Main execution
async function main() {
  log(`${colors.bold}🚀 Azure Static Web Apps Deployment Verification${colors.reset}`)
  log(`${colors.bold}=================================================${colors.reset}\n`)

  await verifyHealthEndpoint()
  await verifyEnvironmentVariables()

  log(`\n${colors.bold}📚 For more information, see:${colors.reset}`)
  log(`   - AZURE_DEPLOYMENT_ENVIRONMENT_SETUP.md`)
  log(`   - scripts/setup-github-secrets.md`)
  log(`\n${colors.bold}🔗 Useful Links:${colors.reset}`)
  log(`   - Health Endpoint: ${AZURE_DOMAIN}${HEALTH_ENDPOINT}`)
  log(`   - Azure Portal: https://portal.azure.com`)
  log(`   - GitHub Actions: https://github.com/your-username/aquapartner-ts/actions`)
}

// Run the verification
main().catch((error) => {
  log(`\n💥 Unexpected error: ${error.message}`, colors.red)
  process.exit(1)
})
